package handlers

import (
	"fmt"
	"net/http"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NovoLinkManagementHandler gerencia a nova página de gerenciamento de vínculos
type NovoLinkManagementHandler struct {
	db          *gorm.DB
	menuService *services.MenuService
}

// NewNovoLinkManagementHandler cria uma nova instância do handler
func NewNovoLinkManagementHandler(db *gorm.DB, menuService *services.MenuService) *NovoLinkManagementHandler {
	return &NovoLinkManagementHandler{db: db, menuService: menuService}
}

// ShowNovoLinkManagementPage renderiza a página de gerenciamento de vínculos
func (h *NovoLinkManagementHandler) ShowNovoLinkManagementPage(c *gin.Context) {
	// Recuperar usuário do contexto
	userValue, exists := c.Get("user")
	var user *models.User
	if exists {
		if userObj, ok := userValue.(*models.User); ok {
			user = userObj
		}
	}
	if user == nil {
		userID, _ := c.Get("userID")
		userRole, _ := c.Get("userRole")
		userName, _ := c.Get("userName")
		if userID != nil && userRole != nil {
			user = &models.User{
				ID:   uint(userID.(int)),
				Role: models.UserRole(userRole.(string)),
				Name: userName.(string),
			}
		}
	}
	menu := h.menuService.GetMenuForPage(user, "admin-link-management")
	c.HTML(http.StatusOK, "admin/novo_link_management.html", gin.H{
		"title":      "Gerenciamento de Vínculos",
		"ActivePage": "admin-link-management",
		"User":       user,
		"UserMenu":   menu,
	})
}

// GetEquipmentTypes retorna todos os tipos de equipamentos
func (h *NovoLinkManagementHandler) GetEquipmentTypes(c *gin.Context) {
	var equipmentTypes []models.EquipmentType
	if err := h.db.Order("name").Find(&equipmentTypes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar tipos de equipamentos: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    equipmentTypes,
	})
}

// GetActiveTechnicians retorna técnicos ativos
func (h *NovoLinkManagementHandler) GetActiveTechnicians(c *gin.Context) {
	var technicians []models.User
	if err := h.db.Where("role = ? AND is_active = ?", "tecnico", true).
		Order("name").Find(&technicians).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar técnicos: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    technicians,
	})
}

// GetTechnicianMaintenanceHistory retorna o histórico de manutenções de um técnico
func (h *NovoLinkManagementHandler) GetTechnicianMaintenanceHistory(c *gin.Context) {
	technicianID := c.Param("id")

	var history []models.TechnicianMaintenanceHistory
	if err := h.db.Where("technician_id = ?", technicianID).
		Order("maintenance_date DESC").
		Find(&history).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar histórico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// GetTechnicianBranchLinks retorna os vínculos técnico-filial
func (h *NovoLinkManagementHandler) GetTechnicianBranchLinks(c *gin.Context) {
	var links []models.TechnicianBranch
	if err := h.db.Preload("Technician").
		Preload("Branch").
		Preload("EquipmentType").
		Order("created_at DESC").
		Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar vínculos: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    links,
	})
}

// LinkTechnicianToBranch vincula um técnico a uma filial
func (h *NovoLinkManagementHandler) LinkTechnicianToBranch(c *gin.Context) {
	var request struct {
		TechnicianID    uint `json:"technician_id" binding:"required"`
		BranchID        uint `json:"branch_id" binding:"required"`
		EquipmentTypeID uint `json:"equipment_type_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}

	// Verificar se o técnico tem especialidade no tipo de equipamento
	var specialties []models.TechnicianSpecialty
	if err := h.db.Where("technician_id = ?", request.TechnicianID).Find(&specialties).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Erro ao buscar especialidades do técnico",
		})
		return
	}
	found := false
	for _, spec := range specialties {
		for _, eqType := range spec.EquipmentTypes {
			if eqType == fmt.Sprintf("%d", request.EquipmentTypeID) {
				found = true
				break
			}
		}
		if found {
			break
		}
	}
	if !found {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Técnico não possui especialidade neste tipo de equipamento",
		})
		return
	}

	// Verificar se o vínculo já existe
	var existing models.TechnicianBranch
	if err := h.db.Where("technician_id = ? AND branch_id = ? AND equipment_type_id = ?",
		request.TechnicianID, request.BranchID, request.EquipmentTypeID).First(&existing).Error; err == nil {
		// Vínculo existe, apenas ativar
		existing.IsActive = true
		if err := h.db.Save(&existing).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao ativar vínculo: " + err.Error(),
			})
			return
		}
	} else {
		// Criar novo vínculo
		newLink := models.TechnicianBranch{
			TechnicianID: int(request.TechnicianID),
			BranchID:     int(request.BranchID),
			IsActive:     true,
		}
		if err := h.db.Create(&newLink).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao criar vínculo: " + err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico vinculado à filial com sucesso",
	})
}

// UnlinkTechnicianFromBranch desvincula um técnico de uma filial
func (h *NovoLinkManagementHandler) UnlinkTechnicianFromBranch(c *gin.Context) {
	technicianID := c.Param("technician_id")
	branchID := c.Param("branch_id")
	equipmentTypeID := c.Param("equipment_type_id")

	if err := h.db.Model(&models.TechnicianBranch{}).
		Where("technician_id = ? AND branch_id = ? AND equipment_type_id = ?",
			technicianID, branchID, equipmentTypeID).
		Update("is_active", false).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao desvincular técnico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico desvinculado da filial com sucesso",
	})
}

// --- VÍNCULOS PRESTADORA x TÉCNICO ---

// Listar vínculos prestadora x técnico
func (h *NovoLinkManagementHandler) GetProviderTechnicianLinks(c *gin.Context) {
	var links []models.ProviderTechnicianLink
	if err := h.db.Preload("Provider").Preload("Technician").Order("created_at DESC").Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao buscar vínculos: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "data": links})
}

// Criar vínculo prestadora x técnico
func (h *NovoLinkManagementHandler) LinkProviderToTechnician(c *gin.Context) {
	var req struct {
		ProviderID   uint `json:"provider_id" binding:"required"`
		TechnicianID uint `json:"technician_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Dados inválidos: " + err.Error()})
		return
	}
	var existing models.ProviderTechnicianLink
	if err := h.db.Where("provider_id = ? AND technician_id = ?", req.ProviderID, req.TechnicianID).First(&existing).Error; err == nil {
		existing.IsActive = true
		if err := h.db.Save(&existing).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao ativar vínculo: " + err.Error()})
			return
		}
	} else {
		newLink := models.ProviderTechnicianLink{ProviderID: int(req.ProviderID), TechnicianID: int(req.TechnicianID), IsActive: true}
		if err := h.db.Create(&newLink).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao criar vínculo: " + err.Error()})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo criado com sucesso"})
}

// Remover vínculo prestadora x técnico
func (h *NovoLinkManagementHandler) UnlinkProviderFromTechnician(c *gin.Context) {
	providerID := c.Param("provider_id")
	technicianID := c.Param("technician_id")
	if err := h.db.Model(&models.ProviderTechnicianLink{}).Where("provider_id = ? AND technician_id = ?", providerID, technicianID).Update("is_active", false).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao desvincular: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo removido com sucesso"})
}

// --- VÍNCULOS PRESTADORA x FILIAL ---

// Listar vínculos prestadora x filial
func (h *NovoLinkManagementHandler) GetProviderBranchLinks(c *gin.Context) {
	var links []models.ProviderBranchLink
	if err := h.db.Preload("Provider").Preload("Branch").Order("created_at DESC").Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao buscar vínculos: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "data": links})
}

// Criar vínculo prestadora x filial
func (h *NovoLinkManagementHandler) LinkProviderToBranch(c *gin.Context) {
	var req struct {
		ProviderID uint `json:"provider_id" binding:"required"`
		BranchID   uint `json:"branch_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Dados inválidos: " + err.Error()})
		return
	}
	var existing models.ProviderBranchLink
	if err := h.db.Where("provider_id = ? AND branch_id = ?", req.ProviderID, req.BranchID).First(&existing).Error; err == nil {
		existing.IsActive = true
		if err := h.db.Save(&existing).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao ativar vínculo: " + err.Error()})
			return
		}
	} else {
		newLink := models.ProviderBranchLink{ProviderID: int(req.ProviderID), BranchID: int(req.BranchID), IsActive: true}
		if err := h.db.Create(&newLink).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao criar vínculo: " + err.Error()})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo criado com sucesso"})
}

// Remover vínculo prestadora x filial
func (h *NovoLinkManagementHandler) UnlinkProviderFromBranch(c *gin.Context) {
	providerID := c.Param("provider_id")
	branchID := c.Param("branch_id")
	if err := h.db.Model(&models.ProviderBranchLink{}).Where("provider_id = ? AND branch_id = ?", providerID, branchID).Update("is_active", false).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao desvincular: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo removido com sucesso"})
}
