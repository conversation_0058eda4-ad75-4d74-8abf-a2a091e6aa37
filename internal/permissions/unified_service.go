package permissions

import (
	"log"
	"strings"
	"sync"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// Constantes para perfis de usuário (para compatibilidade)
const (
	RoleTecnico   = "tecnico"
	RolePrestador = "prestador"
	RoleProvider  = "provider"
)

// UnifiedPermissionService é o serviço centralizado para gerenciar todas as permissões
// Este serviço unifica as verificações de permissão para páginas, APIs e recursos
type UnifiedPermissionService struct {
	db                 *gorm.DB
	service            *Service
	mutex              sync.RWMutex
	orderAssignmentSvc OrderAssignmentServiceInterface
	auditService       *PermissionAuditService
	config             *Config
}

// OrderAssignmentServiceInterface define a interface para o serviço de atribuição de ordens
// Isso permite que o serviço de permissões use o serviço de atribuição sem criar dependência circular
type OrderAssignmentServiceInterface interface {
	HasAccessToOrder(technicianID, orderID uint) (bool, error)
}

// NewUnifiedPermissionService cria uma nova instância do serviço unificado de permissões
func NewUnifiedPermissionService(db *gorm.DB, service *Service) *UnifiedPermissionService {
	// Verificar se o banco de dados está disponível
	var auditService *PermissionAuditService
	if db != nil {
		auditService = NewPermissionAuditService(db)

		// Ajuste: Configuração detalhada de auditoria
		auditConfig := AuditConfig{
			Enabled:             true,
			Level:               AuditLevelFull,
			LogSuccessfulChecks: true,
			LogFailedChecks:     true,
			SensitiveResources:  []string{},
		}
		auditService.SetConfig(auditConfig)

		// Verificar se a tabela audit_logs existe
		if err := db.Raw("SELECT 1 FROM audit_logs LIMIT 1").Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Aviso: Tabela audit_logs não encontrada ou não acessível: %v", err)
			log.Printf("[PERMISSION-SERVICE] A auditoria de permissões será desativada")
			auditService = nil
		}
	} else {
		log.Printf("[PERMISSION-SERVICE] Aviso: Banco de dados não disponível, a auditoria de permissões será desativada")
		auditService = nil
	}

	return &UnifiedPermissionService{
		db:           db,
		service:      service,
		mutex:        sync.RWMutex{},
		auditService: auditService,
		config:       service.config,
	}
}

// SetOrderAssignmentService define o serviço de atribuição de ordens
func (s *UnifiedPermissionService) SetOrderAssignmentService(svc OrderAssignmentServiceInterface) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.orderAssignmentSvc = svc
}

// HasPagePermission verifica se um usuário tem permissão para acessar uma página
func (s *UnifiedPermissionService) HasPagePermission(userID uint, userRole string, pagePath string, ip string, userAgent string) bool {
	// Usar o serviço de permissões existente
	allowed := s.service.HasPermission(userRole, pagePath, PagePermission)

	// Registrar na auditoria
	if s.auditService != nil {
		s.auditService.LogPageAccess(userID, userRole, pagePath, allowed, ip, userAgent)
	}

	return allowed
}

// HasAPIPermission verifica se um usuário tem permissão para acessar uma API
func (s *UnifiedPermissionService) HasAPIPermission(userID uint, userRole string, apiPath string, method string, ip string, userAgent string) bool {
	// LOG DETALHADO PARA DEBUG
	permittedRoles := s.service.GetPermittedRoles(apiPath, APIPermission)
	log.Printf("[PERMISSION-DEBUG] HasAPIPermission: userID=%d, userRole=%s, apiPath=%s, method=%s, permittedRoles=%v", userID, userRole, apiPath, method, permittedRoles)

	// Usar o serviço de permissões existente
	allowed := s.service.HasPermission(userRole, apiPath, APIPermission)
	log.Printf("[PERMISSION-DEBUG] HasPermission result: allowed=%v", allowed)

	// Registrar na auditoria
	if s.auditService != nil {
		s.auditService.LogAPIAccess(userID, userRole, apiPath, method, allowed, ip, userAgent)
	}

	return allowed
}

// HasResourcePermission verifica se um usuário tem permissão para acessar um recurso específico
func (s *UnifiedPermissionService) HasResourcePermission(userID uint, userRole string, resourceType ResourceType, resourceID uint, action Action, ip string, userAgent string) bool {
	// Administradores têm acesso a tudo
	if userRole == string(models.RoleAdmin) {
		// Registrar na auditoria
		if s.auditService != nil {
			s.auditService.LogPermissionCheck(userID, userRole, string(resourceType), resourceID, string(action), true, ip, userAgent)
		}
		return true
	}

	// Verificar permissões específicas para cada tipo de recurso
	var allowed bool
	switch resourceType {
	case ResourceOrder:
		allowed = s.hasOrderPermission(userID, userRole, resourceID, action, ip, userAgent)
	case ResourceEquipment:
		allowed = s.hasEquipmentPermission(userID, userRole, resourceID, action, ip, userAgent)
	case ResourceBranch:
		allowed = s.hasBranchPermission(userID, userRole, resourceID, action, ip, userAgent)
	case ResourceTechnician:
		allowed = s.hasTechnicianPermission(userID, userRole, resourceID, action, ip, userAgent)
	case ResourceServiceProvider:
		allowed = s.hasServiceProviderPermission(userID, userRole, resourceID, action, ip, userAgent)
	default:
		log.Printf("[PERMISSION-SERVICE] Tipo de recurso desconhecido: %s", resourceType)
		allowed = false
	}

	// Registrar na auditoria para os outros tipos de recursos
	if s.auditService != nil && resourceType != ResourceOrder {
		s.auditService.LogPermissionCheck(userID, userRole, string(resourceType), resourceID, string(action), allowed, ip, userAgent)
	}

	return allowed
}

// hasOrderPermission verifica se um usuário tem permissão para acessar uma ordem específica
func (s *UnifiedPermissionService) hasOrderPermission(userID uint, userRole string, orderID uint, action Action, ip string, userAgent string) bool {
	// Administradores, gerentes e financeiros têm acesso a todas as ordens
	if userRole == string(models.RoleAdmin) || userRole == string(models.RoleGerente) || userRole == string(models.RoleFinanceiro) {
		// Registrar na auditoria
		if s.auditService != nil {
			s.auditService.LogPermissionCheck(userID, userRole, "order", orderID, string(action), true, ip, userAgent)
		}
		return true
	}

	// Buscar a ordem
	var order models.MaintenanceOrder
	if err := s.db.First(&order, orderID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Erro ao buscar ordem %d: %v", orderID, err)
		return false
	}

	// Verificar permissões com base no perfil do usuário
	switch userRole {
	case string(models.RoleTechnician), RoleTecnico:
		// Verificar se o técnico tem acesso à ordem usando o serviço de atribuição
		if s.orderAssignmentSvc != nil {
			hasAccess, err := s.orderAssignmentSvc.HasAccessToOrder(userID, orderID)
			if err != nil {
				log.Printf("[PERMISSION-SERVICE] Erro ao verificar acesso do técnico %d à ordem %d: %v", userID, orderID, err)
				return false
			}
			return hasAccess
		}

		// Se o serviço de atribuição não estiver disponível, verificar diretamente
		// Verificar se o técnico está atribuído diretamente à ordem
		if order.TechnicianID != nil && *order.TechnicianID == userID {
			return true
		}

		// Verificar se o técnico pertence ao prestador atribuído à ordem
		if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
			var technician models.User
			if err := s.db.First(&technician, userID).Error; err != nil {
				log.Printf("[PERMISSION-SERVICE] Erro ao buscar técnico %d: %v", userID, err)
				return false
			}

			if technician.ServiceProviderID != nil && *technician.ServiceProviderID == *order.ServiceProviderID {
				return true
			}
		}

		// Verificar se existe um registro na tabela technician_orders
		var count int64
		if err := s.db.Model(&models.TechnicianOrder{}).
			Where("technician_id = ? AND order_id = ?", userID, orderID).
			Count(&count).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao verificar tabela technician_orders: %v", err)
			return false
		}

		return count > 0

	case string(models.RoleFilial), string(models.RoleBranchUser):
		// Filiais só podem acessar ordens da própria filial
		var user models.User
		if err := s.db.First(&user, userID).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao buscar usuário %d: %v", userID, err)
			return false
		}

		// Verificar se o usuário tem um BranchID diretamente associado
		if user.BranchID != nil && *user.BranchID == order.BranchID {
			return true
		}

		// Se não tiver BranchID diretamente, verificar na tabela user_branches
		var branchIDs []uint
		if err := s.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
			// Verificar se a ordem pertence a uma das filiais do usuário
			for _, branchID := range branchIDs {
				if branchID == order.BranchID {
					return true
				}
			}
		}

		// Se não encontrar nenhuma associação, negar acesso
		log.Printf("[PERMISSION-SERVICE] Usuário %d (filial) não tem acesso à ordem %d da filial %d", userID, order.ID, order.BranchID)
		return false

	case string(models.RolePrestador), RolePrestador:
		// Prestadores de serviço só podem acessar ordens atribuídas a eles
		return order.ServiceProviderID != nil && *order.ServiceProviderID == userID

	case string(models.RoleFinanceiro):
		// Financeiro tem acesso a todas as ordens para fins de relatório
		return true

	default:
		log.Printf("[PERMISSION-SERVICE] Perfil desconhecido: %s", userRole)
		return false
	}
}

// hasEquipmentPermission verifica se um usuário tem permissão para acessar um equipamento específico
func (s *UnifiedPermissionService) hasEquipmentPermission(userID uint, userRole string, equipmentID uint, action Action, ip string, userAgent string) bool {
	// Administradores, gerentes e financeiros têm acesso a todos os equipamentos
	if userRole == string(models.RoleAdmin) || userRole == string(models.RoleGerente) || userRole == string(models.RoleFinanceiro) {
		// Registrar na auditoria
		if s.auditService != nil {
			s.auditService.LogPermissionCheck(userID, userRole, "equipment", equipmentID, string(action), true, ip, userAgent)
		}
		return true
	}

	// Buscar o equipamento
	var equipment models.Equipment
	if err := s.db.First(&equipment, equipmentID).Error; err != nil {
		log.Printf("[PERMISSION-SERVICE] Erro ao buscar equipamento %d: %v", equipmentID, err)
		return false
	}

	// Verificar permissões com base no perfil do usuário
	switch userRole {
	case string(models.RoleFilial), string(models.RoleBranchUser):
		// Filiais só podem acessar equipamentos da própria filial
		var user models.User
		if err := s.db.First(&user, userID).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao buscar usuário %d: %v", userID, err)
			return false
		}

		// Verificar se o usuário tem um BranchID diretamente associado
		if user.BranchID != nil && *user.BranchID == equipment.BranchID {
			return true
		}

		// Se não tiver BranchID diretamente, verificar na tabela user_branches
		var branchIDs []uint
		if err := s.db.Table("user_branches").Where("user_id = ?", userID).Pluck("branch_id", &branchIDs).Error; err == nil && len(branchIDs) > 0 {
			// Verificar se o equipamento pertence a uma das filiais do usuário
			for _, branchID := range branchIDs {
				if branchID == equipment.BranchID {
					return true
				}
			}
		}

		// Se não encontrar nenhuma associação, negar acesso
		log.Printf("[PERMISSION-SERVICE] Usuário %d (filial) não tem acesso ao equipamento %d da filial %d", userID, equipment.ID, equipment.BranchID)
		return false

	case string(models.RoleTechnician), RoleTecnico:
		// Técnicos podem acessar equipamentos das filiais a que têm acesso
		// Primeiro verificar se o técnico tem acesso à filial do equipamento
		var technicianBranches []models.TechnicianBranch
		if err := s.db.Where("technician_id = ? AND is_active = ?", userID, true).Find(&technicianBranches).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao buscar filiais do técnico %d: %v", userID, err)
			return false
		}

		log.Printf("[PERMISSION-DEBUG] Técnico %d tem acesso às filiais: %+v", userID, technicianBranches)

		// Verificar se o técnico tem acesso à filial do equipamento
		hasAccessToBranch := false
		for _, tb := range technicianBranches {
			if uint(tb.BranchID) == equipment.BranchID {
				hasAccessToBranch = true
				break
			}
		}

		if !hasAccessToBranch {
			log.Printf("[PERMISSION-DEBUG] Técnico %d não tem acesso à filial %d do equipamento %d", userID, equipment.BranchID, equipmentID)
			return false
		}

		log.Printf("[PERMISSION-DEBUG] Técnico %d tem acesso ao equipamento %d da filial %d", userID, equipmentID, equipment.BranchID)
		return true

	case string(models.RolePrestador), RolePrestador:
		// Prestadores podem acessar equipamentos das filiais a que têm acesso
		var providerBranches []models.ProviderBranch
		if err := s.db.Where("service_provider_id = ?", userID).Find(&providerBranches).Error; err != nil {
			log.Printf("[PERMISSION-SERVICE] Erro ao buscar filiais do prestador %d: %v", userID, err)
			return false
		}

		// Se não encontrar nenhuma filial associada ao prestador, verificar se ele é um prestador direto
		if len(providerBranches) == 0 {
			var provider models.ServiceProvider
			if err := s.db.First(&provider, userID).Error; err != nil {
				log.Printf("[PERMISSION-SERVICE] Erro ao buscar prestador %d: %v", userID, err)
				return false
			}

			// Verificar se o prestador tem acesso ao equipamento
			// Verificar diretamente na tabela provider_equipment
			var count int64
			if err := s.db.Table("provider_equipment").
				Where("service_provider_id = ? AND equipment_id = ?", provider.ID, equipment.ID).
				Count(&count).Error; err != nil {
				log.Printf("[PERMISSION-SERVICE] Erro ao verificar acesso do prestador ao equipamento: %v", err)
				return false
			}

			return count > 0
		}

		for _, pb := range providerBranches {
			if pb.BranchID == equipment.BranchID {
				return true
			}
		}

		// Se não encontrar nenhuma associação, negar acesso
		log.Printf("[PERMISSION-SERVICE] Prestador %d não tem acesso ao equipamento %d da filial %d", userID, equipment.ID, equipment.BranchID)
		return false

	case string(models.RoleFinanceiro):
		// Financeiro tem acesso a todos os equipamentos para fins de relatório
		return true

	default:
		log.Printf("[PERMISSION-SERVICE] Perfil desconhecido: %s", userRole)
		return false
	}
}

// hasBranchPermission verifica se um usuário tem permissão para acessar uma filial específica
func (s *UnifiedPermissionService) hasBranchPermission(userID uint, userRole string, branchID uint, action Action, ip string, userAgent string) bool {
	// Implementação simplificada - expandir conforme necessário

	// Registrar na auditoria
	if s.auditService != nil {
		s.auditService.LogPermissionCheck(userID, userRole, "branch", branchID, string(action), true, ip, userAgent)
	}

	return true
}

// hasTechnicianPermission verifica se um usuário tem permissão para acessar um técnico específico
func (s *UnifiedPermissionService) hasTechnicianPermission(userID uint, userRole string, technicianID uint, action Action, ip string, userAgent string) bool {
	// Implementação simplificada - expandir conforme necessário

	// Registrar na auditoria
	if s.auditService != nil {
		s.auditService.LogPermissionCheck(userID, userRole, "technician", technicianID, string(action), true, ip, userAgent)
	}

	return true
}

// hasServiceProviderPermission verifica se um usuário tem permissão para acessar um prestador específico
func (s *UnifiedPermissionService) hasServiceProviderPermission(userID uint, userRole string, providerID uint, action Action, ip string, userAgent string) bool {
	// Implementação simplificada - expandir conforme necessário

	// Registrar na auditoria
	if s.auditService != nil {
		s.auditService.LogPermissionCheck(userID, userRole, "service_provider", providerID, string(action), true, ip, userAgent)
	}

	return true
}

// GetPermittedRoles retorna a lista de perfis que têm acesso a um recurso
func (s *UnifiedPermissionService) GetPermittedRoles(resourcePath string, permType PermissionType) []string {
	// Usar o serviço de permissões existente
	return s.service.GetPermittedRoles(resourcePath, permType)
}

// GetConfig retorna a configuração de permissões
func (s *UnifiedPermissionService) GetConfig() *Config {
	// Usar o serviço de permissões existente
	return s.service.GetConfig()
}

// HasRoutePermission verifica se um usuário tem permissão para acessar uma rota
func (s *UnifiedPermissionService) HasRoutePermission(userID uint, userRole string, path string, method string) bool {
	// Administradores têm acesso a todas as rotas
	if userRole == "admin" {
		return true
	}

	// Verificar se é uma rota pública
	for _, publicAPI := range s.config.PublicAPIs {
		if matchesRoute(path, publicAPI) {
			return true
		}
	}

	// Verificar permissões específicas do papel
	roleConfig, exists := s.config.Roles[userRole]
	if !exists {
		log.Printf("[PERMISSION] Papel não encontrado: %s", userRole)
		return false
	}

	// Verificar permissões de API
	for _, api := range roleConfig.APIs {
		if matchesRoute(path, api) {
			return true
		}
	}

	log.Printf("[PERMISSION] Acesso negado: Usuário %d (role: %s) não tem permissão para %s %s",
		userID, userRole, method, path)
	return false
}

// Função auxiliar para verificar se uma rota corresponde a um padrão
func matchesRoute(path, pattern string) bool {
	// Implementação simples: verificar se o padrão é um prefixo da rota
	// ou se termina com * e o prefixo corresponde
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(path, prefix)
	}
	return path == pattern
}
