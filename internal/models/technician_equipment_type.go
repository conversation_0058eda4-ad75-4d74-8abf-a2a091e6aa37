package models

import (
	"time"
)

// TechnicianEquipmentType representa um relacionamento entre um técnico e um tipo de equipamento
// Este modelo controla a aptidão técnica de técnicos para tipos específicos de equipamento
// Estrutura baseada na tabela real do banco: technician_equipment_types
type TechnicianEquipmentType struct {
	ID              int       `json:"id" gorm:"primaryKey" db:"id"`
	TechnicianID    int       `json:"technician_id" gorm:"column:technician_id;not null" db:"technician_id"`
	EquipmentTypeID int       `json:"equipment_type_id" gorm:"column:equipment_type_id;not null" db:"equipment_type_id"`
	IsActive        bool      `json:"is_active" gorm:"column:is_active;default:true" db:"is_active"`
	CreatedAt       time.Time `json:"created_at" gorm:"column:created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"column:updated_at" db:"updated_at"`

	// Relacionamentos
	Technician    *User          `json:"technician,omitempty" gorm:"foreignKey:TechnicianID" db:"-"`
	EquipmentType *EquipmentType `json:"equipment_type,omitempty" gorm:"foreignKey:EquipmentTypeID" db:"-"`
}

// TableName define o nome da tabela no banco de dados
func (TechnicianEquipmentType) TableName() string {
	return "technician_equipment_types"
}
