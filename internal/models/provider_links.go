package models

import (
	"time"
)

// ProviderBranchLink representa vínculo entre prestadora e filial
// Estrutura baseada na tabela real do banco: provider_branch_links
type ProviderBranchLink struct {
	ID         int       `json:"id" gorm:"primaryKey" db:"id"`
	ProviderID int       `json:"provider_id" gorm:"column:provider_id" db:"provider_id"`
	BranchID   int       `json:"branch_id" gorm:"column:branch_id" db:"branch_id"`
	IsActive   bool      `json:"is_active" gorm:"column:is_active;default:true" db:"is_active"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at" db:"updated_at"`

	// Relacionamentos
	Provider *ServiceProvider `json:"provider,omitempty" gorm:"foreignKey:ProviderID" db:"-"`
	Branch   *Branch          `json:"branch,omitempty" gorm:"foreignKey:BranchID" db:"-"`
}

// TableName define o nome da tabela no banco de dados
func (ProviderBranchLink) TableName() string {
	return "provider_branch_links"
}
