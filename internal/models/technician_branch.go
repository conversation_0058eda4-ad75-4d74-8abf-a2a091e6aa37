package models

import (
	"time"
)

// TechnicianBranch representa o vínculo entre um técnico e uma filial
// Este modelo controla exclusivamente o acesso de técnicos às filiais
// Para aptidão técnica por tipo de equipamento, veja TechnicianEquipmentType
// Estrutura baseada na tabela real do banco: technician_branches
type TechnicianBranch struct {
	ID              int       `json:"id" gorm:"primaryKey" db:"id"`
	TechnicianID int       `json:"technician_id" gorm:"column:technician_id" db:"technician_id"`
	BranchID     int       `json:"branch_id" gorm:"column:branch_id" db:"branch_id"`
	IsActive     bool      `json:"is_active" gorm:"column:is_active;default:true" db:"is_active"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at" db:"created_at"`

	// Relacionamentos
	Technician *User   `json:"technician,omitempty" gorm:"foreignKey:TechnicianID" db:"-"`
	Branch     *Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID" db:"-"`
}

// TableName define o nome da tabela no banco de dados
func (TechnicianBranch) TableName() string {
	return "technician_branches"
}
