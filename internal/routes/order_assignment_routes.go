package routes

import (
	"net/http"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// SetupOrderAssignmentRoutes configura redirects de compatibilidade para rotas legadas de atribuição
// 
// IMPORTANTE: Este arquivo mantém apenas redirects 301 para compatibilidade com sistemas legados.
// Toda nova funcionalidade de atribuição deve usar os endpoints unificados em /api/orders/:id/assign
// 
// Funcionalidade movida para:
// - unified_order_handler.AssignOrder() para POST /api/orders/:id/assign
// - unified_order_handler.GetOrder() para GET /api/orders/:id (inclui dados de atribuição)
//
// Redirects implementados:
// - /api/order-assignments/technician/* → /api/orders/:id/assign
// - /api/order-assignments/provider/* → /api/orders/:id/assign  
// - /api/ordens/atribuir → /api/orders/:id/assign
func SetupOrderAssignmentRoutes(router *gin.Engine) {
	// Grupo de rotas legadas para compatibilidade - APENAS REDIRECTS 301
	apiAssignments := router.Group("/api/order-assignments")
	apiAssignments.Use(middleware.RequireAuth())

	// Redirects para rotas de técnicos - movidas para /api/orders/:id/assign
	technicianAssignments := apiAssignments.Group("/technician")
	technicianAssignments.Use(permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware())
	{
		// POST /api/order-assignments/technician → POST /api/orders/:id/assign
		technicianAssignments.POST("", func(c *gin.Context) {
			c.Header("Location", "/api/orders/:id/assign")
			c.JSON(http.StatusMovedPermanently, gin.H{
				"success": false,
				"message": "Endpoint movido permanentemente para POST /api/orders/:id/assign",
				"new_endpoint": "/api/orders/:id/assign",
				"method": "POST",
			})
		})

		// GET /api/order-assignments/technician → GET /api/orders/technician
		technicianAssignments.GET("", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/api/orders/technician")
		})
	}

	// Redirects para rotas de prestadores - movidas para /api/orders/:id/assign
	providerAssignments := apiAssignments.Group("/provider")
	providerAssignments.Use(permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware())
	{
		// POST /api/order-assignments/provider → POST /api/orders/:id/assign
		providerAssignments.POST("", func(c *gin.Context) {
			c.Header("Location", "/api/orders/:id/assign")
			c.JSON(http.StatusMovedPermanently, gin.H{
				"success": false,
				"message": "Endpoint movido permanentemente para POST /api/orders/:id/assign",
				"new_endpoint": "/api/orders/:id/assign",
				"method": "POST",
			})
		})

		// GET /api/order-assignments/provider → GET /api/orders (com filtros)
		providerAssignments.GET("", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/api/orders")
		})
	}

	// Redirects para rotas antigas em português
	router.GET("/api/ordens/atribuir", middleware.RequireAuth(), permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware(), func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api/orders")
	})

	// Redirect adicional para POST em /api/ordens/atribuir
	router.POST("/api/ordens/atribuir", middleware.RequireAuth(), permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware(), func(c *gin.Context) {
		c.Header("Location", "/api/orders/:id/assign")
		c.JSON(http.StatusMovedPermanently, gin.H{
			"success": false,
			"message": "Endpoint movido permanentemente para POST /api/orders/:id/assign",
			"new_endpoint": "/api/orders/:id/assign",
			"method": "POST",
		})
	})
}
