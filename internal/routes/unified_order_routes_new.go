package routes

import (
	"fmt"
	"net/http"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// SetupUnifiedOrderRoutesNew configura todas as rotas unificadas do sistema
// Esta função substitui todas as rotas duplicadas por endpoints únicos e consistentes
func SetupUnifiedOrderRoutesNew(router *gin.Engine) {
	// Criar handler unificado
	unifiedHandler := handlers.NewUnifiedOrderHandler()

	// API Routes - Endpoints unificados para ordens
	apiOrders := router.Group("/api/orders")
	apiOrders.Use(middleware.RequireAuth())      // Middleware de autenticação
	apiOrders.Use(middleware.FilialMiddleware()) // Middleware de filial para contexto
	{
		// Endpoint principal para listar ordens
		// Substitui: /api/orders, /api/ordens, /api/orders/list
		apiOrders.GET("", unifiedHandler.ListOrders)

		// Endpoint para criar ordens - ADICIONADO
		// Substitui: /api/orders POST, /api/ordens POST
		apiOrders.POST("", unifiedHandler.CreateOrder)

		// Endpoint para obter uma ordem específica
		// Substitui: /api/orders/:id, /api/ordens/:id
		apiOrders.GET("/:id", unifiedHandler.GetOrder)

		// Endpoint para atualizar status da ordem
		// Substitui: /api/ordens/:id/status, PUT /api/orders/:id/status
		// Parâmetros: status (string) - novo status da ordem
		// Resposta: StandardResponse com ordem atualizada
		apiOrders.PUT("/:id/status", permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware(), unifiedHandler.UpdateOrderStatus)

		// Endpoint para atribuir ordem a técnico ou prestador
		// Substitui: /api/order-assignments/*, /api/ordens/atribuir
		// Parâmetros: assigned_to (uint), assigned_type (string: "technician" ou "provider")
		// Resposta: StandardResponse com ordem atribuída e notificação enviada
		apiOrders.POST("/:id/assign", permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware(), unifiedHandler.AssignOrder)

		// Endpoint para ordens do calendário
		// Substitui: /api/ordens/calendario, /api/calendar-events
		apiOrders.GET("/calendar", unifiedHandler.GetCalendarOrders)

		// Endpoint para ordens do técnico
		// Substitui: /api/ordens/tecnico
		apiOrders.GET("/technician", unifiedHandler.GetTechnicianOrders)

		// Endpoint simples para ordens do técnico (sem filtros complexos)
		apiOrders.GET("/technician_simple", unifiedHandler.GetTechnicianOrdersSimple)

		// Endpoints para métricas e estatísticas
		apiOrders.GET("/metrics", func(c *gin.Context) {
			// TODO: Implementar métricas unificadas
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "Métricas em desenvolvimento",
				"data": gin.H{
					"total":       0,
					"pending":     0,
					"in_progress": 0,
					"completed":   0,
				},
			})
		})

		// Endpoint para buscar prestadoras disponíveis por filial e tipo de equipamento
		apiOrders.GET("/available-providers", unifiedHandler.GetAvailableProvidersByEquipment)

		// Endpoint para buscar técnicos disponíveis por filial e tipo de equipamento
		// Parâmetros: branch_id (uint), equipment_type_id (uint)
		// Resposta: StandardResponse com lista de técnicos vinculados ao tipo de equipamento
		apiOrders.GET("/available-technicians", unifiedHandler.GetAvailableTechniciansByEquipment)
	}

	// Redirecionamentos para manter compatibilidade
	// Redirecionar endpoints antigos para os novos
	setupCompatibilityRedirects(router)

	// Web Routes - Páginas HTML (mantidas para compatibilidade)
	// NOTA: Estas rotas devem estar no grupo 'pages' para funcionar corretamente
	// com os middlewares de permissão de página
	webOrders := router.Group("/orders")
	webOrders.Use(middleware.RequireAuth())
	webOrders.Use(middleware.FilialMiddleware())
	{
		// Página principal de ordens
		webOrders.GET("", func(c *gin.Context) {
			user, _ := c.Get("user")
			c.HTML(http.StatusOK, "ordens/orders_gallery_style.html", gin.H{
				"title":      "Ordens de Serviço - Rede Tradição",
				"page":       "orders",
				"ActivePage": "orders",
				"User":       user,
			})
		})

		// Página de criação de ordens - ADICIONADA
		webOrders.GET("/create", func(c *gin.Context) {
			user, _ := c.Get("user")
			userID, exists := c.Get("userID")
			if !exists {
				c.Redirect(http.StatusFound, "/login")
				return
			}
			userName, _ := c.Get("userName")
			if userName == nil {
				userName = "Usuário"
			}
			branchID, exists := c.Get("filialID")
			if !exists {
				branchID, exists = c.Get("branchID")
				if !exists {
					branchID = uint(0)
				}
			}
			branchName, _ := c.Get("filialName")
			if branchName == nil {
				branchName = "Filial"
			}
			branchIDStr := fmt.Sprintf("%v", branchID)
			c.HTML(http.StatusOK, "ordens/create_order.html", gin.H{
				"title":      "Nova Ordem de Serviço - Rede Tradição",
				"page":       "orders_create",
				"ActivePage": "orders",
				"UserID":     userID,
				"UserName":   userName,
				"BranchID":   branchIDStr,
				"User":       user,
				"BranchName": branchName,
			})
		})

		// Página de calendário
		webOrders.GET("/calendar", func(c *gin.Context) {
			user, _ := c.Get("user")
			c.HTML(http.StatusOK, "ordens/calendar.html", gin.H{
				"title":      "Calendário de Ordens - Rede Tradição",
				"page":       "orders_calendar",
				"ActivePage": "orders",
				"User":       user,
			})
		})

		// Página de detalhes da ordem
		webOrders.GET("/:id", func(c *gin.Context) {
			user, _ := c.Get("user")
			c.HTML(http.StatusOK, "ordens/order_detail.html", gin.H{
				"title":      "Detalhes da Ordem - Rede Tradição",
				"page":       "order_detail",
				"ActivePage": "orders",
				"OrderID":    c.Param("id"),
				"User":       user,
			})
		})
	}
}

// setupCompatibilityRedirects configura redirecionamentos para manter compatibilidade
func setupCompatibilityRedirects(router *gin.Engine) {
	// Redirecionamentos para endpoints antigos

	// /api/ordens -> /api/orders
	router.GET("/api/ordens", func(c *gin.Context) {
		// Preservar query parameters
		query := c.Request.URL.RawQuery
		newURL := "/api/orders"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/:id -> /api/orders/:id
	router.GET("/api/ordens/:id", func(c *gin.Context) {
		id := c.Param("id")
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/" + id
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/tecnico -> /api/orders/technician
	router.GET("/api/ordens/tecnico", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/technician"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/ordens/calendario -> /api/orders/calendar
	router.GET("/api/ordens/calendario", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/calendar"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/calendar-events -> /api/orders/calendar
	router.GET("/api/calendar-events", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/calendar"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// Redirecionamentos para páginas web antigas
	router.GET("/ordens", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders")
	})

	router.GET("/ordens/:id", func(c *gin.Context) {
		id := c.Param("id")
		c.Redirect(http.StatusMovedPermanently, "/orders/"+id)
	})

	// Rota /calendario removida - já existe no main.go

	// Redirecionamentos adicionais para endpoints legados
	setupAdditionalLegacyRedirects(router)
}

// setupAdditionalLegacyRedirects configura redirecionamentos para endpoints legados adicionais
func setupAdditionalLegacyRedirects(router *gin.Engine) {
	// /verordens -> /api/orders/technician
	router.GET("/verordens", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/technician"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /api/verordens -> /api/orders/technician
	router.GET("/api/verordens", func(c *gin.Context) {
		query := c.Request.URL.RawQuery
		newURL := "/api/orders/technician"
		if query != "" {
			newURL += "?" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	// /atribuir_ordem -> /orders (página web para atribuição)
	router.GET("/atribuir_ordem", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/orders")
	})

	// /api/ordens/atribuir -> /api/orders/:id/assign (requer ID na URL)
	router.POST("/api/ordens/atribuir", func(c *gin.Context) {
		c.JSON(http.StatusMovedPermanently, gin.H{
			"success": false,
			"message": "Endpoint movido. Use POST /api/orders/:id/assign",
			"redirect": "/api/orders/:id/assign",
		})
	})

	// /api/order-assignments/* -> /api/orders/:id/assign
	router.Any("/api/order-assignments/*path", func(c *gin.Context) {
		c.JSON(http.StatusMovedPermanently, gin.H{
			"success": false,
			"message": "Endpoints de atribuição foram consolidados. Use POST /api/orders/:id/assign",
			"redirect": "/api/orders/:id/assign",
		})
	})

	// Redirecionamentos para rotas v2 legadas
	router.GET("/api/v2/filial/:filialID/ordens", func(c *gin.Context) {
		filialID := c.Param("filialID")
		query := c.Request.URL.RawQuery
		newURL := "/api/orders?branch_id=" + filialID
		if query != "" {
			newURL += "&" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})

	router.GET("/filial/:filialID/ordens", func(c *gin.Context) {
		filialID := c.Param("filialID")
		query := c.Request.URL.RawQuery
		newURL := "/orders?branch_id=" + filialID
		if query != "" {
			newURL += "&" + query
		}
		c.Redirect(http.StatusMovedPermanently, newURL)
	})
}

// RemoveDeprecatedRoutes remove rotas obsoletas que foram substituídas
// Esta função serve como documentação das rotas que foram removidas
func RemoveDeprecatedRoutes() {
	// HANDLERS REMOVIDOS (violavam arquitetura unificada):
	// - api_ordens.go (duplicava funcionalidade do unified_order_handler.go)
	// - verordens_handler.go (funcionalidade movida para unified handler)
	// - ordens.go (código morto, não usado por rotas ativas)
	// - ordem_v2_handler.go (handler transitório, não faz parte da arquitetura final)

	// ROTAS REMOVIDAS E REDIRECIONADAS:
	// - /api/ordens → /api/orders (301 redirect)
	// - /api/ordens/:id → /api/orders/:id (301 redirect)
	// - /api/ordens/tecnico → /api/orders/technician (301 redirect)
	// - /api/ordens/calendario → /api/orders/calendar (301 redirect)
	// - /api/calendar-events → /api/orders/calendar (301 redirect)
	// - /verordens → /api/orders/technician (301 redirect)
	// - /api/verordens → /api/orders/technician (301 redirect)
	// - /atribuir_ordem → /orders (301 redirect)
	// - /api/ordens/atribuir → /api/orders/:id/assign (301 redirect)
	// - /api/order-assignments/* → /api/orders/:id/assign (301 redirect)
	// - /api/v2/filial/:id/ordens → /api/orders?branch_id=:id (301 redirect)
	// - /filial/:id/ordens → /orders?branch_id=:id (301 redirect)

	// ARQUIVOS DE ROTAS REMOVIDOS:
	// - internal/routes/ordens.go (rotas legadas não unificadas)
	// - internal/routes/ordens_v2_routes.go (rotas v2 legadas)
	// - internal/routes/verordens_routes.go (rotas legadas do verordens)

	// BENEFÍCIOS DA UNIFICAÇÃO:
	// ✅ Endpoint único e consistente (/api/orders)
	// ✅ Resposta padronizada (StandardResponse)
	// ✅ Filtros consistentes em todos os endpoints
	// ✅ Paginação padronizada
	// ✅ Tratamento de erros unificado
	// ✅ Exclusão automática da ordem #18
	// ✅ Logs estruturados
	// ✅ Cache otimizado com TTL
	// ✅ Queries eficientes (sem N+1)
	// ✅ Manutenção simplificada
	// ✅ Notificações automáticas integradas
	// ✅ Validação de vínculos técnico-equipamento
	// ✅ RBAC granular por filial
	// ✅ Middleware consistente em todas as rotas
	// ✅ 0% duplicação de código (meta obrigatória)
	// ✅ Conformidade total com ARQUITETURA_OBRIGATORIA.md
}

// GetUnifiedEndpoints retorna lista completa dos endpoints unificados ativos
func GetUnifiedEndpoints() map[string]string {
	return map[string]string{
		// API Endpoints - Operações CRUD unificadas
		"list_orders":              "GET /api/orders",
		"create_order":             "POST /api/orders",
		"get_order":                "GET /api/orders/:id",
		"update_order_status":      "PUT /api/orders/:id/status",
		"assign_order":             "POST /api/orders/:id/assign",
		"calendar_orders":          "GET /api/orders/calendar",
		"technician_orders":        "GET /api/orders/technician",
		"technician_orders_simple": "GET /api/orders/technician_simple",
		"order_metrics":            "GET /api/orders/metrics",
		"available_providers":      "GET /api/orders/available-providers",
		"available_technicians":    "GET /api/orders/available-technicians",

		// Web Pages - Interface HTML
		"orders_page":       "GET /orders",
		"create_order_page": "GET /orders/create",
		"calendar_page":     "GET /orders/calendar",
		"order_detail_page": "GET /orders/:id",
	}
}

// GetEndpointDocumentation retorna documentação detalhada dos endpoints
func GetEndpointDocumentation() map[string]map[string]interface{} {
	return map[string]map[string]interface{}{
		"GET /api/orders": {
			"description": "Lista ordens com filtros e paginação",
			"parameters": map[string]string{
				"page":        "Número da página (padrão: 1)",
				"limit":       "Itens por página (padrão: 10, máx: 100)",
				"branch_id":   "Filtrar por filial",
				"status":      "Filtrar por status",
				"technician":  "Filtrar por técnico",
				"provider":    "Filtrar por prestador",
				"date_from":   "Data inicial (YYYY-MM-DD)",
				"date_to":     "Data final (YYYY-MM-DD)",
			},
			"response": "StandardResponse com lista paginada de ordens",
			"middleware": []string{"RequireAuth", "FilialMiddleware"},
		},
		"POST /api/orders": {
			"description": "Cria nova ordem de serviço",
			"body": map[string]string{
				"branch_id":         "ID da filial (obrigatório)",
				"equipment_id":      "ID do equipamento (obrigatório)",
				"assigned_to":       "ID do técnico/prestador (opcional)",
				"assigned_type":     "Tipo: 'technician' ou 'provider' (opcional)",
				"description":       "Descrição do problema (obrigatório)",
				"priority":          "Prioridade: 'low', 'medium', 'high' (padrão: medium)",
				"scheduled_date":    "Data agendada (opcional)",
			},
			"response": "StandardResponse com ordem criada e notificação enviada",
			"middleware": []string{"RequireAuth", "FilialMiddleware"},
		},
		"PUT /api/orders/:id/status": {
			"description": "Atualiza status da ordem",
			"parameters": map[string]string{
				"id": "ID da ordem",
			},
			"body": map[string]string{
				"status": "Novo status: 'pending', 'in_progress', 'completed', 'cancelled'",
			},
			"response": "StandardResponse com ordem atualizada",
			"middleware": []string{"RequireAuth", "FilialMiddleware", "APIAccessMiddleware"},
		},
		"POST /api/orders/:id/assign": {
			"description": "Atribui ordem a técnico ou prestador",
			"parameters": map[string]string{
				"id": "ID da ordem",
			},
			"body": map[string]string{
				"assigned_to":   "ID do técnico/prestador (obrigatório)",
				"assigned_type": "Tipo: 'technician' ou 'provider' (obrigatório)",
			},
			"response": "StandardResponse com ordem atribuída e notificação enviada",
			"middleware": []string{"RequireAuth", "FilialMiddleware", "APIAccessMiddleware"},
		},
		"GET /api/orders/available-technicians": {
			"description": "Lista técnicos disponíveis por filial e tipo de equipamento",
			"parameters": map[string]string{
				"branch_id":         "ID da filial (obrigatório)",
				"equipment_type_id": "ID do tipo de equipamento (obrigatório)",
			},
			"response": "StandardResponse com técnicos vinculados ao tipo de equipamento",
			"middleware": []string{"RequireAuth", "FilialMiddleware"},
		},
		"GET /api/orders/available-providers": {
			"description": "Lista prestadores disponíveis por filial e tipo de equipamento",
			"parameters": map[string]string{
				"branch_id":         "ID da filial (obrigatório)",
				"equipment_type_id": "ID do tipo de equipamento (obrigatório)",
			},
			"response": "StandardResponse com prestadores disponíveis",
			"middleware": []string{"RequireAuth", "FilialMiddleware"},
		},
	}
}
