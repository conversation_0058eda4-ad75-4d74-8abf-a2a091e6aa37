package routes

import (
	"tradicao/internal/database"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/permissions"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupNovoLinkManagementRoutes configura as rotas para o novo gerenciamento de vínculos
func SetupNovoLinkManagementRoutes(router *gin.Engine) {
	// Criar handler
	db, err := database.Connect()
	if err != nil {
		return
	}
	// Inicializar MenuService com sistema de permissões global
	permissionService := permissions.GetGlobalUnifiedService()
	menuService := services.NewMenuService(permissionService)
	handler := handlers.NewNovoLinkManagementHandler(db, menuService)

	// Rota da página principal (protegida)
	adminGroup := router.Group("/admin")
	adminGroup.Use(middleware.RequireAuth())
	adminGroup.Use(permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware())
	{
		adminGroup.GET("/novo-link-management", handler.ShowNovoLinkManagementPage)
	}

	// Rota de teste temporária (sem autenticação)
	router.GET("/teste-modal", handler.ShowNovoLinkManagementPage)

	// Rotas da API (protegidas)
	apiGroup := router.Group("/api/novo-link-management")
	apiGroup.Use(middleware.RequireAuth())
	apiGroup.Use(permissions.GetGlobalUnifiedMiddleware().APIAccessMiddleware())
	{
		// Tipos de equipamentos
		apiGroup.GET("/equipment-types", handler.GetEquipmentTypes)

		// Técnicos
		apiGroup.GET("/technicians/active", handler.GetActiveTechnicians)
		apiGroup.GET("/technicians/:id/maintenance-history", handler.GetTechnicianMaintenanceHistory)

		// Vínculos técnico-filial
		apiGroup.GET("/links/technician-branch", handler.GetTechnicianBranchLinks)
		apiGroup.POST("/links/technician-branch", handler.LinkTechnicianToBranch)
		apiGroup.DELETE("/links/technician-branch/:technician_id/:branch_id", handler.UnlinkTechnicianFromBranch)

		// Vínculos técnico-tipo de equipamento
		apiGroup.GET("/links/technician-equipment-type", handler.GetTechnicianEquipmentTypeLinks)
		apiGroup.POST("/links/technician-equipment-type", handler.LinkTechnicianToEquipmentType)
		apiGroup.DELETE("/links/technician-equipment-type/:technician_id/:equipment_type_id", handler.UnlinkTechnicianFromEquipmentType)

		// VÍNCULOS PRESTADORA x TÉCNICO
		apiGroup.GET("/links/provider-technician", handler.GetProviderTechnicianLinks)
		apiGroup.POST("/links/provider-technician", handler.LinkProviderToTechnician)
		apiGroup.DELETE("/links/provider-technician/:provider_id/:technician_id", handler.UnlinkProviderFromTechnician)

		// VÍNCULOS PRESTADORA x FILIAL
		apiGroup.GET("/links/provider-branch", handler.GetProviderBranchLinks)
		apiGroup.POST("/links/provider-branch", handler.LinkProviderToBranch)
		apiGroup.DELETE("/links/provider-branch/:provider_id/:branch_id", handler.UnlinkProviderFromBranch)
	}
}
