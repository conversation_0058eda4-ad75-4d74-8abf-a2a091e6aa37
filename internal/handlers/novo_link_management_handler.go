package handlers

import (
	"log"
	"net/http"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NovoLinkManagementHandler gerencia a nova página de gerenciamento de vínculos
type NovoLinkManagementHandler struct {
	db          *gorm.DB
	menuService *services.MenuService
}

// NewNovoLinkManagementHandler cria uma nova instância do handler
func NewNovoLinkManagementHandler(db *gorm.DB, menuService *services.MenuService) *NovoLinkManagementHandler {
	return &NovoLinkManagementHandler{db: db, menuService: menuService}
}

// ShowNovoLinkManagementPage renderiza a página de gerenciamento de vínculos
func (h *NovoLinkManagementHandler) ShowNovoLinkManagementPage(c *gin.Context) {
	// Recuperar usuário do contexto
	userValue, exists := c.Get("user")
	var user *models.User
	if exists {
		if userObj, ok := userValue.(*models.User); ok {
			user = userObj
		}
	}
	if user == nil {
		userID, _ := c.Get("userID")
		userRole, _ := c.Get("userRole")
		userName, _ := c.Get("userName")
		if userID != nil && userRole != nil {
			user = &models.User{
				ID:   uint(userID.(int)),
				Role: models.UserRole(userRole.(string)),
				Name: userName.(string),
			}
		}
	}
	menu := h.menuService.GetMenuForPage(user, "admin-link-management")
	c.HTML(http.StatusOK, "admin/novo_link_management.html", gin.H{
		"title":      "Gerenciamento de Vínculos",
		"ActivePage": "admin-link-management",
		"User":       user,
		"UserMenu":   menu,
	})
}

// GetEquipmentTypes retorna todos os tipos de equipamentos
func (h *NovoLinkManagementHandler) GetEquipmentTypes(c *gin.Context) {
	var equipmentTypes []models.EquipmentType
	if err := h.db.Order("name").Find(&equipmentTypes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar tipos de equipamentos: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    equipmentTypes,
	})
}

// GetActiveTechnicians retorna técnicos ativos
func (h *NovoLinkManagementHandler) GetActiveTechnicians(c *gin.Context) {
	log.Printf("[GET-TECHNICIANS] Iniciando busca por técnicos ativos")
	log.Printf("[GET-TECHNICIANS] Filtro aplicado: role IN ('tecnico', 'technician') AND is_active = true")

	var technicians []models.User
	if err := h.db.Where("role IN (?, ?) AND active = ?", "tecnico", "technician", true).
		Order("name").Find(&technicians).Error; err != nil {
		log.Printf("[GET-TECHNICIANS-ERROR] Erro ao buscar técnicos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar técnicos: " + err.Error(),
		})
		return
	}

	log.Printf("[GET-TECHNICIANS-SUCCESS] Encontrados %d técnicos ativos", len(technicians))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    technicians,
	})
}

// GetTechnicianMaintenanceHistory retorna o histórico de manutenções de um técnico
func (h *NovoLinkManagementHandler) GetTechnicianMaintenanceHistory(c *gin.Context) {
	technicianID := c.Param("id")

	var history []models.TechnicianMaintenanceHistory
	if err := h.db.Where("technician_id = ?", technicianID).
		Order("maintenance_date DESC").
		Find(&history).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar histórico: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// GetTechnicianBranchLinks retorna os vínculos técnico-filial
func (h *NovoLinkManagementHandler) GetTechnicianBranchLinks(c *gin.Context) {
	branchID := c.Query("branch_id")
	technicianID := c.Query("technician_id")

	log.Printf("[GET-TECH-LINKS] Buscando vínculos - BranchID: %s, TechnicianID: %s", branchID, technicianID)

	var links []models.TechnicianBranch
	query := h.db.Preload("Technician").
		Preload("Branch")

	// Filtrar por branch_id se fornecido
	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
		log.Printf("[GET-TECH-LINKS] Aplicando filtro por branch_id: %s", branchID)
	}

	// Filtrar por technician_id se fornecido
	if technicianID != "" {
		query = query.Where("technician_id = ?", technicianID)
		log.Printf("[GET-TECH-LINKS] Aplicando filtro por technician_id: %s", technicianID)
	}

	if err := query.Order("created_at DESC").Find(&links).Error; err != nil {
		log.Printf("[GET-TECH-LINKS-ERROR] Erro ao buscar vínculos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar vínculos: " + err.Error(),
		})
		return
	}

	log.Printf("[GET-TECH-LINKS-SUCCESS] Encontrados %d vínculos", len(links))
	for i, link := range links {
		techName := "N/A"
		if link.Technician != nil {
			techName = link.Technician.Name
		}
		branchName := "N/A"
		if link.Branch != nil {
			branchName = link.Branch.Name
		}
		log.Printf("[GET-TECH-LINKS-DETAIL] Vínculo %d: TechID=%d (%s) -> BranchID=%d (%s)",
			i+1, link.TechnicianID, techName, link.BranchID, branchName)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    links,
	})
}

// LinkTechnicianToBranch vincula um técnico a uma filial
func (h *NovoLinkManagementHandler) LinkTechnicianToBranch(c *gin.Context) {
	log.Printf("[LINK-TECH] Iniciando vinculação técnico-filial")

	var request struct {
		TechnicianID uint `json:"technician_id" binding:"required"`
		BranchID     uint `json:"branch_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("[LINK-TECH-ERROR] Dados inválidos: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}

	log.Printf("[LINK-TECH] Dados recebidos: TechnicianID=%d, BranchID=%d",
		request.TechnicianID, request.BranchID)

	// Log informativo - não fazemos verificação de especialidades para vínculos simples
	log.Printf("[LINK-INFO] Criando vínculo simples técnico-filial (sem verificação de especialidades)")

	// Verificar se o vínculo já existe (apenas técnico + filial)
	var existingBranch models.TechnicianBranch
	if err := h.db.Where("technician_id = ? AND branch_id = ?", request.TechnicianID, request.BranchID).First(&existingBranch).Error; err == nil {
		// Vínculo já existe
		log.Printf("[LINK-TECH-INFO] Vínculo já existe: Técnico %d -> Filial %d", request.TechnicianID, request.BranchID)
	} else {
		// Criar novo vínculo usando SQL direto para evitar problemas com modelo
		if err := h.db.Exec("INSERT INTO technician_branches (technician_id, branch_id, created_at) VALUES (?, ?, NOW())",
			request.TechnicianID, request.BranchID).Error; err != nil {
			log.Printf("[LINK-TECH-ERROR] Erro ao criar vínculo no banco: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao criar vínculo: " + err.Error(),
			})
			return
		}
		log.Printf("[LINK-TECH-SUCCESS] Novo vínculo criado: Técnico %d -> Filial %d", request.TechnicianID, request.BranchID)
	}

	log.Printf("[LINK-TECH-SUCCESS] Técnico %d vinculado à filial %d com sucesso", request.TechnicianID, request.BranchID)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico vinculado à filial com sucesso",
	})
}

// UnlinkTechnicianFromBranch desvincula um técnico de uma filial
func (h *NovoLinkManagementHandler) UnlinkTechnicianFromBranch(c *gin.Context) {
	technicianID := c.Param("technician_id")
	branchID := c.Param("branch_id")

	log.Printf("[UNLINK-TECH] Removendo vínculo: TechnicianID=%s, BranchID=%s", technicianID, branchID)

	// Remover o vínculo completamente da tabela (DELETE ao invés de UPDATE)
	if err := h.db.Where("technician_id = ? AND branch_id = ?", technicianID, branchID).
		Delete(&models.TechnicianBranch{}).Error; err != nil {
		log.Printf("[UNLINK-TECH-ERROR] Erro ao remover vínculo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao desvincular técnico: " + err.Error(),
		})
		return
	}

	log.Printf("[UNLINK-TECH-SUCCESS] Vínculo removido com sucesso")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico desvinculado da filial com sucesso",
	})
}

// --- VÍNCULOS TÉCNICO x TIPO DE EQUIPAMENTO ---

// GetTechnicianEquipmentTypeLinks retorna os vínculos técnico-tipo de equipamento
func (h *NovoLinkManagementHandler) GetTechnicianEquipmentTypeLinks(c *gin.Context) {
	equipmentTypeID := c.Query("equipment_type_id")
	technicianID := c.Query("technician_id")

	log.Printf("[GET-TECH-EQ-LINKS] Buscando vínculos - EquipmentTypeID: %s, TechnicianID: %s", equipmentTypeID, technicianID)

	var links []models.TechnicianEquipmentType
	query := h.db.Preload("Technician").Preload("EquipmentType")

	if equipmentTypeID != "" {
		query = query.Where("equipment_type_id = ?", equipmentTypeID)
		log.Printf("[GET-TECH-EQ-LINKS] Aplicando filtro por equipment_type_id: %s", equipmentTypeID)
	}

	if technicianID != "" {
		query = query.Where("technician_id = ?", technicianID)
		log.Printf("[GET-TECH-EQ-LINKS] Aplicando filtro por technician_id: %s", technicianID)
	}

	if err := query.Order("created_at DESC").Find(&links).Error; err != nil {
		log.Printf("[GET-TECH-EQ-LINKS-ERROR] Erro ao buscar vínculos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar vínculos técnico-equipamento: " + err.Error(),
		})
		return
	}

	log.Printf("[GET-TECH-EQ-LINKS-SUCCESS] Encontrados %d vínculos", len(links))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    links,
	})
}

// LinkTechnicianToEquipmentType vincula um técnico a um tipo de equipamento
func (h *NovoLinkManagementHandler) LinkTechnicianToEquipmentType(c *gin.Context) {
	log.Printf("[LINK-TECH-EQ] Iniciando vinculação técnico-equipamento")

	var request struct {
		TechnicianID    uint `json:"technician_id" binding:"required"`
		EquipmentTypeID uint `json:"equipment_type_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("[LINK-TECH-EQ-ERROR] Dados inválidos: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Dados inválidos: " + err.Error(),
		})
		return
	}

	log.Printf("[LINK-TECH-EQ] Dados recebidos: TechnicianID=%d, EquipmentTypeID=%d",
		request.TechnicianID, request.EquipmentTypeID)

	var existing models.TechnicianEquipmentType
	if err := h.db.Where("technician_id = ? AND equipment_type_id = ?", request.TechnicianID, request.EquipmentTypeID).
		First(&existing).Error; err == nil {
		// Ativar vínculo se já existir
		existing.IsActive = true
		if err := h.db.Save(&existing).Error; err != nil {
			log.Printf("[LINK-TECH-EQ-ERROR] Erro ao ativar vínculo: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao ativar vínculo técnico-equipamento: " + err.Error(),
			})
			return
		}
		log.Printf("[LINK-TECH-EQ-SUCCESS] Vínculo reativado: Técnico %d -> EquipmentType %d", request.TechnicianID, request.EquipmentTypeID)
	} else {
		// Criar novo vínculo
		newLink := models.TechnicianEquipmentType{
			TechnicianID:    int(request.TechnicianID),
			EquipmentTypeID: int(request.EquipmentTypeID),
			IsActive:        true,
		}
		if err := h.db.Create(&newLink).Error; err != nil {
			log.Printf("[LINK-TECH-EQ-ERROR] Erro ao criar vínculo: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Erro ao criar vínculo técnico-equipamento: " + err.Error(),
			})
			return
		}
		log.Printf("[LINK-TECH-EQ-SUCCESS] Novo vínculo criado: Técnico %d -> EquipmentType %d", request.TechnicianID, request.EquipmentTypeID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico vinculado ao tipo de equipamento com sucesso",
	})
}

// UnlinkTechnicianFromEquipmentType desvincula um técnico de um tipo de equipamento
func (h *NovoLinkManagementHandler) UnlinkTechnicianFromEquipmentType(c *gin.Context) {
	technicianID := c.Param("technician_id")
	equipmentTypeID := c.Param("equipment_type_id")

	log.Printf("[UNLINK-TECH-EQ] Removendo vínculo: TechnicianID=%s, EquipmentTypeID=%s", technicianID, equipmentTypeID)

	// Atualizar is_active para false
	if err := h.db.Model(&models.TechnicianEquipmentType{}).
		Where("technician_id = ? AND equipment_type_id = ?", technicianID, equipmentTypeID).
		Update("is_active", false).Error; err != nil {
		log.Printf("[UNLINK-TECH-EQ-ERROR] Erro ao desvincular vínculo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao desvincular técnico-equipamento: " + err.Error(),
		})
		return
	}

	log.Printf("[UNLINK-TECH-EQ-SUCCESS] Vínculo técnico-equipamento removido com sucesso")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Técnico desvinculado do tipo de equipamento com sucesso",
	})
}

// --- VÍNCULOS PRESTADORA x TÉCNICO ---

// Listar vínculos prestadora x técnico
func (h *NovoLinkManagementHandler) GetProviderTechnicianLinks(c *gin.Context) {
	var links []models.ProviderTechnicianLink
	query := h.db.Preload("Provider").Preload("Technician")

	// Filtrar por provider_id se fornecido
	if providerID := c.Query("provider_id"); providerID != "" {
		query = query.Where("provider_id = ?", providerID)
	}

	// Filtrar por technician_id se fornecido
	if technicianID := c.Query("technician_id"); technicianID != "" {
		query = query.Where("technician_id = ?", technicianID)
	}

	if err := query.Order("created_at DESC").Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao buscar vínculos: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "data": links})
}

// Criar vínculo prestadora x técnico
func (h *NovoLinkManagementHandler) LinkProviderToTechnician(c *gin.Context) {
	var req struct {
		ProviderID   uint `json:"provider_id" binding:"required"`
		TechnicianID uint `json:"technician_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Dados inválidos: " + err.Error()})
		return
	}
	var existing models.ProviderTechnicianLink
	if err := h.db.Where("provider_id = ? AND technician_id = ?", req.ProviderID, req.TechnicianID).First(&existing).Error; err == nil {
		existing.IsActive = true
		if err := h.db.Save(&existing).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao ativar vínculo: " + err.Error()})
			return
		}
	} else {
		newLink := models.ProviderTechnicianLink{ProviderID: int(req.ProviderID), TechnicianID: int(req.TechnicianID), IsActive: true}
		if err := h.db.Create(&newLink).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao criar vínculo: " + err.Error()})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo criado com sucesso"})
}

// Remover vínculo prestadora x técnico
func (h *NovoLinkManagementHandler) UnlinkProviderFromTechnician(c *gin.Context) {
	providerID := c.Param("provider_id")
	technicianID := c.Param("technician_id")
	if err := h.db.Model(&models.ProviderTechnicianLink{}).Where("provider_id = ? AND technician_id = ?", providerID, technicianID).Update("is_active", false).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao desvincular: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo removido com sucesso"})
}

// --- VÍNCULOS PRESTADORA x FILIAL ---

// Listar vínculos prestadora x filial
func (h *NovoLinkManagementHandler) GetProviderBranchLinks(c *gin.Context) {
	var links []models.ProviderBranchLink
	query := h.db.Preload("Provider").Preload("Branch")

	// Filtrar por branch_id se fornecido
	if branchID := c.Query("branch_id"); branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}

	// Filtrar por provider_id se fornecido
	if providerID := c.Query("provider_id"); providerID != "" {
		query = query.Where("provider_id = ?", providerID)
	}

	if err := query.Order("created_at DESC").Find(&links).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao buscar vínculos: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "data": links})
}

// Criar vínculo prestadora x filial
func (h *NovoLinkManagementHandler) LinkProviderToBranch(c *gin.Context) {
	var req struct {
		ProviderID uint `json:"provider_id" binding:"required"`
		BranchID   uint `json:"branch_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "Dados inválidos: " + err.Error()})
		return
	}
	var existing models.ProviderBranchLink
	if err := h.db.Where("provider_id = ? AND branch_id = ?", req.ProviderID, req.BranchID).First(&existing).Error; err == nil {
		// Vínculo já existe
		log.Printf("[LINK-PROVIDER-INFO] Vínculo já existe: Prestadora %d -> Filial %d", req.ProviderID, req.BranchID)
	} else {
		// Criar novo vínculo (apenas campos que existem na tabela real)
		newLink := models.ProviderBranchLink{
			ProviderID: int(req.ProviderID),
			BranchID:   int(req.BranchID),
		}
		if err := h.db.Create(&newLink).Error; err != nil {
			log.Printf("[LINK-PROVIDER-ERROR] Erro ao criar vínculo no banco: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao criar vínculo: " + err.Error()})
			return
		}
		log.Printf("[LINK-PROVIDER-SUCCESS] Novo vínculo criado: Prestadora %d -> Filial %d", req.ProviderID, req.BranchID)
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo criado com sucesso"})
}

// Remover vínculo prestadora x filial
func (h *NovoLinkManagementHandler) UnlinkProviderFromBranch(c *gin.Context) {
	providerID := c.Param("provider_id")
	branchID := c.Param("branch_id")
	if err := h.db.Model(&models.ProviderBranchLink{}).Where("provider_id = ? AND branch_id = ?", providerID, branchID).Update("is_active", false).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "Erro ao desvincular: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Vínculo removido com sucesso"})
}
