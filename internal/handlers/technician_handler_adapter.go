package handlers

import (
	"time"
	"tradicao/internal/models"
	"tradicao/internal/services"
)

// TechnicianServiceAdapter adapta um *services.TechnicianService para a interface services.TechnicianService
type TechnicianServiceAdapter struct {
	service *services.TechnicianService
}

// NewTechnicianServiceAdapter cria um novo adaptador para o serviço de técnicos
func NewTechnicianServiceAdapter(service *services.TechnicianService) services.TechnicianServiceInterface {
	return &TechnicianServiceAdapter{service: service}
}

// Implementação dos métodos da interface services.TechnicianService

// CreateSpecialty cria uma nova especialidade
func (a *TechnicianServiceAdapter) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	return a.service.CreateSpecialty(specialty)
}

// ListSpecialties lista todas as especialidades
func (a *TechnicianServiceAdapter) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	return a.service.ListSpecialties()
}

// UpdateSpecialty atualiza uma especialidade existente
func (a *TechnicianServiceAdapter) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	return a.service.UpdateSpecialty(specialty)
}

// DeleteSpecialty remove uma especialidade
func (a *TechnicianServiceAdapter) DeleteSpecialty(id uint) error {
	return a.service.DeleteSpecialty(id)
}

// GetSpecialty obtém uma especialidade pelo ID
func (a *TechnicianServiceAdapter) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	return a.service.GetSpecialty(id)
}

// GetSpecialtyByID obtém uma especialidade pelo ID
func (a *TechnicianServiceAdapter) GetSpecialtyByID(id uint) (*models.TechnicianSpecialty, error) {
	return a.service.GetSpecialtyByID(id)
}

// CreateTechnicianBranch cria uma associação entre técnico e filial
func (a *TechnicianServiceAdapter) CreateTechnicianBranch(branch *models.TechnicianBranch) error {
	return a.service.CreateTechnicianBranch(branch)
}

// GetTechnicianBranches obtém as filiais associadas a um técnico
func (a *TechnicianServiceAdapter) GetTechnicianBranches(technicianID uint) ([]models.TechnicianBranch, error) {
	return a.service.GetTechnicianBranches(technicianID)
}

// GetBranchTechnicians obtém os técnicos associados a uma filial
func (a *TechnicianServiceAdapter) GetBranchTechnicians(branchID uint) ([]models.Technician, error) {
	return a.service.GetBranchTechnicians(branchID)
}

// DeleteTechnicianBranch remove uma associação entre técnico e filial
func (a *TechnicianServiceAdapter) DeleteTechnicianBranch(technicianID, branchID, specialtyID uint) error {
	return a.service.DeleteTechnicianBranch(technicianID, branchID, specialtyID)
}

// GetTechnicianHistory obtém o histórico de manutenções de um técnico
func (a *TechnicianServiceAdapter) GetTechnicianHistory(technicianID uint) ([]models.MaintenanceHistory, error) {
	return a.service.GetTechnicianHistory(technicianID)
}

// GetEquipmentHistory obtém o histórico de manutenções de um equipamento
func (a *TechnicianServiceAdapter) GetEquipmentHistory(equipmentID uint) ([]models.MaintenanceHistory, error) {
	return a.service.GetEquipmentHistory(equipmentID)
}

// GetOrderHistory obtém o histórico de manutenções de uma ordem
func (a *TechnicianServiceAdapter) GetOrderHistory(orderID uint) ([]models.MaintenanceHistory, error) {
	return a.service.GetOrderHistory(orderID)
}

// CreateMaintenanceHistory cria um novo registro no histórico de manutenção
func (a *TechnicianServiceAdapter) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	return a.service.CreateMaintenanceHistory(history)
}

// CreateBranchAssociation cria uma nova associação entre técnico e filial
func (a *TechnicianServiceAdapter) CreateBranchAssociation(association *models.TechnicianBranch) error {
	return a.service.CreateBranchAssociation(association)
}

// UpdateBranchAssociation atualiza uma associação existente
func (a *TechnicianServiceAdapter) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	return a.service.UpdateBranchAssociation(association)
}

// DeleteBranchAssociation remove uma associação
func (a *TechnicianServiceAdapter) DeleteBranchAssociation(technicianID, branchID uint) error {
	return a.service.DeleteBranchAssociation(technicianID, branchID)
}

// GetBranchAssociation obtém uma associação pelo ID do técnico e da filial
func (a *TechnicianServiceAdapter) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	return a.service.GetBranchAssociation(technicianID, branchID)
}

// ListBranchAssociations lista todas as associações de um técnico
func (a *TechnicianServiceAdapter) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	return a.service.ListBranchAssociations(technicianID)
}

// ListMaintenanceHistory lista o histórico de manutenção de um técnico
func (a *TechnicianServiceAdapter) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	return a.service.ListMaintenanceHistory(technicianID, startDate, endDate)
}

// GetMaintenanceHistory obtém um registro do histórico pelo ID
func (a *TechnicianServiceAdapter) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	return a.service.GetMaintenanceHistory(id)
}

// GetBranchTechniciansFiltered retorna técnicos ativos vinculados à filial e ao tipo de equipamento
func (a *TechnicianServiceAdapter) GetBranchTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error) {
	return a.service.GetBranchTechniciansFiltered(branchID, equipmentTypeID)
}

func (a *TechnicianServiceAdapter) GetBranchInternalTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error) {
	return a.service.GetBranchInternalTechniciansFiltered(branchID, equipmentTypeID)
}

// GetTechnicianByUserID busca um técnico pelo ID do usuário
func (a *TechnicianServiceAdapter) GetTechnicianByUserID(userID uint) (*models.User, error) {
	return a.service.GetTechnicianByUserID(userID)
}
