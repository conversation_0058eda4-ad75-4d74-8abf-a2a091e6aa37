package handlers

import (
	"log"
	"net/http"
	"strconv"
	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// TechnicianHandler gerencia as requisições relacionadas a técnicos
type TechnicianHandler struct {
	userService *services.UserService
	techService services.TechnicianServiceInterface
}

// NewTechnicianHandler cria um novo handler para técnicos
func NewTechnicianHandler(userService *services.UserService, techService services.TechnicianServiceInterface) *TechnicianHandler {
	return &TechnicianHandler{
		userService: userService,
		techService: techService,
	}
}

// ListTechnicians retorna todos os técnicos disponíveis
func (h *TechnicianHandler) ListTechnicians(c *gin.Context) {
	log.Printf("[TECHNICIAN] Buscando técnicos disponíveis")

	// Obter técnicos do serviço
	technicians, err := h.userService.GetTechnicians()
	if err != nil {
		log.Printf("[TECHNICIAN-ERROR] Erro ao buscar técnicos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Erro ao buscar técnicos",
			"message": err.Error(),
		})
		return
	}

	// Se não houver técnicos, retornar array vazio
	if technicians == nil {
		log.Printf("[TECHNICIAN] Nenhum técnico encontrado, retornando array vazio")
		c.JSON(http.StatusOK, gin.H{
			"success":     true,
			"data":        []models.UserResponse{},
			"technicians": []models.UserResponse{},
		})
		return
	}

	log.Printf("[TECHNICIAN] Retornando %d técnicos", len(technicians))
	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"data":        technicians,
		"technicians": technicians,
	})
}

// GetTechniciansForAPI retorna todos os técnicos disponíveis no formato esperado pelo frontend
func (h *TechnicianHandler) GetTechniciansForAPI(c *gin.Context) {
	log.Printf("[TECHNICIAN-API] Buscando técnicos disponíveis para API")

	// Obter técnicos do serviço
	technicians, err := h.userService.GetTechnicians()
	if err != nil {
		log.Printf("[TECHNICIAN-API-ERROR] Erro ao buscar técnicos: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Erro ao buscar técnicos: " + err.Error(),
		})
		return
	}

	// Se não houver técnicos, retornar array vazio
	if technicians == nil {
		log.Printf("[TECHNICIAN-API] Nenhum técnico encontrado, retornando array vazio")
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    []models.UserResponse{},
		})
		return
	}

	log.Printf("[TECHNICIAN-API] Retornando %d técnicos", len(technicians))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    technicians,
	})
}

// GetTechnicianByID retorna um técnico específico pelo ID
func (h *TechnicianHandler) GetTechnicianByID(c *gin.Context) {
	log.Printf("[TECHNICIAN] Buscando técnico por ID")

	// Obter ID do técnico da URL
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("[TECHNICIAN-ERROR] ID inválido: %s - %v", idStr, err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido", "details": err.Error()})
		return
	}

	// Buscar técnico pelo ID
	technician, err := h.userService.GetUserByID(id)
	if err != nil {
		log.Printf("[TECHNICIAN-ERROR] Erro ao buscar técnico ID %d: %v", id, err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Técnico não encontrado", "details": err.Error()})
		return
	}

	// Verificar se o usuário é realmente um técnico
	if !models.IsTechnician(string(technician.Role)) {
		log.Printf("[TECHNICIAN-ERROR] Usuário ID %d não é um técnico (role: %s)", id, technician.Role)
		c.JSON(http.StatusNotFound, gin.H{"error": "Usuário não é um técnico"})
		return
	}

	log.Printf("[TECHNICIAN] Técnico encontrado: ID=%d, Nome=%s", technician.ID, technician.Name)
	c.JSON(http.StatusOK, technician)
}

// Especialidades

// CreateSpecialty cria uma nova especialidade
func (h *TechnicianHandler) CreateSpecialty(c *gin.Context) {
	var specialty models.TechnicianSpecialty
	if err := c.ShouldBindJSON(&specialty); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	if err := h.techService.CreateSpecialty(&specialty); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, specialty)
}

// GetSpecialtyByID retorna uma especialidade pelo ID
func (h *TechnicianHandler) GetSpecialtyByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	specialty, err := h.techService.GetSpecialtyByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Especialidade não encontrada"})
		return
	}

	c.JSON(http.StatusOK, specialty)
}

// ListSpecialties retorna todas as especialidades
func (h *TechnicianHandler) ListSpecialties(c *gin.Context) {
	specialties, err := h.techService.ListSpecialties()
	if err != nil {
		log.Printf("[ERROR] Erro ao buscar especialidades: %v", err)

		// Se não houver especialidades ou ocorrer um erro, retornar uma lista vazia
		c.JSON(http.StatusOK, []models.TechnicianSpecialty{})
		return
	}

	// Se não houver especialidades, retornar uma lista vazia
	if len(specialties) == 0 {
		c.JSON(http.StatusOK, []models.TechnicianSpecialty{})
		return
	}

	c.JSON(http.StatusOK, specialties)
}

// UpdateSpecialty atualiza uma especialidade
func (h *TechnicianHandler) UpdateSpecialty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var specialty models.TechnicianSpecialty
	if err := c.ShouldBindJSON(&specialty); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	specialty.ID = uint(id)
	if err := h.techService.UpdateSpecialty(&specialty); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, specialty)
}

// DeleteSpecialty remove uma especialidade
func (h *TechnicianHandler) DeleteSpecialty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.techService.DeleteSpecialty(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// Vinculação com Filiais

// CreateTechnicianBranch vincula um técnico a uma filial
func (h *TechnicianHandler) CreateTechnicianBranch(c *gin.Context) {
	var branch models.TechnicianBranch
	if err := c.ShouldBindJSON(&branch); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	if err := h.techService.CreateTechnicianBranch(&branch); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// GetTechnicianBranches retorna as filiais de um técnico
func (h *TechnicianHandler) GetTechnicianBranches(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	branches, err := h.techService.GetTechnicianBranches(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar filiais"})
		return
	}

	c.JSON(http.StatusOK, branches)
}

// GetBranchTechnicians retorna os técnicos de uma filial
func (h *TechnicianHandler) GetBranchTechnicians(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	equipmentTypeIDStr := c.Query("equipment_type_id")
	var equipmentTypeID *uint
	if equipmentTypeIDStr != "" {
		parsed, err := strconv.ParseUint(equipmentTypeIDStr, 10, 32)
		if err == nil {
			tmp := uint(parsed)
			equipmentTypeID = &tmp
		}
	}

	technicians, err := h.techService.GetBranchTechniciansFiltered(uint(id), equipmentTypeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar técnicos"})
		return
	}

	c.JSON(http.StatusOK, technicians)
}

// Endpoint para buscar técnicos por filial e tipo de equipamento
func (h *TechnicianHandler) GetBranchTechniciansAPI(c *gin.Context) {
	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}
	equipmentTypeIDStr := c.Query("equipment_type_id")
	var equipmentTypeID *uint
	if equipmentTypeIDStr != "" {
		parsed, err := strconv.ParseUint(equipmentTypeIDStr, 10, 32)
		if err == nil {
			tmp := uint(parsed)
			equipmentTypeID = &tmp
		}
	}
	technicians, err := h.techService.GetBranchTechniciansFiltered(uint(branchID), equipmentTypeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar técnicos"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": technicians})
}

// Endpoint para buscar técnicos da filial sem vínculo com prestadora
func (h *TechnicianHandler) GetBranchInternalTechniciansAPI(c *gin.Context) {
	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}
	equipmentTypeIDStr := c.Query("equipment_type_id")
	var equipmentTypeID *uint
	if equipmentTypeIDStr != "" {
		parsed, err := strconv.ParseUint(equipmentTypeIDStr, 10, 32)
		if err == nil {
			tmp := uint(parsed)
			equipmentTypeID = &tmp
		}
	}
	technicians, err := h.techService.GetBranchInternalTechniciansFiltered(uint(branchID), equipmentTypeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar técnicos"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": technicians})
}

// DeleteTechnicianBranch remove a vinculação de um técnico com uma filial
func (h *TechnicianHandler) DeleteTechnicianBranch(c *gin.Context) {
	technicianID, err := strconv.ParseUint(c.Param("technician_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchID, err := strconv.ParseUint(c.Param("branch_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	specialtyID, err := strconv.ParseUint(c.Param("specialty_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID da especialidade inválido"})
		return
	}

	if err := h.techService.DeleteTechnicianBranch(uint(technicianID), uint(branchID), uint(specialtyID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// Histórico de Manutenções

// CreateMaintenanceHistory registra um histórico de manutenção
func (h *TechnicianHandler) CreateMaintenanceHistory(c *gin.Context) {
	var history models.MaintenanceHistory
	if err := c.ShouldBindJSON(&history); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	if err := h.techService.CreateMaintenanceHistory(&history); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, history)
}

// GetTechnicianHistory retorna o histórico de manutenções de um técnico
func (h *TechnicianHandler) GetTechnicianHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	history, err := h.techService.GetTechnicianHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar histórico"})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetEquipmentHistory retorna o histórico de manutenções de um equipamento
func (h *TechnicianHandler) GetEquipmentHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	history, err := h.techService.GetEquipmentHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar histórico"})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetOrderHistory retorna o histórico de manutenções de uma ordem
func (h *TechnicianHandler) GetOrderHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	history, err := h.techService.GetOrderHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao buscar histórico"})
		return
	}

	c.JSON(http.StatusOK, history)
}
