package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	webpush "github.com/SherClockHolmes/webpush-go"
)

// NotificationService gerencia notificações
type NotificationService struct {
	repo            *repository.GormNotificationRepository
	userRepo        repository.UserRepository
	orderRepo       repository.OrderRepository // Para buscar informações sobre ordens
	vapidPublicKey  string
	vapidPrivateKey string
	vapidSubject    string                           // Geralmente um mailto: ou https: URL
	clients         map[*models.WebSocketClient]bool // Clientes WebSocket conectados
}

// NewNotificationService cria um novo serviço de notificações
func NewNotificationService(
	repo *repository.GormNotificationRepository,
	userRepo repository.UserRepository,
	orderRepo repository.OrderRepository,
	vapidPublicKey string,
	vapidPrivateKey string,
	vapidSubject string,
) *NotificationService {
	return &NotificationService{
		repo:            repo,
		userRepo:        userRepo,
		orderRepo:       orderRepo,
		vapidPublicKey:  vapidPublicKey,
		vapidPrivateKey: vapidPrivateKey,
		vapidSubject:    vapidSubject,
		clients:         make(map[*models.WebSocketClient]bool),
	}
}

// SaveSubscription salva uma assinatura de notificação para um usuário
func (s *NotificationService) SaveSubscription(userID int64, userRole string, subscription models.PushSubscription) error {
	sub := models.NotificationSubscription{
		UserID:       userID,
		UserRole:     userRole,
		Subscription: subscription,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	return s.repo.SaveSubscription(sub)
}

// DeleteSubscription remove uma assinatura de notificação
func (s *NotificationService) DeleteSubscription(endpoint string) error {
	return s.repo.DeleteSubscriptionByEndpoint(endpoint)
}

// EnviarNotificacao envia uma notificação para um usuário específico
func (s *NotificationService) EnviarNotificacao(userID int64, titulo, mensagem string, orderID int64, url string) error {
	var orderIDUint *uint
	if orderID > 0 {
		u := uint(orderID)
		orderIDUint = &u
	}

	notificacao := models.Notification{
		UserID:    uint(userID),
		Title:     titulo,
		Body:      mensagem,
		OrderID:   orderIDUint,
		URL:       url,
		Read:      false,
		CreatedAt: time.Now(),
	}

	// Salvar a notificação no banco de dados
	if err := s.repo.SaveNotification(notificacao); err != nil {
		return fmt.Errorf("erro ao salvar notificação: %w", err)
	}

	// Buscar as assinaturas do usuário
	subscriptions, err := s.repo.GetSubscriptionsByUserID(userID)
	if err != nil {
		return fmt.Errorf("erro ao buscar assinaturas do usuário: %w", err)
	}

	// Enviar para cada assinatura do usuário
	for _, sub := range subscriptions {
		if err := s.sendPushNotification(sub.Subscription, notificacao); err != nil {
			log.Printf("Erro ao enviar notificação push para %s: %v", sub.Subscription.Endpoint, err)
			// Continuar para as próximas assinaturas mesmo que uma falhe
		}
	}

	return nil
}

// EnviarNotificacaoPorPapel envia uma notificação para todos os usuários com determinado papel
func (s *NotificationService) EnviarNotificacaoPorPapel(papel string, titulo, mensagem string, orderID int64, url string) error {
	// Buscar as assinaturas dos usuários com este papel
	subscriptions, err := s.repo.GetSubscriptionsByRole(papel)
	if err != nil {
		return fmt.Errorf("erro ao buscar assinaturas por papel: %w", err)
	}

	var orderIDUint *uint
	if orderID > 0 {
		u := uint(orderID)
		orderIDUint = &u
	}

	// Processar cada assinatura
	for _, sub := range subscriptions {
		notificacao := models.Notification{
			UserID:    uint(sub.UserID),
			Title:     titulo,
			Body:      mensagem,
			OrderID:   orderIDUint,
			URL:       url,
			Read:      false,
			CreatedAt: time.Now(),
		}

		// Salvar a notificação no banco de dados
		if err := s.repo.SaveNotification(notificacao); err != nil {
			log.Printf("Erro ao salvar notificação para usuário %d: %v", sub.UserID, err)
			continue
		}

		// Enviar a notificação push
		if err := s.sendPushNotification(sub.Subscription, notificacao); err != nil {
			log.Printf("Erro ao enviar notificação push para %s: %v", sub.Subscription.Endpoint, err)
			// Continuar para os próximos usuários mesmo que um falhe
		}
	}

	return nil
}

// EnviarNotificacaoOrdemServico envia uma notificação relacionada a uma ordem de serviço
func (s *NotificationService) EnviarNotificacaoOrdemServico(
	orderID int64,
	acao string,
	usuarioOrigemID int64,
	titulo, mensagem string,
) error {
	// Buscar detalhes da ordem para saber quem deve receber a notificação
	ordem, err := s.orderRepo.GetOrderByID(uint(orderID))
	if err != nil {
		return fmt.Errorf("erro ao buscar detalhes da ordem %d: %w", orderID, err)
	}

	// Preparar a URL para a ordem
	url := fmt.Sprintf("/calendario/order/%d", orderID)

	// Dependendo da ação, notificar pessoas diferentes
	switch acao {
	case "nova_ordem":
		// Notificar o provedor/técnico designado
		if ordem.ServiceProviderID != nil && *ordem.ServiceProviderID > 0 {
			providerID := int64(*ordem.ServiceProviderID)

			// Remover referências a WhatsApp - apenas enviar notificação normal
			return s.EnviarNotificacao(providerID, titulo, mensagem, orderID, url)
		}

	case "aprovada":
		// Notificar o provedor/técnico
		if ordem.ServiceProviderID != nil && *ordem.ServiceProviderID > 0 {
			providerID := int64(*ordem.ServiceProviderID)

			// Remover referências a WhatsApp - apenas enviar notificação normal
			return s.EnviarNotificacao(providerID, titulo, mensagem, orderID, url)
		}

	case "comentario":
		// Notificar gerentes e o provedor/técnico (exceto quem enviou o comentário)
		var destinatarios []string

		// Se não foi um gerente que comentou, notificar gerentes
		usuario, _ := s.userRepo.GetUserByID(usuarioOrigemID)
		if usuario == nil || usuario.Role != models.RoleGerente {
			destinatarios = append(destinatarios, string(models.RoleGerente))
		}

		// Se não foi um financeiro que comentou, notificar financeiros
		if usuario == nil || usuario.Role != models.RoleFinanceiro {
			destinatarios = append(destinatarios, string(models.RoleFinanceiro))
		}

		// Se não foi o provedor/técnico que comentou, notificar o provedor/técnico
		if ordem.ServiceProviderID != nil && int64(*ordem.ServiceProviderID) != usuarioOrigemID {
			providerID := int64(*ordem.ServiceProviderID)
			s.EnviarNotificacao(providerID, titulo, mensagem, orderID, url)
		}

		// Enviar para os papéis específicos
		for _, papel := range destinatarios {
			s.EnviarNotificacaoPorPapel(papel, titulo, mensagem, orderID, url)
		}

	case "rejeitada", "alteracao_status":
		// Notificar o provedor/técnico apenas via notificação normal
		if ordem.ServiceProviderID != nil && *ordem.ServiceProviderID > 0 {
			providerID := int64(*ordem.ServiceProviderID)
			return s.EnviarNotificacao(providerID, titulo, mensagem, orderID, url)
		}

	case "documento_enviado", "documento_solicitado":
		// Notificar gerentes e financeiro
		s.EnviarNotificacaoPorPapel(string(models.RoleGerente), titulo, mensagem, orderID, url)
		s.EnviarNotificacaoPorPapel(string(models.RoleFinanceiro), titulo, mensagem, orderID, url)
	}

	return nil
}

// sendPushNotification envia uma notificação push usando a WebPush API
func (s *NotificationService) sendPushNotification(subscription models.PushSubscription, notification models.Notification) error {
	// Preparar os dados da notificação
	payload, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("erro ao converter notificação para JSON: %w", err)
	}

	// Criar objeto de assinatura
	sub := &webpush.Subscription{
		Endpoint: subscription.Endpoint,
		Keys: webpush.Keys{
			Auth:   subscription.Keys["auth"],
			P256dh: subscription.Keys["p256dh"],
		},
	}

	// Enviar a notificação usando a função SendNotification do pacote webpush
	// Configurar opções da notificação
	options := &webpush.Options{
		TTL:             30,
		Urgency:         webpush.UrgencyHigh,
		Topic:           fmt.Sprintf("order-%v", notification.OrderID),
		VAPIDPublicKey:  s.vapidPublicKey,
		VAPIDPrivateKey: s.vapidPrivateKey,
		Subscriber:      s.vapidSubject,
	}

	// Enviar notificação
	resp, err := webpush.SendNotification(payload, sub, options)

	if err != nil {
		return fmt.Errorf("erro ao enviar notificação: %w", err)
	}
	defer resp.Body.Close()

	// Verificar resposta
	if resp.StatusCode >= 400 {
		// Se for 404 ou 410, a assinatura não é mais válida
		if resp.StatusCode == http.StatusNotFound || resp.StatusCode == http.StatusGone {
			// Remover assinatura inválida do banco de dados
			if err := s.repo.DeleteSubscriptionByEndpoint(subscription.Endpoint); err != nil {
				log.Printf("Erro ao remover assinatura inválida: %v", err)
			}
		}
		return fmt.Errorf("erro HTTP ao enviar notificação: %d", resp.StatusCode)
	}

	return nil
}

// MarkNotificationAsRead marca uma notificação como lida
func (s *NotificationService) MarkNotificationAsRead(notificationID int64) error {
	return s.repo.MarkNotificationAsRead(notificationID)
}

// GetUserNotifications obtém as notificações de um usuário
func (s *NotificationService) GetUserNotifications(userID int64, limit, offset int) ([]models.Notification, error) {
	return s.repo.GetNotificationsByUserID(userID, limit, offset)
}

// GerarMensagemNotificacao cria uma mensagem de notificação baseada na ação
func GerarMensagemNotificacao(acao, nomeOrdem, nomeUsuario string) (string, string) {
	var titulo, mensagem string

	switch acao {
	case "nova_ordem":
		titulo = "Nova Ordem de Serviço"
		mensagem = fmt.Sprintf("Você foi designado para a ordem '%s'", nomeOrdem)

	case "comentario":
		titulo = "Novo Comentário"
		mensagem = fmt.Sprintf("%s comentou na ordem '%s'", nomeUsuario, nomeOrdem)

	case "aprovacao":
		titulo = "Ordem Aprovada"
		mensagem = fmt.Sprintf("A ordem '%s' foi aprovada", nomeOrdem)

	case "rejeicao":
		titulo = "Ordem Rejeitada"
		mensagem = fmt.Sprintf("A ordem '%s' foi rejeitada", nomeOrdem)

	case "alteracao_status":
		titulo = "Status Alterado"
		mensagem = fmt.Sprintf("O status da ordem '%s' foi alterado", nomeOrdem)

	case "documento_enviado":
		titulo = "Documento Enviado"
		mensagem = fmt.Sprintf("%s enviou um documento na ordem '%s'", nomeUsuario, nomeOrdem)

	case "documento_solicitado":
		titulo = "Documento Solicitado"
		mensagem = fmt.Sprintf("%s solicitou um documento na ordem '%s'", nomeUsuario, nomeOrdem)

	default:
		titulo = "Atualização da Ordem"
		mensagem = fmt.Sprintf("A ordem '%s' foi atualizada", nomeOrdem)
	}

	return titulo, mensagem
}

// AddClient adiciona um cliente WebSocket à lista de clientes conectados
func (s *NotificationService) AddClient(client *models.WebSocketClient) {
	s.clients[client] = true
	log.Printf("Cliente WebSocket adicionado: UserID=%d, Role=%s", client.UserID, client.Role)
}

// RemoveClient remove um cliente WebSocket da lista de clientes conectados
func (s *NotificationService) RemoveClient(client *models.WebSocketClient) {
	if _, ok := s.clients[client]; ok {
		delete(s.clients, client)
		close(client.Send)
		log.Printf("Cliente WebSocket removido: UserID=%d, Role=%s", client.UserID, client.Role)
	}
}

// BroadcastToUser envia uma mensagem para todos os clientes WebSocket de um usuário específico
func (s *NotificationService) BroadcastToUser(userID int64, message []byte) {
	for client := range s.clients {
		if client.UserID == userID {
			select {
			case client.Send <- message:
				// Mensagem enviada com sucesso
			default:
				// Se o canal estiver cheio ou fechado, remover o cliente
				s.RemoveClient(client)
			}
		}
	}
}

// BroadcastToRole envia uma mensagem para todos os clientes WebSocket com determinado papel
func (s *NotificationService) BroadcastToRole(role string, message []byte) {
	for client := range s.clients {
		if string(client.Role) == role {
			select {
			case client.Send <- message:
				// Mensagem enviada com sucesso
			default:
				// Se o canal estiver cheio ou fechado, remover o cliente
				s.RemoveClient(client)
			}
		}
	}
}

// BroadcastAll envia uma mensagem para todos os clientes WebSocket conectados
func (s *NotificationService) BroadcastAll(message []byte) {
	for client := range s.clients {
		select {
		case client.Send <- message:
			// Mensagem enviada com sucesso
		default:
			// Se o canal estiver cheio ou fechado, remover o cliente
			s.RemoveClient(client)
		}
	}
}

// NotifyOrderCreated notifica automaticamente prestadores ou técnicos sobre uma nova ordem criada
func (s *NotificationService) NotifyOrderCreated(order *models.MaintenanceOrder) error {
	log.Printf("Iniciando notificação para ordem criada: ID=%d", order.GetIDAsUint())
	
	// Determinar destinatário automaticamente (prestador OU técnico)
	var destinatarioID uint
	var tipoDestinatario string
	var titulo, mensagem string
	
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		// Ordem atribuída a prestador
		destinatarioID = *order.ServiceProviderID
		tipoDestinatario = "prestador"
		titulo = "Nova Ordem de Serviço Atribuída"
		mensagem = fmt.Sprintf("Você foi designado para uma nova ordem de manutenção. Equipamento: %s", 
			s.getEquipmentName(order))
	} else if order.TechnicianID != nil && *order.TechnicianID > 0 {
		// Ordem atribuída a técnico interno
		destinatarioID = *order.TechnicianID
		tipoDestinatario = "técnico"
		titulo = "Nova Ordem de Manutenção Atribuída"
		mensagem = fmt.Sprintf("Uma nova ordem de manutenção foi atribuída a você. Equipamento: %s", 
			s.getEquipmentName(order))
	} else {
		// Ordem criada mas não atribuída ainda - notificar gerentes
		log.Printf("Ordem %d criada sem atribuição específica, notificando gerentes", order.GetIDAsUint())
		titulo = "Nova Ordem de Manutenção Criada"
		mensagem = "Uma nova ordem de manutenção foi criada e aguarda atribuição"
		return s.EnviarNotificacaoPorPapel(string(models.RoleGerente), titulo, mensagem, order.GetIDAsInt64(), 
			fmt.Sprintf("/orders/%d", order.GetIDAsUint()))
	}
	
	url := fmt.Sprintf("/orders/%d", order.GetIDAsUint())
	
	// Enviar notificação para o destinatário específico
	err := s.EnviarNotificacao(int64(destinatarioID), titulo, mensagem, order.GetIDAsInt64(), url)
	if err != nil {
		log.Printf("Erro ao enviar notificação para %s ID=%d: %v", tipoDestinatario, destinatarioID, err)
		return err
	}
	
	// Enviar via WebSocket em tempo real
	s.sendWebSocketNotification(destinatarioID, titulo, mensagem, order)
	
	log.Printf("Notificação enviada com sucesso para %s ID=%d sobre ordem %d", 
		tipoDestinatario, destinatarioID, order.GetIDAsUint())
	
	return nil
}

// NotifyOrderAssigned notifica sobre atribuição de ordem a prestador ou técnico
func (s *NotificationService) NotifyOrderAssigned(orderId uint, assignedTo uint, assignedType string) error {
	log.Printf("Notificando atribuição: Ordem=%d, Atribuído para=%d, Tipo=%s", orderId, assignedTo, assignedType)
	
	// Buscar detalhes da ordem
	ordem, err := s.orderRepo.GetOrderByID(orderId)
	if err != nil {
		log.Printf("Erro ao buscar ordem %d para notificação de atribuição: %v", orderId, err)
		return fmt.Errorf("erro ao buscar detalhes da ordem: %w", err)
	}
	
	var titulo, mensagem string
	
	switch assignedType {
	case "prestador", "provider":
		titulo = "Ordem de Serviço Atribuída"
		mensagem = fmt.Sprintf("Uma ordem de manutenção foi atribuída à sua empresa. Equipamento: %s, Filial: %s", 
			s.getEquipmentName(ordem), s.getBranchName(ordem))
	case "técnico", "technician":
		titulo = "Ordem de Manutenção Atribuída"
		mensagem = fmt.Sprintf("Uma nova ordem foi atribuída a você. Equipamento: %s, Filial: %s", 
			s.getEquipmentName(ordem), s.getBranchName(ordem))
	default:
		titulo = "Nova Atribuição"
		mensagem = fmt.Sprintf("Uma ordem foi atribuída a você. Equipamento: %s", s.getEquipmentName(ordem))
	}
	
	url := fmt.Sprintf("/orders/%d", orderId)
	
	// Enviar notificação push e salvar no banco
	err = s.EnviarNotificacao(int64(assignedTo), titulo, mensagem, int64(orderId), url)
	if err != nil {
		log.Printf("Erro ao enviar notificação de atribuição: %v", err)
		return err
	}
	
	// Enviar via WebSocket
	s.sendWebSocketNotification(assignedTo, titulo, mensagem, ordem)
	
	// Notificar gerentes sobre a atribuição
	tituloGerente := "Ordem Atribuída"
	mensagemGerente := fmt.Sprintf("Ordem %d foi atribuída a %s", orderId, assignedType)
	s.EnviarNotificacaoPorPapel(string(models.RoleGerente), tituloGerente, mensagemGerente, 
		int64(orderId), url)
	
	log.Printf("Notificação de atribuição enviada com sucesso")
	return nil
}

// NotifyStatusChanged notifica sobre mudanças de status da ordem
func (s *NotificationService) NotifyStatusChanged(order *models.MaintenanceOrder, oldStatus, newStatus string) error {
	log.Printf("Notificando mudança de status: Ordem=%d, Status: %s -> %s", 
		order.GetIDAsUint(), oldStatus, newStatus)
	
	titulo := "Status da Ordem Atualizado"
	mensagem := fmt.Sprintf("O status da ordem foi alterado de '%s' para '%s'", oldStatus, newStatus)
	url := fmt.Sprintf("/orders/%d", order.GetIDAsUint())
	
	// Notificar o responsável pela ordem (prestador ou técnico)
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		err := s.EnviarNotificacao(int64(*order.ServiceProviderID), titulo, mensagem, 
			order.GetIDAsInt64(), url)
		if err != nil {
			log.Printf("Erro ao notificar prestador sobre mudança de status: %v", err)
		}
		s.sendWebSocketNotification(*order.ServiceProviderID, titulo, mensagem, order)
	}
	
	if order.TechnicianID != nil && *order.TechnicianID > 0 {
		err := s.EnviarNotificacao(int64(*order.TechnicianID), titulo, mensagem, 
			order.GetIDAsInt64(), url)
		if err != nil {
			log.Printf("Erro ao notificar técnico sobre mudança de status: %v", err)
		}
		s.sendWebSocketNotification(*order.TechnicianID, titulo, mensagem, order)
	}
	
	// Notificar gerentes sobre mudanças importantes
	if s.isImportantStatusChange(newStatus) {
		tituloGerente := "Atualização Importante de Ordem"
		mensagemGerente := fmt.Sprintf("Ordem %d teve status alterado para '%s'", 
			order.GetIDAsUint(), newStatus)
		s.EnviarNotificacaoPorPapel(string(models.RoleGerente), tituloGerente, mensagemGerente, 
			order.GetIDAsInt64(), url)
	}
	
	return nil
}

// NotifyNewMaintenanceOrder notifica técnicos/prestadores sobre uma nova ordem (mantido para compatibilidade)
func (s *NotificationService) NotifyNewMaintenanceOrder(ctx context.Context, order *models.MaintenanceOrder) error {
	// Redirecionar para o novo método NotifyOrderCreated
	return s.NotifyOrderCreated(order)
}

// NotifyOrderStatusChange notifica sobre mudanças no status da ordem
func (s *NotificationService) NotifyOrderStatusChange(ctx context.Context, order *models.MaintenanceOrder) error {
	titulo := "Atualização de Ordem de Manutenção"
	mensagem := fmt.Sprintf("A ordem de manutenção teve seu status alterado para: %s", order.Status)
	url := fmt.Sprintf("/calendar/order/%d", order.GetIDAsUint())

	// Notificar gerentes
	return s.EnviarNotificacaoPorPapel(string(models.RoleGerente), titulo, mensagem, order.GetIDAsInt64(), url)
}

// NotifyPaymentStatusChange notifica sobre mudanças no status de pagamento
func (s *NotificationService) NotifyPaymentStatusChange(ctx context.Context, order *models.MaintenanceOrder) error {
	titulo := "Atualização de Pagamento"
	mensagem := "O status de pagamento da ordem de manutenção foi atualizado"
	url := fmt.Sprintf("/calendar/order/%d", order.GetIDAsUint())

	// Notificar financeiro
	return s.EnviarNotificacaoPorPapel(string(models.RoleFinanceiro), titulo, mensagem, order.GetIDAsInt64(), url)
}

// CreateNotification cria uma nova notificação
func (s *NotificationService) CreateNotification(notification *models.Notification) error {
	return s.repo.SaveNotification(*notification)
}

// GetNotifications retorna notificações de um usuário
func (s *NotificationService) GetNotifications(userID uint) ([]models.Notification, error) {
	return s.repo.GetNotificationsByUserID(int64(userID), 100, 0)
}

// MarkAsRead marca uma notificação como lida
func (s *NotificationService) MarkAsRead(notificationID uint) error {
	return s.repo.MarkNotificationAsRead(int64(notificationID))
}

// GetUnreadCount retorna o número de notificações não lidas
func (s *NotificationService) GetUnreadCount(userID uint) (int64, error) {
	count, err := s.repo.CountUnreadNotifications(int64(userID))
	return int64(count), err
}

// MarkAllAsRead marca todas as notificações de um usuário como lidas
func (s *NotificationService) MarkAllAsRead(userID uint) error {
	// Usar o método MarkAllNotificationsAsRead do repositório
	err := s.repo.MarkAllNotificationsAsRead(int64(userID))
	if err != nil {
		log.Printf("Erro ao marcar todas as notificações do usuário %d como lidas: %v", userID, err)

		// Implementação alternativa caso o método acima falhe
		log.Printf("Usando implementação alternativa para marcar notificações como lidas")
		notifications, err := s.GetNotifications(userID)
		if err != nil {
			return err
		}

		for _, notification := range notifications {
			if !notification.Read {
				if err := s.MarkAsRead(notification.ID); err != nil {
					log.Printf("Erro ao marcar notificação %d como lida: %v", notification.ID, err)
					// Continuar mesmo com erro
				}
			}
		}
		return nil
	}

	return nil
}

// sendWebSocketNotification envia notificação via WebSocket em tempo real
func (s *NotificationService) sendWebSocketNotification(userID uint, titulo, mensagem string, order *models.MaintenanceOrder) {
	// Preparar mensagem WebSocket
	wsMessage := map[string]interface{}{
		"type":      "notification",
		"title":     titulo,
		"message":   mensagem,
		"order_id":  order.GetIDAsUint(),
		"timestamp": time.Now().Unix(),
		"url":       fmt.Sprintf("/orders/%d", order.GetIDAsUint()),
	}
	
	messageBytes, err := json.Marshal(wsMessage)
	if err != nil {
		log.Printf("Erro ao serializar mensagem WebSocket: %v", err)
		return
	}
	
	// Enviar para o usuário específico
	s.BroadcastToUser(int64(userID), messageBytes)
	
	log.Printf("Notificação WebSocket enviada para usuário %d", userID)
}

// validateVAPIDConfiguration valida se as configurações VAPID estão corretas
func (s *NotificationService) ValidateVAPIDConfiguration() error {
	if s.vapidPublicKey == "" {
		return fmt.Errorf("VAPID public key não configurada")
	}
	
	if s.vapidPrivateKey == "" {
		return fmt.Errorf("VAPID private key não configurada")
	}
	
	if s.vapidSubject == "" {
		return fmt.Errorf("VAPID subject não configurado")
	}
	
	log.Printf("Configuração VAPID validada com sucesso")
	return nil
}

// hasActivePushSubscription verifica se o usuário tem assinatura push ativa
func (s *NotificationService) hasActivePushSubscription(userID int64) bool {
	subscriptions, err := s.repo.GetSubscriptionsByUserID(userID)
	if err != nil {
		log.Printf("Erro ao verificar assinaturas do usuário %d: %v", userID, err)
		return false
	}
	
	return len(subscriptions) > 0
}

// getEquipmentName retorna o nome do equipamento da ordem (helper method)
func (s *NotificationService) getEquipmentName(order *models.MaintenanceOrder) string {
	// Implementação básica - pode ser expandida para buscar nome real do equipamento
	if order.EquipmentID != 0 {
		return fmt.Sprintf("Equipamento #%d", order.EquipmentID)
	}
	return "Equipamento não especificado"
}

// getBranchName retorna o nome da filial da ordem (helper method)
func (s *NotificationService) getBranchName(order *models.MaintenanceOrder) string {
	// Implementação básica - pode ser expandida para buscar nome real da filial
	if order.BranchID != 0 {
		return fmt.Sprintf("Filial #%d", order.BranchID)
	}
	return "Filial não especificada"
}

// isImportantStatusChange verifica se a mudança de status é importante para gerentes
func (s *NotificationService) isImportantStatusChange(status string) bool {
	importantStatuses := []string{
		"completed", "concluída", "finalizada",
		"cancelled", "cancelada",
		"rejected", "rejeitada",
		"approved", "aprovada",
	}
	
	for _, importantStatus := range importantStatuses {
		if status == importantStatus {
			return true
		}
	}
	
	return false
}

// BroadcastToFilial envia mensagem para todos os usuários de uma filial específica
func (s *NotificationService) BroadcastToFilial(filialID uint, message []byte) {
	// Implementação para enviar notificações por filial
	// Pode ser expandida quando informações de filial estiverem disponíveis nos clientes WebSocket
	for client := range s.clients {
		// Por enquanto, enviar para todos os clientes
		// TODO: Implementar filtro por filial quando disponível
		select {
		case client.Send <- message:
			// Mensagem enviada com sucesso
		default:
			// Se o canal estiver cheio ou fechado, remover o cliente
			s.RemoveClient(client)
		}
	}
}

// GetNotificationStats retorna estatísticas de notificações para debugging
func (s *NotificationService) GetNotificationStats() map[string]interface{} {
	stats := map[string]interface{}{
		"connected_clients":    len(s.clients),
		"vapid_configured":     s.vapidPublicKey != "" && s.vapidPrivateKey != "",
		"service_initialized":  true,
		"timestamp":           time.Now().Unix(),
	}
	
	// Contar clientes por role
	roleCount := make(map[string]int)
	for client := range s.clients {
		roleCount[string(client.Role)]++
	}
	stats["clients_by_role"] = roleCount
	
	return stats
}

// NotifyOrderCompletion notifica sobre conclusão de ordem
func (s *NotificationService) NotifyOrderCompletion(order *models.MaintenanceOrder) error {
	titulo := "Ordem de Manutenção Concluída"
	mensagem := fmt.Sprintf("A ordem de manutenção foi marcada como concluída")
	url := fmt.Sprintf("/orders/%d", order.GetIDAsUint())
	
	// Notificar gerentes e financeiro
	s.EnviarNotificacaoPorPapel(string(models.RoleGerente), titulo, mensagem, 
		order.GetIDAsInt64(), url)
	s.EnviarNotificacaoPorPapel(string(models.RoleFinanceiro), titulo, mensagem, 
		order.GetIDAsInt64(), url)
	
	log.Printf("Notificação de conclusão enviada para ordem %d", order.GetIDAsUint())
	return nil
}

// NotifyOrderCancellation notifica sobre cancelamento de ordem
func (s *NotificationService) NotifyOrderCancellation(order *models.MaintenanceOrder, reason string) error {
	titulo := "Ordem de Manutenção Cancelada"
	mensagem := fmt.Sprintf("A ordem de manutenção foi cancelada. Motivo: %s", reason)
	url := fmt.Sprintf("/orders/%d", order.GetIDAsUint())
	
	// Notificar o responsável pela ordem
	if order.ServiceProviderID != nil && *order.ServiceProviderID > 0 {
		s.EnviarNotificacao(int64(*order.ServiceProviderID), titulo, mensagem, 
			order.GetIDAsInt64(), url)
		s.sendWebSocketNotification(*order.ServiceProviderID, titulo, mensagem, order)
	}
	
	if order.TechnicianID != nil && *order.TechnicianID > 0 {
		s.EnviarNotificacao(int64(*order.TechnicianID), titulo, mensagem, 
			order.GetIDAsInt64(), url)
		s.sendWebSocketNotification(*order.TechnicianID, titulo, mensagem, order)
	}
	
	// Notificar gerentes
	s.EnviarNotificacaoPorPapel(string(models.RoleGerente), titulo, mensagem, 
		order.GetIDAsInt64(), url)
	
	log.Printf("Notificação de cancelamento enviada para ordem %d", order.GetIDAsUint())
	return nil
}
