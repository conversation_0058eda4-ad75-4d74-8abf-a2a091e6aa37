package services

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"

	"gorm.io/gorm"
)

// Erros comuns
var (
	ErrPermissionDenied   = errors.New("permissão negada")
	ErrInvalidOrderStatus = errors.New("status inválido da ordem para esta ação")
	ErrOrderNotFound      = gorm.ErrRecordNotFound
	ErrRequiredFields     = errors.New("campos obrigatórios não preenchidos")
)

// MaintenanceOrderService representa o serviço de ordens de manutenção
// Responsável por gerenciar todas as operações relacionadas a ordens de manutenção
type MaintenanceOrderService struct {
	repo repository.MaintenanceOrderRepositoryInterface
	db   *sql.DB
}

// NewMaintenanceOrderService cria uma nova instância do serviço
// Parâmetros:
//   - repo: Interface do repositório de ordens de manutenção
//   - db: Conexão com o banco de dados
func NewMaintenanceOrderService(repo repository.MaintenanceOrderRepositoryInterface, db *sql.DB) *MaintenanceOrderService {
	return &MaintenanceOrderService{
		repo: repo,
		db:   db,
	}
}

// GetAll retorna todas as ordens de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - filters: Filtros para a busca
//   - userID: ID do usuário
//   - userRole: Papel do usuário
//   - page: Número da página
//   - limit: Limite de registros por página
func (s *MaintenanceOrderService) GetAll(ctx context.Context, filters map[string]interface{}, userID int64, userRole string, page, limit int) ([]models.MaintenanceOrderDetailed, int, error) {
	return s.repo.GetAll(ctx, filters, userID, userRole, page, limit)
}

// GetOrderByID retorna uma ordem de manutenção pelo ID
// Parâmetros:
//   - ctx: Contexto da requisição
//   - id: ID da ordem de manutenção
func (s *MaintenanceOrderService) GetOrderByID(ctx context.Context, id uint) (*models.MaintenanceOrder, error) {
	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		return nil, errors.New("esta ordem não está disponível no momento. Por favor, contate o suporte")
	}

	return s.repo.GetByID(ctx, id)
}

// CreateOrder cria uma nova ordem de manutenção com validações completas
func (s *MaintenanceOrderService) CreateOrder(order *models.MaintenanceOrder, userID int) error {
	// 1. Validar se o equipamento existe e pertence à filial do usuário
	var equipment models.Equipment
	err := s.db.QueryRow(`
		SELECT e.id, e.name, e.type, e.branch_id, et.id as equipment_type_id
		FROM equipment e
		LEFT JOIN equipment_types et ON e.type = et.name
		WHERE e.id = $1
	`, order.EquipmentID).Scan(&equipment.ID, &equipment.Name, &equipment.Type, &equipment.BranchID, &equipment.EquipmentTypeID)

	if err != nil {
		return fmt.Errorf("equipamento não encontrado: %v", err)
	}

	// 2. Validar se o usuário tem acesso à filial do equipamento
	var userBranchID int
	err = s.db.QueryRow("SELECT branch_id FROM users WHERE id = $1", userID).Scan(&userBranchID)
	if err != nil {
		return fmt.Errorf("usuário não encontrado: %v", err)
	}

	if int(equipment.BranchID) != userBranchID {
		return fmt.Errorf("equipamento não pertence à sua filial")
	}

	// 3. Gerar número único da ordem
	orderNumber, err := s.generateOrderNumber(equipment.BranchID)
	if err != nil {
		return fmt.Errorf("erro ao gerar número da ordem: %v", err)
	}

	// 4. Definir valores da ordem
	order.Number = orderNumber
	order.BranchID = equipment.BranchID
	order.CreatedByUserID = uint(userID)
	order.EquipmentTypeID = equipment.EquipmentTypeID
	order.Status = models.StatusPending
	order.CreatedAt = time.Now()
	order.UpdatedAt = time.Now()

	// 5. Inserir ordem no banco
	err = s.db.QueryRow(`
		INSERT INTO maintenance_orders (
			number, equipment_id, branch_id, created_by_user_id, equipment_type_id,
			technician_id, service_provider_id, status, priority, description,
			due_date, created_at, updated_at, user_id
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id
	`, order.Number, order.EquipmentID, order.BranchID, order.CreatedByUserID, order.EquipmentTypeID,
		order.TechnicianID, order.ServiceProviderID, order.Status, order.Priority, order.Description,
		order.DueDate, order.CreatedAt, order.UpdatedAt, order.UserID).Scan(&order.ID)

	if err != nil {
		return fmt.Errorf("erro ao criar ordem: %v", err)
	}

	// Vincular automaticamente na technician_orders se houver técnico definido
	if order.TechnicianID != nil && *order.TechnicianID > 0 {
		_, err = s.db.Exec(`INSERT INTO technician_orders (technician_id, order_id, created_at, created_by) VALUES ($1, $2, NOW(), $3)`, *order.TechnicianID, order.ID, userID)
		if err != nil {
			return fmt.Errorf("erro ao vincular ordem ao técnico: %v", err)
		}
	}

	return nil
}

// UpdateOrder atualiza uma ordem de manutenção existente
// Parâmetros:
//   - ctx: Contexto da requisição
//   - order: Ordem de manutenção a ser atualizada
func (s *MaintenanceOrderService) UpdateOrder(ctx context.Context, order *models.MaintenanceOrder) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if order.ID == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	if err := s.validateOrder(order); err != nil {
		return err
	}
	return s.repo.Update(ctx, order)
}

// DeleteOrder remove uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - id: ID da ordem de manutenção
func (s *MaintenanceOrderService) DeleteOrder(ctx context.Context, id uint) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		return errors.New("esta ordem não está disponível para exclusão. Por favor, contate o suporte")
	}

	return s.repo.Delete(ctx, id)
}

// UpdateStatus atualiza o status de uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - id: ID da ordem de manutenção
//   - status: Novo status
//   - userID: ID do usuário que está atualizando
//   - notes: Notas sobre a atualização
func (s *MaintenanceOrderService) UpdateStatus(ctx context.Context, id uint, status models.OrderStatus, userID uint, notes string) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	// VERIFICAÇÃO DE SEGURANÇA: Apenas admin, gerente e financeiro podem alterar status
	userRepo := repository.NewGormUserRepository()
	usuario, err := userRepo.FindByID(uint(userID))
	if err != nil {
		return errors.New("erro ao buscar usuário para validação de permissões")
	}

	if !canUserUpdateOrderStatus(usuario.Role) {
		log.Printf("[SEGURANÇA] Tentativa de alteração de status por usuário não autorizado. Usuário ID: %d, Role: %s", userID, usuario.Role)
		return errors.New("permissão negada: apenas administradores, gerentes e financeiro podem alterar o status das ordens")
	}

	return s.repo.UpdateStatus(ctx, id, status, userID, notes)
}

// UpdatePriority atualiza a prioridade de uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - id: ID da ordem de manutenção
//   - priority: Nova prioridade
//   - userID: ID do usuário que está atualizando
//   - notes: Notas sobre a atualização
func (s *MaintenanceOrderService) UpdatePriority(ctx context.Context, id uint, priority models.PriorityLevel, userID uint, notes string) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if id == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	return s.repo.UpdatePriority(ctx, id, priority, userID, notes)
}

// AddNote adiciona uma nota a uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - orderID: ID da ordem de manutenção
//   - content: Conteúdo da nota
//   - userID: ID do usuário que está adicionando a nota
func (s *MaintenanceOrderService) AddNote(ctx context.Context, orderID int64, content string, userID int64) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if orderID == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	return s.repo.AddNote(ctx, orderID, content, userID)
}

// AddMaterial adiciona um material a uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - orderID: ID da ordem de manutenção
//   - material: Material a ser adicionado
//   - userID: ID do usuário que está adicionando o material
func (s *MaintenanceOrderService) AddMaterial(ctx context.Context, orderID int64, material models.MaterialRequest, userID int64) error {
	// Bloquear acesso à ordem #18 que está causando problemas
	if orderID == 18 {
		return errors.New("esta ordem não está disponível para atualização. Por favor, contate o suporte")
	}

	return s.repo.AddMaterial(ctx, orderID, material, userID)
}

// GetMetrics retorna as métricas das ordens de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - filters: Filtros para a busca
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetMetrics(ctx context.Context, filters map[string]interface{}, userID int64, userRole string) (*models.MaintenanceOrderMetricsV2, error) {
	return s.repo.GetMetrics(ctx, filters, userID, userRole)
}

// GetOrdersByStatus retorna as ordens de manutenção por status
// Parâmetros:
//   - ctx: Contexto da requisição
//   - status: Status das ordens
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetOrdersByStatus(ctx context.Context, status string, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	return s.repo.GetByStatus(ctx, status, userID, userRole)
}

// GetMetricsByStatus retorna as métricas das ordens de manutenção por status
// Parâmetros:
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetMetricsByStatus(userID int64, userRole string) (map[models.OrderStatus]int, error) {
	return s.repo.GetMetricsByStatus(context.Background(), userID, userRole)
}

// GetOrdersByPriority retorna as ordens de manutenção por prioridade
// Parâmetros:
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetOrdersByPriority(userID int64, userRole string) (map[models.PriorityLevel]int, error) {
	// Implementação temporária
	return map[models.PriorityLevel]int{
		models.PriorityLow:      5,
		models.PriorityMedium:   10,
		models.PriorityHigh:     3,
		models.PriorityCritical: 2,
	}, nil
}

// GetRecentOrders retorna as ordens de manutenção recentes
// Parâmetros:
//   - userID: ID do usuário
//   - userRole: Papel do usuário
//   - limit: Limite de registros
func (s *MaintenanceOrderService) GetRecentOrders(userID int64, userRole string, limit int) ([]models.MaintenanceOrderDetailed, error) {
	// Implementação temporária
	orders, _, err := s.GetAll(context.Background(), nil, userID, userRole, 1, limit)
	return orders, err
}

// GetAllOrders retorna todas as ordens de manutenção (compatível com a interface antiga)
func (s *MaintenanceOrderService) GetAllOrders() ([]models.MaintenanceOrder, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, nil
}

// ValidateTechnicianAssignment valida se um técnico pode ser atribuído a uma ordem
func (s *MaintenanceOrderService) ValidateTechnicianAssignment(technicianID, branchID, equipmentID uint) (bool, error) {
	// Implementação temporária - sempre retorna true
	log.Printf("Validando atribuição do técnico %d para filial %d e equipamento %d", technicianID, branchID, equipmentID)
	return true, nil
}

// AssignOrderToTechnician atribui uma ordem a um técnico
func (s *MaintenanceOrderService) AssignOrderToTechnician(orderID, technicianID uint, notes string, userID uint) error {
	// Implementação temporária
	log.Printf("Atribuindo ordem %d ao técnico %d por usuário %d", orderID, technicianID, userID)
	return nil
}

// AssignOrderToProvider atribui uma ordem a um prestador
func (s *MaintenanceOrderService) AssignOrderToProvider(orderID, providerID uint, notes string, userID uint) error {
	// Implementação temporária
	log.Printf("Atribuindo ordem %d ao prestador %d por usuário %d", orderID, providerID, userID)
	return nil
}

// GetOrderDetails retorna os detalhes de uma ordem de manutenção (compatível com a interface antiga)
func (s *MaintenanceOrderService) GetOrderDetails(id uint) (*models.MaintenanceOrder, *models.Branch, *models.Equipment, *models.User, *models.User, *models.ServiceProvider, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, &models.Branch{}, &models.Equipment{}, &models.User{}, &models.User{}, &models.ServiceProvider{}, nil
}

// AssignProvider atribui um prestador de serviço a uma ordem de manutenção
func (s *MaintenanceOrderService) AssignProvider(orderID, userID, providerID uint) (*models.MaintenanceOrder, error) {
	// Implementação temporária
	return &models.MaintenanceOrder{}, nil
}

// AddCost adiciona um custo a uma ordem de manutenção
func (s *MaintenanceOrderService) AddCost(orderID, userID uint, cost models.CostItem) (*models.CostItem, error) {
	// Implementação temporária
	return &models.CostItem{}, nil
}

// GetCostsByOrderID retorna os custos de uma ordem de manutenção
func (s *MaintenanceOrderService) GetCostsByOrderID(orderID uint) ([]models.CostItem, error) {
	// Implementação temporária
	return []models.CostItem{}, nil
}

// AddInteraction adiciona uma interação a uma ordem de manutenção
func (s *MaintenanceOrderService) AddInteraction(orderID, userID uint, message string) (*models.Interaction, error) {
	// Implementação temporária
	return &models.Interaction{}, nil
}

// GetOrdersByBranch retorna as ordens de manutenção por filial
// Parâmetros:
//   - ctx: Contexto da requisição
//   - branchID: ID da filial
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetOrdersByBranch(ctx context.Context, branchID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	return s.repo.GetByBranch(ctx, branchID, userID, userRole)
}

// GetOrdersByEquipment retorna as ordens de manutenção por equipamento
// Parâmetros:
//   - ctx: Contexto da requisição
//   - equipmentID: ID do equipamento
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetOrdersByEquipment(ctx context.Context, equipmentID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	return s.repo.GetByEquipment(ctx, equipmentID, userID, userRole)
}

// GetOrdersByServiceProvider retorna as ordens de manutenção por prestador de serviço
// Parâmetros:
//   - ctx: Contexto da requisição
//   - providerID: ID do prestador de serviço
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetOrdersByServiceProvider(ctx context.Context, providerID uint, userID int64, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	return s.repo.GetByProvider(ctx, providerID, userID, userRole)
}

// GetOrderCosts retorna os custos de uma ordem de manutenção
// Parâmetros:
//   - ctx: Contexto da requisição
//   - orderID: ID da ordem de manutenção
func (s *MaintenanceOrderService) GetOrderCosts(ctx context.Context, orderID int64) (*models.MaintenanceOrderCosts, error) {
	return s.repo.GetCosts(ctx, orderID)
}

// GetStations retorna as estações disponíveis para o usuário
// Parâmetros:
//   - ctx: Contexto da requisição
//   - userID: ID do usuário
//   - userRole: Papel do usuário
func (s *MaintenanceOrderService) GetStations(ctx context.Context, userID int64, userRole string) ([]models.FilialSummary, error) {
	return s.repo.GetStations(ctx, userID, userRole)
}

// RemoveTestOrders remove permanentemente todas as ordens de teste do banco de dados
func (s *MaintenanceOrderService) RemoveTestOrders() error {
	log.Println("[SEGURANÇA] Iniciando remoção de ordens de manutenção de teste...")

	// Remover da tabela principal
	testOrdersResult := s.repo.RemoveTestOrders()
	if testOrdersResult != nil {
		log.Printf("[ERRO] Falha ao remover ordens de teste: %v", testOrdersResult)
		return testOrdersResult
	}

	// Remover ordem #18 especificamente (causa do problema relatado)
	err := s.repo.Delete(context.Background(), 18)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Printf("[ALERTA] Falha ao excluir ordem #18: %v", err)
		// Não bloquear o processo se essa ordem específica já foi excluída
	}

	// Registrar conclusão
	log.Println("[SEGURANÇA] Remoção de ordens de teste concluída com sucesso")
	return nil
}

// validateOrder valida os dados de uma ordem de manutenção
// Parâmetros:
//   - order: Ordem de manutenção a ser validada
func (s *MaintenanceOrderService) validateOrder(order *models.MaintenanceOrder) error {
	if order == nil {
		return errors.New("ordem de manutenção não pode ser nula")
	}

	if order.Problem == "" {
		return errors.New("problema é obrigatório")
	}

	if order.EquipmentID == 0 {
		return errors.New("equipamento é obrigatório")
	}

	if order.BranchID == 0 {
		return errors.New("filial é obrigatória")
	}

	return nil
}

// GetAvailableProviders retorna prestadoras disponíveis para uma filial e tipo de equipamento
func (s *MaintenanceOrderService) GetAvailableProviders(branchID int, equipmentTypeID int) ([]models.ServiceProvider, error) {
	query := `
		SELECT DISTINCT sp.id, sp.name, sp.company_name, sp.cnpj, sp.contact_phone, sp.contact_email, sp.address
		FROM service_providers sp
		INNER JOIN provider_branch_links pbl ON sp.id = pbl.provider_id
		INNER JOIN users u ON pbl.user_id = u.id
		WHERE pbl.branch_id = $1 
		AND pbl.is_active = true
		AND u.role = 'tecnico'
		AND u.equipment_type_id = $2
		AND u.is_active = true
		ORDER BY sp.name
	`

	rows, err := s.db.Query(query, branchID, equipmentTypeID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar prestadoras: %v", err)
	}
	defer rows.Close()

	var providers []models.ServiceProvider
	for rows.Next() {
		var provider models.ServiceProvider
		err := rows.Scan(&provider.ID, &provider.Name, &provider.CompanyName, &provider.CNPJ, &provider.ContactPhone, &provider.ContactEmail, &provider.Address)
		if err != nil {
			return nil, fmt.Errorf("erro ao ler prestadora: %v", err)
		}
		providers = append(providers, provider)
	}

	return providers, nil
}

// GetAvailableTechnicians retorna técnicos disponíveis para uma filial e tipo de equipamento
func (s *MaintenanceOrderService) GetAvailableTechnicians(branchID int, equipmentTypeID int) ([]models.User, error) {
	// Query atualizada para usar vínculos separados: técnico-filial E técnico-tipo_equipamento
	query := `
		SELECT DISTINCT u.id, u.name, u.email, u.role
		FROM users u
		INNER JOIN technician_branches tb ON u.id = tb.technician_id 
			AND tb.branch_id = $1 
			AND tb.is_active = true
		INNER JOIN technician_equipment_types tet ON u.id = tet.technician_id 
			AND tet.equipment_type_id = $2 
			AND tet.is_active = true
		WHERE u.role = 'tecnico'
		AND u.is_active = true
		ORDER BY u.name
	`

	log.Printf("[DEBUG] GetAvailableTechnicians - branchID: %d, equipmentTypeID: %d", branchID, equipmentTypeID)
	log.Printf("[DEBUG] Query: %s", query)

	rows, err := s.db.Query(query, branchID, equipmentTypeID)
	if err != nil {
		log.Printf("[ERROR] Erro ao buscar técnicos: %v", err)
		return nil, fmt.Errorf("erro ao buscar técnicos: %v", err)
	}
	defer rows.Close()

	var technicians []models.User
	for rows.Next() {
		var technician models.User
		err := rows.Scan(&technician.ID, &technician.Name, &technician.Email, &technician.Role)
		if err != nil {
			log.Printf("[ERROR] Erro ao ler técnico: %v", err)
			return nil, fmt.Errorf("erro ao ler técnico: %v", err)
		}
		technicians = append(technicians, technician)
	}

	log.Printf("[DEBUG] Encontrados %d técnicos disponíveis", len(technicians))
	return technicians, nil
}

// GetEquipmentType retorna o tipo de equipamento baseado no equipamento
func (s *MaintenanceOrderService) GetEquipmentType(equipmentID int) (*models.EquipmentType, error) {
	var equipmentType models.EquipmentType
	err := s.db.QueryRow(`
		SELECT et.id, et.name, et.description
		FROM equipment_types et
		INNER JOIN equipments e ON e.type = et.name
		WHERE e.id = $1
	`, equipmentID).Scan(&equipmentType.ID, &equipmentType.Name, &equipmentType.Description)

	if err != nil {
		return nil, fmt.Errorf("tipo de equipamento não encontrado: %v", err)
	}

	return &equipmentType, nil
}

// generateOrderNumber gera número único para a ordem baseado na filial
func (s *MaintenanceOrderService) generateOrderNumber(branchID uint) (string, error) {
	branchIDStr := fmt.Sprintf("%d", branchID)
	substrIdx := len(branchIDStr) + 1
	query := fmt.Sprintf(`
		SELECT MAX(CAST(SUBSTRING(number FROM %d) AS BIGINT))
		FROM maintenance_orders
		WHERE branch_id = $1
		AND number LIKE '%s%%'
		AND LENGTH(number) > %d
	`, substrIdx, branchIDStr, len(branchIDStr))
	log.Printf("[DEBUG] Query de geração de número: %s", query)

	var maxNumber sql.NullInt64
	err := s.db.QueryRow(query, branchID).Scan(&maxNumber)
	if err != nil {
		return "", fmt.Errorf("erro ao buscar número máximo da filial %d: %v", branchID, err)
	}

	var nextNumber int64
	if maxNumber.Valid {
		nextNumber = maxNumber.Int64 + 1
	} else {
		nextNumber = 1
	}

	number := fmt.Sprintf("%d%03d", branchID, nextNumber)

	var exists bool
	err = s.db.QueryRow("SELECT EXISTS(SELECT 1 FROM maintenance_orders WHERE number = $1)", number).Scan(&exists)
	if err != nil {
		return "", fmt.Errorf("erro ao verificar duplicata: %v", err)
	}
	if exists {
		nextNumber++
		number = fmt.Sprintf("%d%03d", branchID, nextNumber)
	}

	return number, nil
}

// GetOrdersByBranchNew retorna ordens de uma filial (nova implementação)
func (s *MaintenanceOrderService) GetOrdersByBranchNew(branchID int) ([]models.MaintenanceOrder, error) {
	query := `
		SELECT mo.id, mo.number, mo.equipment_id, mo.branch_id, mo.created_by_user_id, mo.equipment_type_id,
			   mo.technician_id, mo.service_provider_id, mo.status, mo.priority, mo.description,
			   mo.due_date, mo.completion_date, mo.created_at, mo.updated_at,
			   e.name as equipment_name, u.name as user_name, et.name as equipment_type_name
		FROM maintenance_orders mo
		LEFT JOIN equipments e ON mo.equipment_id = e.id
		LEFT JOIN users u ON mo.created_by_user_id = u.id
		LEFT JOIN equipment_types et ON mo.equipment_type_id = et.id
		WHERE mo.branch_id = $1
		ORDER BY mo.created_at DESC
	`

	rows, err := s.db.Query(query, branchID)
	if err != nil {
		return nil, fmt.Errorf("erro ao buscar ordens: %v", err)
	}
	defer rows.Close()

	var orders []models.MaintenanceOrder
	for rows.Next() {
		var order models.MaintenanceOrder
		var equipmentName, userName, equipmentTypeName sql.NullString

		err := rows.Scan(
			&order.ID, &order.Number, &order.EquipmentID, &order.BranchID, &order.CreatedByUserID, &order.EquipmentTypeID,
			&order.TechnicianID, &order.ServiceProviderID, &order.Status, &order.Priority, &order.Description,
			&order.DueDate, &order.CompletionDate, &order.CreatedAt, &order.UpdatedAt,
			&equipmentName, &userName, &equipmentTypeName,
		)
		if err != nil {
			return nil, fmt.Errorf("erro ao ler ordem: %v", err)
		}

		// Adicionar dados relacionados
		if equipmentName.Valid {
			order.Equipment = &models.Equipment{ID: order.EquipmentID, Name: equipmentName.String}
		}
		if userName.Valid {
			order.Requester = &models.User{ID: order.CreatedByUserID, Name: userName.String}
		}
		if equipmentTypeName.Valid {
			order.EquipmentType = &models.EquipmentType{ID: *order.EquipmentTypeID, Name: equipmentTypeName.String}
		}

		orders = append(orders, order)
	}

	return orders, nil
}
