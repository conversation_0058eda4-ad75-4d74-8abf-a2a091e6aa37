package repository

import (
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormOrderRepository implementa OrderRepository usando GORM
type GormOrderRepository struct {
	db *gorm.DB
}

// NewGormOrderRepository cria um novo repositório de ordens com GORM
func NewGormOrderRepository(db *gorm.DB) *GormOrderRepository {
	return &GormOrderRepository{db: db}
}

// GetOrderByID busca uma ordem de serviço pelo ID (método original)
func (r *GormOrderRepository) GetOrderByID(id uint) (*models.MaintenanceOrder, error) {
	var order models.MaintenanceOrder
	if err := r.db.First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// GetByID busca uma ordem pelo ID (método para o adaptador)
func (r *GormOrderRepository) GetByID(id uint) (*models.MaintenanceOrder, error) {
	var order models.MaintenanceOrder
	if err := r.db.First(&order, id).Error; err != nil {
		return nil, err
	}
	return &order, nil
}

// GetAll busca todas as ordens com paginação e filtros
func (r *GormOrderRepository) GetAll(offset, limit int, filter string) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	query := r.db

	// Aplicar filtro se fornecido
	if filter != "" {
		query = query.Where("status = ?", filter)
	}

	// Aplicar paginação
	query = query.Offset(offset).Limit(limit)

	// Executar a consulta
	if err := query.Find(&orders).Error; err != nil {
		return nil, err
	}

	return orders, nil
}

// Create cria uma nova ordem
func (r *GormOrderRepository) Create(order *models.MaintenanceOrder) error {
	return r.db.Create(order).Error
}

// Update atualiza uma ordem existente
func (r *GormOrderRepository) Update(order *models.MaintenanceOrder) error {
	return r.db.Save(order).Error
}

// Delete remove uma ordem
func (r *GormOrderRepository) Delete(id uint) error {
	return r.db.Delete(&models.MaintenanceOrder{}, id).Error
}

// GetByUserID retorna todas as ordens criadas por um usuário específico
func (r *GormOrderRepository) GetByUserID(userID uint, offset, limit int) ([]models.MaintenanceOrder, error) {
	var orders []models.MaintenanceOrder
	query := r.db.Where("created_by_user_id = ?", userID)
	query = query.Offset(offset).Limit(limit)
	if err := query.Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// GetOrderCosts retorna os custos de uma ordem
func (r *GormOrderRepository) GetOrderCosts(orderID uint) ([]models.CostItem, error) {
	// Implementação temporária
	return []models.CostItem{}, nil
}

// GetOrdersWithPagination retorna ordens com paginação e filtros
func (r *GormOrderRepository) GetOrdersWithPagination(page, pageSize int, status models.OrderStatus, branchID uint, startDate, endDate time.Time) ([]models.MaintenanceOrder, int, error) {
	// Implementação temporária
	return []models.MaintenanceOrder{}, 0, nil
}

// GetOrderCountsByStatus retorna contagem de ordens por status
func (r *GormOrderRepository) GetOrderCountsByStatus() (map[models.OrderStatus]int, error) {
	// Implementação temporária
	return map[models.OrderStatus]int{}, nil
}

// GetOrderInteractions retorna interações de uma ordem
func (r *GormOrderRepository) GetOrderInteractions(orderID uint) ([]models.Interaction, error) {
	// Implementação temporária
	return []models.Interaction{}, nil
}
