#!/bin/bash

echo "🔧 TESTE DO SISTEMA DE GERENCIAMENTO DE VÍNCULOS"
echo "================================================"
echo ""

# Verificar se o servidor está rodando
echo "1️⃣  Verificando se o servidor está ativo..."
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ Servidor está rodando"
else
    echo "❌ Servidor não está rodando. Execute: ./iniciar_rapido.sh"
    exit 1
fi

echo ""
echo "2️⃣  Testando rotas da API..."

# Testar rota de filiais
echo "   📍 Testando /api/branches..."
BRANCHES_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/branches)
if [ "$BRANCHES_RESPONSE" = "200" ] || [ "$BRANCHES_RESPONSE" = "401" ]; then
    echo "   ✅ Rota /api/branches está configurada (Status: $BRANCHES_RESPONSE)"
else
    echo "   ❌ Rota /api/branches com problema (Status: $BRANCHES_RESPONSE)"
fi

# Testar rota de técnicos
echo "   👨‍🔧 Testando /api/technicians..."
TECHNICIANS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/technicians)
if [ "$TECHNICIANS_RESPONSE" = "200" ] || [ "$TECHNICIANS_RESPONSE" = "401" ]; then
    echo "   ✅ Rota /api/technicians está configurada (Status: $TECHNICIANS_RESPONSE)"
else
    echo "   ❌ Rota /api/technicians com problema (Status: $TECHNICIANS_RESPONSE)"
fi

# Testar rota de prestadores
echo "   🏢 Testando /api/providers..."
PROVIDERS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/providers)
if [ "$PROVIDERS_RESPONSE" = "200" ] || [ "$PROVIDERS_RESPONSE" = "401" ]; then
    echo "   ✅ Rota /api/providers está configurada (Status: $PROVIDERS_RESPONSE)"
else
    echo "   ❌ Rota /api/providers com problema (Status: $PROVIDERS_RESPONSE)"
fi

# Testar rota de convites
echo "   📧 Testando /api/invites/technician..."
INVITES_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:8080/api/invites/technician)
if [ "$INVITES_RESPONSE" = "401" ] || [ "$INVITES_RESPONSE" = "400" ]; then
    echo "   ✅ Rota /api/invites/technician está configurada (Status: $INVITES_RESPONSE)"
else
    echo "   ❌ Rota /api/invites/technician com problema (Status: $INVITES_RESPONSE)"
fi

echo ""
echo "3️⃣  Verificando arquivos estáticos..."

# Verificar JavaScript
if [ -f "web/static/js/novo_link_management.js" ]; then
    echo "   ✅ Arquivo JavaScript encontrado"
else
    echo "   ❌ Arquivo JavaScript não encontrado"
fi

# Verificar favicon
if [ -f "web/static/images/favicon.svg" ]; then
    echo "   ✅ Favicon SVG encontrado"
else
    echo "   ❌ Favicon não encontrado"
fi

echo ""
echo "4️⃣  Testando página principal..."
PAGE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/admin/novo-link-management)
if [ "$PAGE_RESPONSE" = "200" ] || [ "$PAGE_RESPONSE" = "302" ] || [ "$PAGE_RESPONSE" = "401" ]; then
    echo "   ✅ Página principal acessível (Status: $PAGE_RESPONSE)"
else
    echo "   ❌ Página principal com problema (Status: $PAGE_RESPONSE)"
fi

echo ""
echo "📋 RESUMO DAS CORREÇÕES APLICADAS:"
echo "=================================="
echo "✅ Corrigida referência do arquivo JavaScript no template"
echo "✅ Criado favicon.svg para resolver erro 404"
echo "✅ Removido CDN do Tailwind CSS (não recomendado para produção)"
echo "✅ Adicionado método populateSelectors() para popular seletores"
echo "✅ Melhorado tratamento de erros no botão Convidar Técnico"
echo "✅ Adicionados logs de debug para facilitar troubleshooting"
echo ""
echo "🌐 Para testar a interface:"
echo "   1. Faça login como administrador"
echo "   2. Acesse: http://localhost:8080/admin/novo-link-management"
echo "   3. Verifique se os seletores são populados"
echo "   4. Teste o botão 'Convidar Técnico'"
echo ""
