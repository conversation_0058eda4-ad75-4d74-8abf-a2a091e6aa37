#!/bin/bash

# Architecture Compliance Checker for Sistema Tradição
# This script verifies compliance with mandatory architecture patterns
# and blocks deployment if violations are detected.

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
REPORT_FILE="${PROJECT_ROOT}/compliance_report.json"
MIN_COVERAGE=80
MIN_PERFORMANCE=90
VIOLATIONS=0
WARNINGS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
    ((WARNINGS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    ((VIOLATIONS++))
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

# Initialize compliance report
init_report() {
    cat > "$REPORT_FILE" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "project_root": "$PROJECT_ROOT",
    "compliance_score": 0,
    "violations": [],
    "warnings": [],
    "checks": {
        "duplications": {"status": "pending", "details": []},
        "mandatory_patterns": {"status": "pending", "details": []},
        "quality": {"status": "pending", "details": []},
        "structure": {"status": "pending", "details": []},
        "configuration": {"status": "pending", "details": []}
    }
}
EOF
}

# Add violation to report
add_violation() {
    local category="$1"
    local message="$2"
    local file="${3:-""}"
    
    jq --arg cat "$category" --arg msg "$message" --arg f "$file" \
       '.violations += [{"category": $cat, "message": $msg, "file": $f}]' \
       "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"
}

# Add warning to report
add_warning() {
    local category="$1"
    local message="$2"
    local file="${3:-""}"
    
    jq --arg cat "$category" --arg msg "$message" --arg f "$file" \
       '.warnings += [{"category": $cat, "message": $msg, "file": $f}]' \
       "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"
}

# Update check status
update_check_status() {
    local check="$1"
    local status="$2"
    local details="$3"
    
    jq --arg c "$check" --arg s "$status" --arg d "$details" \
       '.checks[$c].status = $s | .checks[$c].details += [$d]' \
       "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"
}

# 1. VERIFICAÇÃO DE DUPLICAÇÕES
check_duplications() {
    log_info "Checking for code duplications..."
    
    # Check for duplicate order handlers
    local order_handlers=()
    while IFS= read -r -d '' file; do
        if [[ "$file" =~ (ordem|order).*handler.*\.go$ ]] && [[ ! "$file" =~ unified_order_handler\.go$ ]]; then
            order_handlers+=("$file")
        fi
    done < <(find "$PROJECT_ROOT/internal/handlers" -name "*.go" -print0 2>/dev/null || true)
    
    if [[ ${#order_handlers[@]} -gt 0 ]]; then
        log_error "Duplicate order handlers found (violates unified architecture):"
        for handler in "${order_handlers[@]}"; do
            log_error "  - $handler"
            add_violation "duplications" "Duplicate order handler found" "$handler"
        done
    fi
    
    # Check for conflicting routes
    local conflicting_routes=()
    if grep -r "/api/ordens" "$PROJECT_ROOT/internal/routes" 2>/dev/null | grep -v "301\|redirect"; then
        log_error "Found conflicting routes using /api/ordens instead of unified /api/orders"
        conflicting_routes+=("/api/ordens")
        add_violation "duplications" "Conflicting route /api/ordens found (should use /api/orders)" ""
    fi
    
    # Check for duplicate JavaScript for orders
    local order_js_files=()
    while IFS= read -r -d '' file; do
        if [[ "$file" =~ ordem.*\.js$ ]] && [[ ! "$file" =~ unified.*\.js$ ]]; then
            order_js_files+=("$file")
        fi
    done < <(find "$PROJECT_ROOT/web/static/js" -name "*.js" -print0 2>/dev/null || true)
    
    if [[ ${#order_js_files[@]} -gt 1 ]]; then
        log_error "Multiple order JavaScript files found (should use unified approach):"
        for js_file in "${order_js_files[@]}"; do
            log_error "  - $js_file"
            add_violation "duplications" "Duplicate order JavaScript file" "$js_file"
        done
    fi
    
    # Check for hardcoded old endpoints
    local old_endpoints=("/api/ordens" "/verordens" "/api/v2/filial")
    for endpoint in "${old_endpoints[@]}"; do
        if grep -r "$endpoint" "$PROJECT_ROOT" --include="*.go" --include="*.js" --include="*.html" 2>/dev/null | grep -v "301\|redirect\|// TODO: remove"; then
            log_error "Found hardcoded old endpoint: $endpoint"
            add_violation "duplications" "Hardcoded old endpoint found" "$endpoint"
        fi
    done
    
    if [[ $VIOLATIONS -eq 0 ]]; then
        log_success "No duplications found"
        update_check_status "duplications" "passed" "No code duplications detected"
    else
        update_check_status "duplications" "failed" "Code duplications found"
    fi
}

# 2. VERIFICAÇÃO DE PADRÕES OBRIGATÓRIOS
check_mandatory_patterns() {
    log_info "Checking mandatory patterns..."
    
    # Check StandardResponse usage
    if ! grep -r "StandardResponse" "$PROJECT_ROOT/internal/handlers" 2>/dev/null | grep -q "unified_order_handler.go"; then
        log_error "unified_order_handler.go must use StandardResponse pattern"
        add_violation "mandatory_patterns" "StandardResponse not used in unified handler" "unified_order_handler.go"
    fi
    
    # Check pagination implementation
    if ! grep -r "Pagination\|PageInfo\|page_size\|page_number" "$PROJECT_ROOT/internal/handlers/unified_order_handler.go" 2>/dev/null; then
        log_warning "Pagination pattern not found in unified handler"
        add_warning "mandatory_patterns" "Pagination implementation missing" "unified_order_handler.go"
    fi
    
    # Check order #18 blocking
    if ! grep -r "order.*18\|ordem.*18" "$PROJECT_ROOT" --include="*.go" 2>/dev/null | grep -i "block\|forbidden\|deny"; then
        log_error "Order #18 blocking not implemented"
        add_violation "mandatory_patterns" "Order #18 must be blocked" ""
    fi
    
    # Check authentication middleware
    local protected_routes=("/api/orders" "/api/orders/")
    for route in "${protected_routes[@]}"; do
        if ! grep -r "$route" "$PROJECT_ROOT/internal/routes" 2>/dev/null | grep -i "auth\|middleware"; then
            log_error "Authentication middleware missing for protected route: $route"
            add_violation "mandatory_patterns" "Authentication middleware missing" "$route"
        fi
    done
    
    update_check_status "mandatory_patterns" "checked" "Mandatory patterns verification completed"
}

# 3. VERIFICAÇÃO DE QUALIDADE
check_quality() {
    log_info "Checking code quality..."
    
    # Run tests and check coverage
    if command -v go >/dev/null 2>&1; then
        cd "$PROJECT_ROOT"
        
        # Run tests
        if ! go test ./... -v 2>/dev/null; then
            log_error "Tests are failing"
            add_violation "quality" "Test suite is failing" ""
        fi
        
        # Check test coverage
        local coverage
        coverage=$(go test ./... -coverprofile=coverage.out 2>/dev/null && go tool cover -func=coverage.out | tail -1 | awk '{print $3}' | sed 's/%//' || echo "0")
        
        if (( $(echo "$coverage < $MIN_COVERAGE" | bc -l) )); then
            log_error "Test coverage ($coverage%) is below minimum required ($MIN_COVERAGE%)"
            add_violation "quality" "Insufficient test coverage: $coverage%" ""
        else
            log_success "Test coverage: $coverage%"
        fi
        
        # Clean up coverage file
        rm -f coverage.out
    else
        log_warning "Go not found, skipping test execution"
        add_warning "quality" "Go not available for testing" ""
    fi
    
    # Check for dead code (unused imports)
    if command -v goimports >/dev/null 2>&1; then
        local unused_imports
        unused_imports=$(find "$PROJECT_ROOT" -name "*.go" -exec goimports -l {} \; 2>/dev/null | wc -l)
        if [[ $unused_imports -gt 0 ]]; then
            log_warning "Found $unused_imports files with unused imports"
            add_warning "quality" "Unused imports found in $unused_imports files" ""
        fi
    fi
    
    # Check for removed handler imports
    local removed_handlers=("api_ordens" "verordens_handler" "ordens" "ordem_v2_handler")
    for handler in "${removed_handlers[@]}"; do
        if grep -r "import.*$handler\|\".*$handler\"" "$PROJECT_ROOT" --include="*.go" 2>/dev/null; then
            log_error "Found import reference to removed handler: $handler"
            add_violation "quality" "Import reference to removed handler" "$handler"
        fi
    done
    
    update_check_status "quality" "checked" "Code quality verification completed"
}

# 4. VERIFICAÇÃO DE ESTRUTURA
check_structure() {
    log_info "Checking project structure..."
    
    # Check mandatory directories
    local required_dirs=("internal/handlers" "internal/routes" "internal/services" "internal/models" "web/templates" "web/static")
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$PROJECT_ROOT/$dir" ]]; then
            log_error "Required directory missing: $dir"
            add_violation "structure" "Required directory missing" "$dir"
        fi
    done
    
    # Check file naming conventions
    local go_files
    while IFS= read -r -d '' file; do
        local basename
        basename=$(basename "$file")
        if [[ "$basename" =~ [A-Z] ]] && [[ ! "$basename" =~ ^[A-Z][A-Z_]*\.go$ ]]; then
            log_warning "File name should use snake_case: $basename"
            add_warning "structure" "File naming convention violation" "$file"
        fi
    done < <(find "$PROJECT_ROOT/internal" -name "*.go" -print0 2>/dev/null || true)
    
    # Check for single handler per domain
    local handler_domains=()
    while IFS= read -r -d '' file; do
        local basename
        basename=$(basename "$file" .go)
        if [[ "$basename" =~ _handler$ ]]; then
            local domain
            domain=$(echo "$basename" | sed 's/_handler$//')
            if [[ " ${handler_domains[*]} " =~ " $domain " ]]; then
                log_error "Multiple handlers found for domain: $domain"
                add_violation "structure" "Multiple handlers for single domain" "$domain"
            else
                handler_domains+=("$domain")
            fi
        fi
    done < <(find "$PROJECT_ROOT/internal/handlers" -name "*_handler.go" -print0 2>/dev/null || true)
    
    update_check_status "structure" "checked" "Project structure verification completed"
}

# 5. VERIFICAÇÃO DE CONFIGURAÇÃO
check_configuration() {
    log_info "Checking configuration..."
    
    # Check for required environment variables documentation
    local env_vars=("DATABASE_URL" "JWT_SECRET" "WEBSOCKET_PORT" "VAPID_PUBLIC_KEY" "VAPID_PRIVATE_KEY")
    local env_file="$PROJECT_ROOT/.env.example"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "Environment variables example file not found: .env.example"
        add_warning "configuration" "Missing .env.example file" ""
    else
        for var in "${env_vars[@]}"; do
            if ! grep -q "$var" "$env_file"; then
                log_warning "Required environment variable not documented: $var"
                add_warning "configuration" "Environment variable not documented" "$var"
            fi
        done
    fi
    
    # Check WebSocket configuration
    if ! grep -r "websocket\|WebSocket" "$PROJECT_ROOT/internal" --include="*.go" 2>/dev/null | grep -i "config\|setup"; then
        log_warning "WebSocket configuration not found"
        add_warning "configuration" "WebSocket configuration missing" ""
    fi
    
    # Check notification service configuration
    if [[ -f "$PROJECT_ROOT/internal/services/notification_service.go" ]]; then
        if ! grep -q "VAPID\|vapid" "$PROJECT_ROOT/internal/services/notification_service.go"; then
            log_warning "VAPID configuration not found in notification service"
            add_warning "configuration" "VAPID configuration missing" "notification_service.go"
        fi
    fi
    
    update_check_status "configuration" "checked" "Configuration verification completed"
}

# Calculate compliance score
calculate_compliance_score() {
    local total_checks=5
    local passed_checks=0
    
    # Count passed checks
    if jq -e '.checks.duplications.status == "passed"' "$REPORT_FILE" >/dev/null; then
        ((passed_checks++))
    fi
    if jq -e '.checks.mandatory_patterns.status == "checked"' "$REPORT_FILE" >/dev/null && [[ $VIOLATIONS -eq 0 ]]; then
        ((passed_checks++))
    fi
    if jq -e '.checks.quality.status == "checked"' "$REPORT_FILE" >/dev/null; then
        ((passed_checks++))
    fi
    if jq -e '.checks.structure.status == "checked"' "$REPORT_FILE" >/dev/null; then
        ((passed_checks++))
    fi
    if jq -e '.checks.configuration.status == "checked"' "$REPORT_FILE" >/dev/null; then
        ((passed_checks++))
    fi
    
    local score
    score=$(( (passed_checks * 100) / total_checks ))
    
    # Penalize for violations
    if [[ $VIOLATIONS -gt 0 ]]; then
        score=$(( score - (VIOLATIONS * 10) ))
        if [[ $score -lt 0 ]]; then
            score=0
        fi
    fi
    
    jq --arg score "$score" '.compliance_score = ($score | tonumber)' \
       "$REPORT_FILE" > "${REPORT_FILE}.tmp" && mv "${REPORT_FILE}.tmp" "$REPORT_FILE"
    
    echo "$score"
}

# Generate final report
generate_report() {
    local score
    score=$(calculate_compliance_score)
    
    echo
    echo "=================================="
    echo "ARCHITECTURE COMPLIANCE REPORT"
    echo "=================================="
    echo "Compliance Score: $score%"
    echo "Violations: $VIOLATIONS"
    echo "Warnings: $WARNINGS"
    echo
    
    if [[ $VIOLATIONS -gt 0 ]]; then
        echo -e "${RED}CRITICAL VIOLATIONS FOUND:${NC}"
        jq -r '.violations[] | "  - [\(.category)] \(.message) \(if .file != "" then "(\(.file))" else "" end)"' "$REPORT_FILE"
        echo
    fi
    
    if [[ $WARNINGS -gt 0 ]]; then
        echo -e "${YELLOW}WARNINGS:${NC}"
        jq -r '.warnings[] | "  - [\(.category)] \(.message) \(if .file != "" then "(\(.file))" else "" end)"' "$REPORT_FILE"
        echo
    fi
    
    echo "Detailed report saved to: $REPORT_FILE"
    echo
    
    if [[ $VIOLATIONS -gt 0 ]]; then
        echo -e "${RED}DEPLOYMENT BLOCKED${NC} - Fix violations before deploying"
        echo
        echo "Required actions:"
        echo "1. Remove duplicate handlers and routes"
        echo "2. Implement mandatory patterns (StandardResponse, pagination, order #18 blocking)"
        echo "3. Fix authentication middleware"
        echo "4. Achieve minimum test coverage ($MIN_COVERAGE%)"
        echo "5. Remove references to deleted handlers"
        return 1
    elif [[ $score -lt 90 ]]; then
        echo -e "${YELLOW}DEPLOYMENT WARNING${NC} - Consider addressing warnings"
        return 0
    else
        echo -e "${GREEN}DEPLOYMENT APPROVED${NC} - All checks passed"
        return 0
    fi
}

# Main execution
main() {
    log_info "Starting architecture compliance check..."
    log_info "Project root: $PROJECT_ROOT"
    
    # Check dependencies
    if ! command -v jq >/dev/null 2>&1; then
        echo "Error: jq is required but not installed" >&2
        exit 1
    fi
    
    init_report
    
    check_duplications
    check_mandatory_patterns
    check_quality
    check_structure
    check_configuration
    
    generate_report
}

# Execute main function
main "$@"