# 📊 Sistema de Monitoramento - Projeto Tradição

## 🏗️ Estrutura Organizada

```
monitoramento/
├── bin/                    # Binários dos serviços
│   ├── prometheus_bin/     # Prometheus
│   ├── grafana-v11.0.0/    # Graf<PERSON>
│   └── node_exporter-1.8.0.linux-amd64/  # Node Exporter
├── configs/                # Arquivos de configuração
│   ├── prometheus.yml      # Configuração do Prometheus
│   └── prometheus_alerts.yml  # Regras de alerta
├── dashboards/             # Dashboards JSON
│   ├── dashboard_basico.json
│   ├── dashboard_api.json
│   ├── dashboard_recursos.json
│   ├── dashboard_health.json
│   ├── dashboard_unificado.json
│   └── dashboard_unificado_alertas.json
├── scripts/                # Scripts de automação
│   ├── gerenciar_monitoramento.sh
│   ├── configurar_grafana.sh
│   └── criar_dashboard_unificado.sh
└── docs/                   # Documentação
    └── MONITORAMENTO_CONFIGURADO.md
```

## 🚀 Início Rápido

### 1. Iniciar <PERSON>
```bash
./monitoramento/scripts/gerenciar_monitoramento.sh start
```

### 2. Verificar Status
```bash
./monitoramento/scripts/gerenciar_monitoramento.sh status
```

### 3. Acessar Dashboards
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Node Exporter**: http://localhost:9100

## 📋 Serviços

### **Prometheus** (Porta 9090)
- Coleta métricas do backend e sistema
- Configurado com Node Exporter
- Alertas configurados e ativos

### **Grafana** (Porta 3000)
- Dashboards unificados
- Alertas visuais e por e-mail
- Login: admin/admin

### **Node Exporter** (Porta 9100)
- Métricas do sistema operacional
- CPU, memória, disco, rede

## 🎯 Alertas Configurados

### **Memória RAM (8GB Total)**
- **Alerta**: Acima de 6GB (75% da capacidade)
- **Limite**: 6144 MB

### **CPU**
- **Alerta**: Acima de 80%
- **Limite**: 80%

### **Disco (30GB+ Total)**
- **Alerta**: Abaixo de 5GB disponível
- **Limite**: 5GB

### **Endpoint /metrics**
- **Alerta**: Endpoint fora do ar
- **Limite**: < 1

## 🛠️ Scripts Disponíveis

### **gerenciar_monitoramento.sh**
```bash
./monitoramento/scripts/gerenciar_monitoramento.sh [comando]
```
- `start` - Inicia todos os serviços
- `stop` - Para todos os serviços
- `restart` - Reinicia todos os serviços
- `status` - Mostra status dos serviços
- `logs` - Mostra logs dos serviços

### **configurar_grafana.sh**
```bash
./monitoramento/scripts/configurar_grafana.sh
```
- Configura fonte de dados Prometheus
- Importa dashboards básicos
- Configura alertas por e-mail

### **criar_dashboard_unificado.sh**
```bash
./monitoramento/scripts/criar_dashboard_unificado.sh
```
- Cria dashboard unificado
- Inclui todos os painéis importantes

## 📧 Notificações

### **Configuração SMTP**
- **Servidor**: smtp.gmail.com:587
- **Canal**: "E-mail - ALERTA GRAFANA"

### **Alertas Enviados**
- CPU acima de 80%
- Memória acima de 6GB
- Disco abaixo de 5GB
- Endpoint /metrics fora do ar

## 🔗 Dashboards Disponíveis

1. **Dashboard Unificado - Sistema Completo**
   - Todos os painéis em uma tela
   - Métricas em tempo real

2. **Dashboard Unificado - Sistema Completo (Alertas Ajustados)**
   - Alertas configurados para 8GB RAM
   - Monitoramento de disco 30GB+

3. **Métricas do Sistema**
   - CPU, memória, requisições HTTP
   - Tempo de resposta

4. **Performance da API**
   - Requisições por segundo
   - Erros 5xx
   - Latência média

5. **Recursos do Sistema**
   - Uso de recursos detalhado
   - Gráficos temporais

## 📚 Documentação

- **MONITORAMENTO_CONFIGURADO.md**: Documentação completa das configurações
- **README.md**: Este arquivo

## ✅ Status Atual

Todos os serviços estão configurados e operacionais:
- ✓ Node Exporter: RODANDO
- ✓ Prometheus: RODANDO  
- ✓ Grafana: RODANDO

## 🎉 Monitoramento Completo

O sistema de monitoramento está totalmente configurado e operacional, com alertas ajustados para a capacidade real do servidor de 8GB RAM e 30GB+ de disco. 