groups:
  - name: basic_alerts
    rules:
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Uso de CPU alto (>80%)"
          description: "O uso de CPU está acima de 80% por mais de 2 minutos."

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 800
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Uso de memória alto (>800MB)"
          description: "O uso de memória está acima de 800MB por mais de 2 minutos."

      - alert: MetricsEndpointDown
        expr: up{job="projeto_linux_backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Endpoint /metrics fora do ar"
          description: "O endpoint /metrics não está respondendo há mais de 1 minuto." 