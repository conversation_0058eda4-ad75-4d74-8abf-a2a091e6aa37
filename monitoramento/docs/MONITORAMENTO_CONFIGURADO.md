# 📊 Monitoramento Configurado - Alertas Ajustados

## 🎯 Configurações Ajustadas para Seu Servidor

### **Especificações do Servidor**
- **RAM**: 8GB total
- **Disco**: 30GB+ disponível
- **Alertas configurados para sua capacidade real**

### **Alertas Configurados**

#### **1. Memória RAM (8GB Total)**
- **Alerta**: Acima de 6GB (75% da capacidade)
- **Métrica**: `process_resident_memory_bytes`
- **Limite**: 6144 MB
- **Mensagem**: "Uso de memória acima de 6GB (de 8GB total)"

#### **2. CPU**
- **Alerta**: Acima de 80%
- **Métrica**: `rate(process_cpu_seconds_total[5m]) * 100`
- **Limite**: 80%
- **Mensagem**: "Uso de CPU acima de 80%"

#### **3. Disco (30GB+ Total)**
- **Alerta**: Abaixo de 5GB disponível
- **Métrica**: `node_filesystem_avail_bytes{mountpoint="/"}`
- **Limite**: 5GB
- **Mensagem**: "Espaço em disco abaixo de 5GB (de 30GB+ total)"

#### **4. Endpoint /metrics**
- **Alerta**: Endpoint fora do ar
- **Métrica**: `up{job="projeto_linux_backend"}`
- **Limite**: < 1
- **Mensagem**: "Endpoint /metrics fora do ar"

## 🚀 Serviços em Execução

### **Prometheus** (Porta 9090)
- Coleta métricas do backend e sistema
- Configurado com Node Exporter para métricas de sistema
- Alertas configurados e ativos

### **Grafana** (Porta 3000)
- Dashboard unificado com todos os painéis
- Alertas visuais e por e-mail configurados
- Login: admin/admin

### **Node Exporter** (Porta 9100)
- Coleta métricas do sistema operacional
- Inclui métricas de CPU, memória, disco, rede

## 📋 Dashboard Unificado

### **Painéis Configurados**
1. **CPU Usage** - Uso de CPU em tempo real
2. **Memory Usage (8GB Total)** - Uso de memória RAM
3. **Endpoint /metrics** - Status do endpoint de métricas
4. **Disk Usage (30GB+ Total)** - Espaço disponível em disco

### **Alertas Visuais**
- Indicadores coloridos baseados nos limites
- Alertas por e-mail configurados
- Notificações automáticas

## 🛠️ Gerenciamento

### **Script de Controle**
```bash
./gerenciar_monitoramento.sh [comando]
```

### **Comandos Disponíveis**
- `start` - Inicia todos os serviços
- `stop` - Para todos os serviços
- `restart` - Reinicia todos os serviços
- `status` - Mostra status dos serviços
- `logs` - Mostra logs dos serviços

## 📧 Notificações por E-mail

### **Configuração SMTP**
- **Servidor**: smtp.gmail.com:587
- **Usuário**: [<EMAIL>]
- **Canal**: "E-mail - ALERTA GRAFANA"

### **Alertas Enviados**
- CPU acima de 80%
- Memória acima de 6GB
- Disco abaixo de 5GB
- Endpoint /metrics fora do ar

## 🔗 URLs de Acesso

- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Node Exporter**: http://localhost:9100
- **Dashboard Unificado**: http://localhost:3000/d/aert3dxfpibk0c

## ✅ Status Atual

Todos os serviços estão rodando e configurados:
- ✓ Node Exporter: RODANDO
- ✓ Prometheus: RODANDO
- ✓ Grafana: RODANDO

## 🎉 Monitoramento Completo

O sistema de monitoramento está totalmente configurado e operacional, com alertas ajustados para a capacidade real do seu servidor de 8GB RAM e 30GB+ de disco. 