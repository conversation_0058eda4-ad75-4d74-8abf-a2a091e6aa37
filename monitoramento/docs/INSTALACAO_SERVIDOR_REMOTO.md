# 🚀 Instalação em Servidor Remoto - Projeto Tradição

## 📋 Pré-requisitos

- **Sistema**: Linux (Ubuntu/Debian, CentOS/RHEL, Fedora)
- **Permissões**: Root ou sudo
- **Portas**: 3000 (Grafana), 9090 (Prometheus), 9100 (Node Exporter)
- **Dependências**: curl, wget, jq (instaladas automaticamente)

## 🔧 Instalação Automática

### 1. Upload dos Arquivos
```bash
# Copiar pasta monitoramento para o servidor
scp -r monitoramento/ usuario@seu-servidor:/tmp/
```

### 2. Executar Instalação
```bash
# Conectar ao servidor
ssh usuario@seu-servidor

# Executar instalação
sudo /tmp/monitoramento/scripts/instalar_servidor_remoto.sh
```

### 3. Iniciar Sistema
```bash
# Iniciar serviços
sudo systemctl start monitoramento

# Verificar status
sudo systemctl status monitoramento
```

## 🎯 Auto-Detecção de Hardware

O sistema **detecta automaticamente** o hardware do servidor e ajusta os alertas:

### **Memória RAM**
- **Detecção**: `free -g` (memória total)
- **Alerta**: 75% da memória total
- **Exemplo**: 16GB → Alerta em 12GB

### **Disco**
- **Detecção**: `df -BG /` (espaço total)
- **Alerta**: 10% do disco total (mínimo 5GB)
- **Exemplo**: 100GB → Alerta em 10GB

### **CPU**
- **Alerta**: Acima de 80% (fixo)

### **Endpoint**
- **Alerta**: Fora do ar (fixo)

## 📊 URLs dos Serviços

Após instalação, acesse:

- **Grafana**: `http://SEU_IP:3000` (admin/admin)
- **Prometheus**: `http://SEU_IP:9090`
- **Node Exporter**: `http://SEU_IP:9100`

## 🔧 Comandos de Gerenciamento

### **Serviço Systemd**
```bash
# Status
sudo systemctl status monitoramento

# Iniciar
sudo systemctl start monitoramento

# Parar
sudo systemctl stop monitoramento

# Reiniciar
sudo systemctl restart monitoramento

# Habilitar auto-inicialização
sudo systemctl enable monitoramento
```

### **Scripts Manuais**
```bash
# Ir para diretório
cd /opt/monitoramento

# Gerenciar serviços
./scripts/gerenciar_monitoramento.sh {start|stop|restart|status}

# Detectar hardware novamente
./scripts/ajustar_hardware.sh

# Configurar Grafana
./scripts/configurar_grafana.sh
```

## 📧 Configuração de E-mail

### **1. Configurar SMTP no Grafana**
```bash
# Editar configuração
sudo nano /opt/monitoramento/bin/grafana-v11.0.0/conf/custom.ini
```

### **2. Adicionar configuração SMTP**
```ini
[smtp]
enabled = true
host = smtp.gmail.com:587
user = <EMAIL>
password = sua-senha-app
from_address = <EMAIL>
from_name = Sistema Tradição
```

### **3. Reiniciar Grafana**
```bash
sudo systemctl restart monitoramento
```

## 🔒 Segurança

### **Firewall**
```bash
# Ubuntu/Debian
sudo ufw allow 3000/tcp  # Grafana
sudo ufw allow 9090/tcp  # Prometheus
sudo ufw allow 9100/tcp  # Node Exporter

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=9090/tcp
sudo firewall-cmd --permanent --add-port=9100/tcp
sudo firewall-cmd --reload
```

### **Usuário de Serviço**
- **Usuário**: `monitor`
- **Diretório**: `/opt/monitoramento`
- **Permissões**: Apenas leitura/execução

## 📈 Monitoramento

### **Logs do Sistema**
```bash
# Logs do serviço
sudo journalctl -u monitoramento -f

# Logs do Grafana
tail -f /opt/monitoramento/bin/grafana-v11.0.0/data/log/grafana.log

# Logs do Prometheus
tail -f /opt/monitoramento/bin/prometheus_bin/prometheus.log
```

### **Métricas Importantes**
- **CPU**: `rate(process_cpu_seconds_total[5m]) * 100`
- **Memória**: `process_resident_memory_bytes / 1024 / 1024`
- **Disco**: `node_filesystem_avail_bytes{mountpoint="/"}`
- **Endpoint**: `up{job="projeto_linux_backend"}`

## 🚨 Troubleshooting

### **Problema**: Serviço não inicia
```bash
# Verificar logs
sudo journalctl -u monitoramento -n 50

# Verificar permissões
ls -la /opt/monitoramento/

# Verificar dependências
which curl wget jq
```

### **Problema**: Porta já em uso
```bash
# Verificar processos
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :9090
sudo netstat -tlnp | grep :9100

# Matar processos
sudo pkill -f grafana
sudo pkill -f prometheus
sudo pkill -f node_exporter
```

### **Problema**: Alertas não funcionam
```bash
# Verificar configuração SMTP
curl -X GET *********************************/api/admin/settings

# Testar e-mail
curl -X POST *********************************/api/admin/users/1/password \
  -H "Content-Type: application/json" \
  -d '{"password":"nova-senha"}'
```

## 📚 Documentação Adicional

- **README Principal**: `/opt/monitoramento/README.md`
- **Configurações**: `/opt/monitoramento/configs/`
- **Scripts**: `/opt/monitoramento/scripts/`
- **Dashboards**: `/opt/monitoramento/dashboards/`

---

**✅ Sistema pronto para produção com auto-detecção de hardware!** 