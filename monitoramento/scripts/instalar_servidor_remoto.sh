#!/bin/bash

# Script de instalação para servidor remoto
# Projeto Tradição - Monitoramento

echo "🚀 Instalação do Sistema de Monitoramento - Servidor Remoto"
echo "=========================================================="
echo ""

# Verificar se está rodando como root
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  Este script deve ser executado como root ou com sudo"
    echo "   sudo $0"
    exit 1
fi

echo "📋 Verificando dependências..."

# Instalar dependências
if command -v apt-get &> /dev/null; then
    echo "📦 Instalando dependências (Ubuntu/Debian)..."
    apt-get update
    apt-get install -y curl wget jq
elif command -v yum &> /dev/null; then
    echo "📦 Instalando dependências (CentOS/RHEL)..."
    yum install -y curl wget jq
elif command -v dnf &> /dev/null; then
    echo "📦 Instalando dependências (Fedora)..."
    dnf install -y curl wget jq
else
    echo "⚠️  Gerenciador de pacotes não suportado"
    echo "   Instale manualmente: curl, wget, jq"
    exit 1
fi

echo "✅ Dependências instaladas!"

# Detectar hardware
echo ""
echo "🔍 Detectando hardware do servidor..."

MEMORY_TOTAL_GB=$(free -g | awk 'NR==2{print $2}')
DISK_TOTAL_GB=$(df -BG / | awk 'NR==2{print $2}' | sed 's/G//')

echo "📊 Hardware detectado:"
echo "   Memória Total: ${MEMORY_TOTAL_GB}GB"
echo "   Disco Total: ${DISK_TOTAL_GB}GB"

# Criar diretório de monitoramento
echo ""
echo "📁 Criando estrutura de diretórios..."
mkdir -p /opt/monitoramento/{bin,configs,dashboards,scripts,docs}

# Copiar arquivos
echo "📋 Copiando arquivos..."
cp -r monitoramento/* /opt/monitoramento/

# Ajustar permissões
chmod +x /opt/monitoramento/scripts/*.sh

# Detectar e ajustar alertas para o hardware
echo ""
echo "🔧 Ajustando alertas para o hardware detectado..."
cd /opt/monitoramento/scripts
./ajustar_hardware.sh

# Criar usuário de serviço
echo ""
echo "👤 Criando usuário de serviço..."
if ! id "monitor" &>/dev/null; then
    useradd -r -s /bin/false -d /opt/monitoramento monitor
    chown -R monitor:monitor /opt/monitoramento
fi

# Criar serviço systemd para auto-inicialização
echo ""
echo "⚙️  Criando serviço systemd..."

cat > /etc/systemd/system/monitoramento.service << EOF
[Unit]
Description=Sistema de Monitoramento - Projeto Tradição
After=network.target

[Service]
Type=forking
User=monitor
WorkingDirectory=/opt/monitoramento
ExecStart=/opt/monitoramento/scripts/gerenciar_monitoramento.sh start
ExecStop=/opt/monitoramento/scripts/gerenciar_monitoramento.sh stop
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Recarregar systemd e habilitar serviço
systemctl daemon-reload
systemctl enable monitoramento.service

echo ""
echo "🎯 Configuração de alertas para seu servidor:"
echo "   CPU: Acima de 80%"
echo "   Memória: Acima de $((MEMORY_TOTAL_GB * 75 / 100))GB (de ${MEMORY_TOTAL_GB}GB total)"
echo "   Disco: Abaixo de $((DISK_TOTAL_GB * 10 / 100))GB (de ${DISK_TOTAL_GB}GB total)"
echo "   Endpoint: Fora do ar"

echo ""
echo "✅ Instalação concluída!"
echo ""
echo "🚀 Para iniciar o sistema:"
echo "   sudo systemctl start monitoramento"
echo ""
echo "📊 URLs dos serviços:"
echo "   Grafana: http://SEU_IP:3000 (admin/admin)"
echo "   Prometheus: http://SEU_IP:9090"
echo "   Node Exporter: http://SEU_IP:9100"
echo ""
echo "📚 Documentação: /opt/monitoramento/README.md"
echo ""
echo "🔧 Comandos úteis:"
echo "   sudo systemctl status monitoramento"
echo "   sudo systemctl restart monitoramento"
echo "   sudo systemctl stop monitoramento" 