#!/bin/bash

# Script para gerenciar serviços de monitoramento
# Prometheus, Grafana e Node Exporter

case "$1" in
    "start")
        echo "Iniciando serviços de monitoramento..."
        
        # Iniciar Node Exporter
        if ! pgrep -f "node_exporter" > /dev/null; then
            echo "Iniciando Node Exporter..."
            ./monitoramento/bin/node_exporter-1.8.0.linux-amd64/node_exporter --web.listen-address=":9100" &
            sleep 2
        else
            echo "Node Exporter já está rodando"
        fi
        
        # Iniciar Prometheus
        if ! pgrep -f "prometheus" > /dev/null; then
            echo "Iniciando Prometheus..."
            ./monitoramento/bin/prometheus_bin/prometheus --config.file=monitoramento/configs/prometheus.yml --web.listen-address=":9090" &
            sleep 3
        else
            echo "Prometheus já está rodando"
        fi
        
        # Iniciar Grafana
        if ! pgrep -f "grafana" > /dev/null; then
            echo "Iniciando Graf<PERSON>..."
            ./monitoramento/bin/grafana-v11.0.0/bin/grafana-server --homepath ./monitoramento/bin/grafana-v11.0.0 &
            sleep 5
        else
            echo "Grafana já está rodando"
        fi
        
        echo "Serviços iniciados!"
        echo "Prometheus: http://localhost:9090"
        echo "Grafana: http://localhost:3000 (admin/admin)"
        echo "Node Exporter: http://localhost:9100"
        ;;
        
    "stop")
        echo "Parando serviços de monitoramento..."
        pkill -f "node_exporter"
        pkill -f "prometheus"
        pkill -f "grafana"
        echo "Serviços parados!"
        ;;
        
    "restart")
        echo "Reiniciando serviços de monitoramento..."
        $0 stop
        sleep 2
        $0 start
        ;;
        
    "status")
        echo "Status dos serviços:"
        echo "===================="
        
        if pgrep -f "node_exporter" > /dev/null; then
            echo "✓ Node Exporter: RODANDO"
        else
            echo "✗ Node Exporter: PARADO"
        fi
        
        if pgrep -f "prometheus" > /dev/null; then
            echo "✓ Prometheus: RODANDO"
        else
            echo "✗ Prometheus: PARADO"
        fi
        
        if pgrep -f "grafana" > /dev/null; then
            echo "✓ Grafana: RODANDO"
        else
            echo "✗ Grafana: PARADO"
        fi
        ;;
        
    "logs")
        echo "Logs dos serviços (últimas 20 linhas):"
        echo "====================================="
        
        if pgrep -f "prometheus" > /dev/null; then
            echo "Prometheus logs:"
            echo "---------------"
            # Prometheus logs aparecem no terminal onde foi iniciado
        fi
        
        if pgrep -f "grafana" > /dev/null; then
            echo "Grafana logs:"
            echo "-------------"
            # Grafana logs aparecem no terminal onde foi iniciado
        fi
        ;;
        
    *)
        echo "Uso: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Comandos:"
        echo "  start   - Inicia todos os serviços"
        echo "  stop    - Para todos os serviços"
        echo "  restart - Reinicia todos os serviços"
        echo "  status  - Mostra status dos serviços"
        echo "  logs    - Mostra logs dos serviços"
        echo ""
        echo "URLs dos serviços:"
        echo "  Prometheus: http://localhost:9090"
        echo "  Grafana: http://localhost:3000 (admin/admin)"
        echo "  Node Exporter: http://localhost:9100"
        exit 1
        ;;
esac 