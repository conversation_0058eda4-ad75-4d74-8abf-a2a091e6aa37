#!/bin/bash

echo "Configurando Grafana automaticamente..."

# Aguardar Grafana inicializar
sleep 5

# Configurar fonte de dados Prometheus
curl -X POST *********************************/api/datasources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Prometheus",
    "type": "prometheus",
    "url": "http://localhost:9090",
    "access": "proxy",
    "isDefault": true
  }'

echo ""
echo "Fonte de dados Prometheus configurada!"

# Dashboard básico para métricas do sistema
cat > dashboard_basico.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "Métricas do Sistema",
    "tags": ["sistema", "monitoramento"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "CPU Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(process_cpu_seconds_total[5m]) * 100",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 90}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Memory Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "MB",
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 500},
                {"color": "red", "value": 1000}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "HTTP Requests",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])",
            "refId": "A"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  },
  "folderId": 0,
  "overwrite": true
}
EOF

# Importar dashboard
curl -X POST *********************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @../dashboards/dashboard_basico.json

echo ""
echo "Dashboard básico importado!"
echo ""
echo "🎉 Configuração completa!"
echo "Acesse: http://localhost:3000"
echo "Usuário: admin"
echo "Senha: admin"
echo ""
echo "Dashboard 'Métricas do Sistema' já está disponível!" 

# Dashboard de Performance de API
cat > dashboard_api.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "Performance da API",
    "tags": ["api", "performance"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Requisições por Segundo",
        "type": "stat",
        "targets": [
          { "expr": "rate(http_requests_total[5m])", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "reqps" } },
        "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Erros 5xx",
        "type": "stat",
        "targets": [
          { "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m]))", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "short" } },
        "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}
      },
      {
        "id": 3,
        "title": "Latência Média",
        "type": "stat",
        "targets": [
          { "expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "s" } },
        "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}
      }
    ],
    "time": { "from": "now-1h", "to": "now" },
    "refresh": "5s"
  },
  "folderId": 0,
  "overwrite": true
}
EOF

curl -X POST *********************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @../dashboards/dashboard_api.json

# Dashboard de Recursos do Sistema
cat > dashboard_recursos.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "Recursos do Sistema",
    "tags": ["sistema", "recursos"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Goroutines",
        "type": "stat",
        "targets": [
          { "expr": "go_goroutines", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "short" } },
        "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Heap Alloc",
        "type": "stat",
        "targets": [
          { "expr": "go_memstats_heap_alloc_bytes / 1024 / 1024", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "MB" } },
        "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}
      }
    ],
    "time": { "from": "now-1h", "to": "now" },
    "refresh": "5s"
  },
  "folderId": 0,
  "overwrite": true
}
EOF

curl -X POST *********************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @dashboard_recursos.json

# Dashboard de Health Check
cat > dashboard_health.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "Health Check",
    "tags": ["health", "status"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Status do /metrics",
        "type": "stat",
        "targets": [
          { "expr": "up{job=\"projeto_linux_backend\"}", "refId": "A" }
        ],
        "fieldConfig": { "defaults": { "unit": "short" } },
        "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}
      }
    ],
    "time": { "from": "now-1h", "to": "now" },
    "refresh": "5s"
  },
  "folderId": 0,
  "overwrite": true
}
EOF

curl -X POST *********************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @dashboard_health.json 

# Criar canal de notificação por e-mail
curl -X POST *********************************/api/alert-notifications \
  -H "Content-Type: application/json" \
  -d '{
    "name": "E-mail - ALERTA GRAFANA",
    "type": "email",
    "isDefault": true,
    "settings": {
      "addresses": "<EMAIL>"
    }
  }'

echo "Canal de notificação por e-mail criado!" 