#!/bin/bash

# Script para detectar hardware e ajustar alertas automaticamente
# Projeto Tradição - Monitoramento

echo "🔍 Detectando hardware do servidor..."

# Detectar memória total
MEMORY_TOTAL_GB=$(free -g | awk 'NR==2{print $2}')
MEMORY_TOTAL_MB=$((MEMORY_TOTAL_GB * 1024))
MEMORY_ALERT_MB=$((MEMORY_TOTAL_MB * 75 / 100))  # 75% da memória total

# Detectar disco total
DISK_TOTAL_GB=$(df -BG / | awk 'NR==2{print $2}' | sed 's/G//')
DISK_ALERT_GB=$((DISK_TOTAL_GB * 10 / 100))  # 10% do disco total (mínimo 5GB)

# Garantir mínimo de 5GB para alerta de disco
if [ $DISK_ALERT_GB -lt 5 ]; then
    DISK_ALERT_GB=5
fi

echo "📊 Hardware detectado:"
echo "   Memória Total: ${MEMORY_TOTAL_GB}GB"
echo "   Alerta Memória: ${MEMORY_ALERT_MB}MB (75%)"
echo "   Disco Total: ${DISK_TOTAL_GB}GB"
echo "   Alerta Disco: ${DISK_ALERT_GB}GB (mínimo)"

# Atualizar dashboard com valores detectados
DASHBOARD_FILE="$(dirname "$0")/../dashboards/dashboard_unificado_alertas.json"

# Backup do arquivo original
cp "$DASHBOARD_FILE" "${DASHBOARD_FILE}.backup"

# Atualizar título do painel de memória
sed -i "s/\"Memory Usage (8GB Total)\"/\"Memory Usage (${MEMORY_TOTAL_GB}GB Total)\"/g" "$DASHBOARD_FILE"

# Atualizar alerta de memória
sed -i "s/\"params\": \[6144\]/\"params\": \[${MEMORY_ALERT_MB}\]/g" "$DASHBOARD_FILE"
sed -i "s/\"Uso de memória acima de 6GB (de 8GB total)\"/\"Uso de memória acima de ${MEMORY_ALERT_MB}MB (de ${MEMORY_TOTAL_GB}GB total)\"/g" "$DASHBOARD_FILE"

# Atualizar título do painel de disco
sed -i "s/\"Disk Usage (30GB+ Total)\"/\"Disk Usage (${DISK_TOTAL_GB}GB Total)\"/g" "$DASHBOARD_FILE"

# Atualizar alerta de disco
sed -i "s/\"params\": \[5\]/\"params\": \[${DISK_ALERT_GB}\]/g" "$DASHBOARD_FILE"
sed -i "s/\"Espaço em disco abaixo de 5GB (de 30GB+ total)\"/\"Espaço em disco abaixo de ${DISK_ALERT_GB}GB (de ${DISK_TOTAL_GB}GB total)\"/g" "$DASHBOARD_FILE"

echo "✅ Dashboard atualizado com valores do hardware detectado!"

# Verificar se Grafana está rodando e atualizar dashboard
if pgrep -f "grafana" > /dev/null; then
    echo "🔄 Atualizando dashboard no Grafana..."
    
    # Buscar ID do dashboard
    DASHBOARD_ID=$(curl -s *********************************/api/search?query=Alertas%20Ajustados | jq '.[0].id')
    
    if [ "$DASHBOARD_ID" != "null" ] && [ "$DASHBOARD_ID" != "" ]; then
        # Atualizar dashboard
        curl -X PUT *********************************/api/dashboards/db \
          -H "Content-Type: application/json" \
          -d "{\"dashboard\": $(cat "$DASHBOARD_FILE" | grep -A 1000 '\"dashboard\"'), \"overwrite\": true}"
        
        echo "✅ Dashboard atualizado no Grafana (ID: $DASHBOARD_ID)"
    else
        echo "⚠️  Dashboard não encontrado no Grafana"
    fi
else
    echo "⚠️  Grafana não está rodando. Execute quando iniciar o Grafana."
fi

echo ""
echo "🎯 Alertas configurados para seu hardware:"
echo "   CPU: Acima de 80%"
echo "   Memória: Acima de ${MEMORY_ALERT_MB}MB (${MEMORY_TOTAL_GB}GB total)"
echo "   Disco: Abaixo de ${DISK_ALERT_GB}GB (${DISK_TOTAL_GB}GB total)"
echo "   Endpoint: Fora do ar" 