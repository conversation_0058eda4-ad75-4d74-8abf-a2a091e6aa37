{"dashboard": {"id": null, "title": "Dashboard Unificado - Si<PERSON><PERSON> (Alertas Ajustados)", "tags": ["unificado", "sistema", "monitoramento", "alerta"], "timezone": "browser", "refresh": "5s", "panels": [{"id": 1, "title": "CPU Usage", "type": "stat", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "alert": {"name": "Alerta CPU Alta", "conditions": [{"evaluator": {"params": [80], "type": ">"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Uso de CPU acima de 80%", "noDataState": "no_data", "notifications": [{"uid": "email-alerta-gra<PERSON>a"}]}}, {"id": 2, "title": "Memory Usage (3GB Total)", "type": "stat", "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "alert": {"name": "Alerta Memória Alta", "conditions": [{"evaluator": {"params": [2304], "type": ">"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Uso de memória acima de 2304MB (de 3GB total)", "noDataState": "no_data", "notifications": [{"uid": "email-alerta-gra<PERSON>a"}]}}, {"id": 3, "title": "Endpoint /metrics", "type": "stat", "targets": [{"expr": "up{job=\"projeto_linux_backend\"}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "alert": {"name": "Alerta Endpoint Down", "conditions": [{"evaluator": {"params": [1], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Endpoint /metrics fora do ar", "noDataState": "alerting", "notifications": [{"uid": "email-alerta-gra<PERSON>a"}]}}, {"id": 4, "title": "Disk Usage (39GB Total)", "type": "stat", "targets": [{"expr": "node_filesystem_avail_bytes{mountpoint=\"/\"} / 1024 / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 8}, "alert": {"name": "Alerta Disco Baixo", "conditions": [{"evaluator": {"params": [5], "type": "<"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "message": "Espaço em disco abaixo de 5GB (de 39GB total)", "noDataState": "no_data", "notifications": [{"uid": "email-alerta-gra<PERSON>a"}]}}]}}