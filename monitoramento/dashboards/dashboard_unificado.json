{"dashboard": {"id": null, "title": "Dashboard Unificado - Sistema Completo", "tags": ["unificado", "sistema", "monitoramento"], "timezone": "browser", "refresh": "5s", "panels": [{"id": 1, "title": "CPU Usage", "type": "stat", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "stat", "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "HTTP Requests/sec", "type": "stat", "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Response Time", "type": "stat", "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Goroutines", "type": "stat", "targets": [{"expr": "go_goroutines", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 8}}, {"id": 6, "title": "Heap Usage", "type": "stat", "targets": [{"expr": "go_memstats_heap_alloc_bytes / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 8}}, {"id": 7, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}}, {"id": 8, "title": "System Uptime", "type": "stat", "targets": [{"expr": "process_start_time_seconds", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}}, {"id": 9, "title": "CPU Usage Over Time", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 10, "title": "Memory Usage Over Time", "type": "timeseries", "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 11, "title": "HTTP Requests Over Time", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 12, "title": "Response Time Over Time", "type": "timeseries", "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}]}}