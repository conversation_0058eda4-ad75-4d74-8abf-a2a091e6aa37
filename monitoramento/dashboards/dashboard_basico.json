{"dashboard": {"id": null, "title": "Métricas do Sistema", "tags": ["sistema", "monitoramento"], "timezone": "browser", "panels": [{"id": 1, "title": "CPU Usage", "type": "stat", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "stat", "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "MB", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 1000}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "HTTP Requests", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Response Time", "type": "graph", "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}, "folderId": 0, "overwrite": true}