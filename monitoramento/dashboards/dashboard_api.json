{"dashboard": {"id": null, "title": "Performance da API", "tags": ["api", "performance"], "timezone": "browser", "panels": [{"id": 1, "title": "Requisições por Segundo", "type": "stat", "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}}, {"id": 2, "title": "Erros 5xx", "type": "stat", "targets": [{"expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m]))", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}}, {"id": 3, "title": "Latência Média", "type": "stat", "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}, "folderId": 0, "overwrite": true}