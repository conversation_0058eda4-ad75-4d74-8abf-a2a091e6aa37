#!/bin/bash

# Script de desenvolvimento com hot reload seguro
# Uso: ./dev.sh

set -e

# Configurar PATH do Go
export PATH="/usr/local/go/bin:$PATH"
export GOPATH="$HOME/go"
export PATH="$GOPATH/bin:$PATH"

echo "🔧 MODO DESENVOLVIMENTO - HOT RELOAD"
echo "===================================="

# Verificar se Air está instalado
if ! command -v air &> /dev/null; then
    echo "📦 Instalando Air..."
    go install github.com/cosmtrek/air@latest
    echo "✅ Air instalado com sucesso!"
fi

# Criar diretório tmp se não existir
mkdir -p tmp

# Verificar se .env existe
if [ ! -f .env ]; then
    echo "❌ Arquivo .env não encontrado!"
    echo "Configure as variáveis de ambiente antes de continuar."
    exit 1
fi

echo "✅ Configuração verificada"
echo "🚀 Iniciando servidor com hot reload..."
echo ""
echo "📝 DICAS:"
echo "   - Ctrl+C para parar"
echo "   - Mudanças em .go, .html serão detectadas automaticamente"
echo "   - Arquivos JS/CSS já recarregam sem restart"
echo ""

# Iniciar Air
air -c .air.toml
