# Sistema de Permissões Centralizado
# Rede Tradição - Sistema de Manutenção

roles:
    admin:
        description: Administrador do Sistema
        pages:
            - dashboard
            - calendario
            - minha-conta
            - relatorios
            - financeiro
            - tuto
            - vinculos/gerenciamentovinculos
            - admin/novo-link-management
        apis:
            - '*'
            - api/painelvinculos/*
            - api/maintenance-orders/remove-test-orders
            - api/branches
            - api/technicians
            - api/providers
            - api/branches
            - api/technicians
            - api/providers
            - api/branches
            - api/technicians
            - api/providers
            - api/technicians/:id/specialties
            - api/technicians/:id/specialties/:specialty_id
            - api/technicians/:id/specialties/audit
            - /api/novo-link-management/*
    branch_user:
        description: ""
        pages:
            - orders
        apis:
            - api/maintenance-orders
    filial:
        description: Gestor de Filial/Posto
        pages:
            - dashboard
            - minha-conta
            - minha-filial
            - tuto
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/stations
            - api/notifications/settings
            - api/notifications/subscribe
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
            - api/minha-filial/equipment-types
            - api/minha-filial/equipment/:id
            - api/minha-filial/equipment
            - api/orders
            - api/orders/:id
    financeiro:
        description: Analista Financeiro
        pages:
            - '*'
            - vinculos/gerenciamentovinculos
            - admin/novo-link-management
        apis:
            - '*'
            - api/painelvinculos/*
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/dashboard/metrics
            - api/dashboard/recent
            - api/dashboard/status
            - api/reports
            - api/reports/download
            - api/financial/summary
            - api/financial/transactions
            - api/notifications/settings
            - api/notifications/subscribe
            - api/technicians/:id/specialties
            - api/technicians/:id/specialties/:specialty_id
            - api/technicians/:id/specialties/audit
            - /api/novo-link-management/*
    gerente:
        description: Gerente / Gestor de Manutenção
        pages:
            - '*'
            - vinculos/gerenciamentovinculos
            - admin/novo-link-management
        apis:
            - '*'
            - api/painelvinculos/*
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance
            - api/maintenance/:id
            - api/stations
            - api/dashboard/metrics
            - api/dashboard/recent
            - api/dashboard/status
            - api/reports
            - api/reports/download
            - api/notifications/settings
            - api/notifications/subscribe
            - api/technicians/:id/specialties
            - api/technicians/:id/specialties/:specialty_id
            - api/technicians/:id/specialties/audit
            - /api/novo-link-management/*
    provider:
        description: Prestador de Serviço
        pages:
            - dashboard
            - minha-conta
        apis:
            - api/user/me
            - api/auth/logout
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/maintenance/:id
            - api/notifications/settings
            - api/notifications/subscribe
            - api/my-provider
            - api/my-provider/technicians
            - api/my-provider/logo
            - api/providers
            - api/providers/:id
            - api/providers/:id/technicians
            - api/providers/:id/logo
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
    technician:
        description: "Técnico de Manutenção"
        pages:
            - dashboard
            - verordens
            - minha-conta
        apis:
            - api/maintenance-orders
            - api/maintenance-orders/:id
            - api/user/me
            - api/auth/logout
            - api/technicians/me/provider
            - api/technicians/me/orders
            - api/user/avatar
            - api/users/:id/avatar
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/filiais
    tecnico:
        description: "Técnico de Manutenção (Alias)"
        pages:
            - dashboard
            - verordens
            - minha-conta
        apis:
            - api/maintenance-orders
            - api/maintenance-orders/:id
            - api/user/me
            - api/auth/logout
            - api/technicians/me/provider
            - api/technicians/me/orders
            - api/user/avatar
            - api/users/:id/avatar
            - api/ordens/:id/manutencao
            - api/ordens/:id/custos
            - api/ordens/:id/cronograma
            - api/ordens/:id/chat
            - api/ordens/:id
            - api/equipments
            - api/equipments/:id
            - api/equipments/types
            - api/equipments/filial/:id
            - api/filiais
public_pages:
    - ""
    - login
    - logout
    - acesso_negado
    - change-password
public_apis:
    - api/login
    - api/auth/change-password
    - api/uploads
