# DOCUMENTAÇÃO COMPLETA DO SISTEMA TRADIÇÃO

## 1. VISÃO GERAL DO PROJETO

### Descrição do Sistema
O **Sistema Tradição** é uma plataforma completa de gestão de ordens de manutenção desenvolvida para otimizar o fluxo de trabalho entre filiais, técnicos e prestadores de serviço. O sistema centraliza a criação, atribuição, acompanhamento e finalização de ordens de manutenção, garantindo rastreabilidade completa e notificações em tempo real.

### Tecnologias Utilizadas
- **Backend**: Go 1.21+ com framework Gin
- **Banco de Dados**: PostgreSQL 14+ com ORM GORM e ENT
- **Frontend**: HTML Templates + JavaScript ES6+ 
- **Notificações**: WebSocket + Push Notifications (VAPID)
- **Cache**: Redis (opcional)
- **Autenticação**: JWT + RBAC

### Arquitetura Unificada Obrigatória
O sistema segue rigorosamente a arquitetura unificada com **UM HANDLER POR DOMÍNIO**, eliminando duplicações e garantindo consistência. Todos os endpoints de ordens são consolidados em `/api/orders` com resposta padronizada `StandardResponse`.

## 2. ESTRUTURA ARQUITETURAL OBRIGATÓRIA

### Estrutura de Diretórios
```
tradicao/
├── main.go                              # Entry point
├── go.mod                               # Dependências Go
├── Makefile                             # Comandos de build/deploy
├── config/
│   └── config.go                        # Configurações centralizadas
├── internal/
│   ├── handlers/                        # Handlers unificados (UM POR DOMÍNIO)
│   │   ├── unified_order_handler.go     # Handler único para ordens
│   │   ├── user_handler.go              # Handler único para usuários
│   │   └── notification_handler.go      # Handler único para notificações
│   ├── services/                        # Lógica de negócio
│   │   ├── maintenance_order_service.go # Serviços de ordem
│   │   └── notification_service.go      # Serviços de notificação
│   ├── repository/                      # Camada de dados
│   │   ├── maintenance_order_repository.go
│   │   └── notification_repository.go
│   ├── models/                          # Modelos de dados
│   │   ├── maintenance_order.go         # Modelo principal
│   │   ├── user.go                      # Modelo de usuário
│   │   └── notification.go              # Modelo de notificação
│   ├── middleware/                      # Middlewares
│   │   ├── auth.go                      # Autenticação JWT
│   │   ├── rbac.go                      # Controle de acesso
│   │   └── cors.go                      # CORS
│   ├── routes/                          # Definição de rotas
│   │   ├── unified_order_routes.go      # Rotas unificadas
│   │   └── api_routes.go                # Rotas da API
│   ├── controllers/                     # Controllers para HTML
│   │   ├── technician_controller.go     # Controller de técnicos
│   │   └── order_controller.go          # Controller de ordens
│   └── database/                        # Configuração DB
│       ├── connection.go                # Conexão
│       └── migrations/                  # Migrações
├── web/
│   ├── templates/                       # Templates HTML
│   │   ├── tecnico/
│   │   │   ├── ordem_detail_flip.html   # Interface flip-card
│   │   │   └── galeria_tecnico_new.html # Dashboard técnico
│   │   └── ordens/
│   │       └── create_order.html        # Criação de ordem
│   └── static/
│       ├── js/
│       │   ├── unified_orders.js        # JavaScript unificado
│       │   └── tecnico_unified.js       # JS específico técnicos
│       └── css/
│           └── tecnico_unified.css      # CSS unificado técnicos
├── tests/
│   ├── integration/                     # Testes de integração
│   │   └── unified_order_flow_test.go   # Teste fluxo completo
│   └── unit/                            # Testes unitários
└── scripts/
    └── check_architecture_compliance.sh # Verificação conformidade
```

### Padrões de Handlers Unificados
**REGRA OBRIGATÓRIA**: UM HANDLER POR DOMÍNIO

```go
// UnifiedOrderHandler - Handler único para todas as operações de ordens
type UnifiedOrderHandler struct {
    repo    *repository.MaintenanceOrderRepository
    service *services.MaintenanceOrderService
}

// Endpoints consolidados:
// GET    /api/orders              - ListOrders
// GET    /api/orders/:id          - GetOrder  
// POST   /api/orders              - CreateOrder
// PUT    /api/orders/:id/status   - UpdateOrderStatus
// POST   /api/orders/:id/assign   - AssignOrder
// GET    /api/orders/technician   - GetTechnicianOrders
// GET    /api/orders/calendar     - GetCalendarOrders
```

### Sistema de Resposta Padronizada
```go
type StandardResponse struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    interface{} `json:"meta,omitempty"`
    Error   string      `json:"error,omitempty"`
}

// Exemplo de uso:
c.JSON(http.StatusOK, StandardResponse{
    Success: true,
    Message: "Ordens encontradas",
    Data:    orders,
    Meta:    paginationMeta,
})
```

## 3. FLUXO DE TRABALHO COMPLETO

### 3.1 Criação de Ordem pela Filial

**Passo 1: Seleção da Filial**
```javascript
// Se usuário tem múltiplas filiais
const filiais = await UnifiedOrderManager.getAvailableBranches();
// Usuário seleciona filial específica
```

**Passo 2: Seleção do Equipamento**
```javascript
// Carregar equipamentos da filial selecionada
const equipamentos = await UnifiedOrderManager.getEquipmentsByBranch(filialId);
// Filtrar por tipo se necessário
```

**Passo 3: Carregamento Automático de Prestadores e Técnicos**
```javascript
// Baseado no tipo de equipamento e vínculo com filial
const prestadores = await fetch(`/api/orders/available-providers?branch_id=${filialId}&equipment_type_id=${equipmentTypeId}`);
const tecnicos = await fetch(`/api/orders/available-technicians?branch_id=${filialId}&equipment_type_id=${equipmentTypeId}`);
```

**Passo 4: Seleção Mutuamente Exclusiva**
- **Prestadora OU Técnico Interno** (nunca ambos)
- Interface impede seleção simultânea
- Validação no frontend e backend

**Passo 5: Validação de Vínculos**
```go
// Backend valida vínculos obrigatórios:
// - Técnico deve estar vinculado à filial (technician_branches)
// - Técnico deve ser apto ao tipo de equipamento (technician_equipment_types)
func (s *MaintenanceOrderService) GetAvailableTechnicians(branchID, equipmentTypeID int) ([]models.User, error) {
    // Query com INNER JOIN para garantir vínculos
    return s.repo.GetTechniciansWithBothLinks(branchID, equipmentTypeID)
}
```

**Passo 6: Salvamento da Ordem**
```go
// POST /api/orders
func (h *UnifiedOrderHandler) CreateOrder(c *gin.Context) {
    // Validações completas
    // Criação da ordem
    // Notificação automática
}
```

**Passo 7: Notificação Automática**
```go
// Após criação bem-sucedida
notificationService.NotifyOrderCreated(order)
// Envia WebSocket + Push + Email para prestador/técnico
```

### 3.2 Fluxo de Atribuição

```go
// POST /api/orders/:id/assign
{
    "assigned_to": 123,
    "assigned_type": "technician", // ou "provider"
    "notes": "Urgente - equipamento crítico"
}
```

### 3.3 Fluxo de Notificações

```
Ordem Criada → NotificationService.NotifyOrderCreated() 
            → WebSocket broadcast
            → Push notification
            → Email (se configurado)
            → Técnico/Prestador recebe notificação
```

## 4. INTERFACE DOS TÉCNICOS

### 4.1 Página Flip-Card Reformada

**Arquivo**: `web/templates/tecnico/ordem_detail_flip.html`

```html
<!-- Baseada no calendar_flip.html -->
<div class="flip-container">
    <div class="flip-card" id="orderFlipCard">
        <!-- Frente: Lista de ordens -->
        <div class="flip-card-front">
            <div class="orders-list">
                <!-- Ordens atribuídas ao técnico -->
            </div>
        </div>
        
        <!-- Verso: Detalhes da ordem -->
        <div class="flip-card-back">
            <div class="order-details-container">
                <!-- Cards sequenciais -->
            </div>
        </div>
    </div>
</div>
```

### 4.2 Cards Sequenciais para Preenchimento

1. **Card 1**: Informações da ordem (readonly)
2. **Card 2**: Status e progresso
3. **Card 3**: Materiais e custos
4. **Card 4**: Fotos e anexos
5. **Card 5**: Interações e comentários
6. **Card 6**: Finalização e aprovação

### 4.3 Integração com unified_orders.js

```javascript
// tecnico_unified.js
class TechnicianOrderManager extends UnifiedOrderManager {
    async updateOrderStatus(orderId, status) {
        return this.makeRequest(`/api/orders/${orderId}/status`, 'PUT', { status });
    }
    
    async uploadPhoto(orderId, file) {
        const formData = new FormData();
        formData.append('photo', file);
        return this.makeRequest(`/api/orders/${orderId}/photos`, 'POST', formData);
    }
    
    async addInteraction(orderId, message) {
        return this.makeRequest(`/api/orders/${orderId}/interactions`, 'POST', { message });
    }
}
```

## 5. SISTEMA DE NOTIFICAÇÕES

### 5.1 Pipeline Completo

```
Evento → NotificationService → WebSocket + Push + Email → Usuário
```

### 5.2 Configuração VAPID

```bash
# Variáveis de ambiente obrigatórias
VAPID_PUBLIC_KEY=BK8...
VAPID_PRIVATE_KEY=abc...
VAPID_SUBJECT=mailto:<EMAIL>
```

### 5.3 Integração com NotificationService

```go
// Após criação de ordem
func (h *UnifiedOrderHandler) CreateOrder(c *gin.Context) {
    // ... criar ordem ...
    
    // Notificação automática
    go func() {
        if err := h.notificationService.NotifyOrderCreated(order); err != nil {
            log.Printf("Erro ao enviar notificação: %v", err)
        }
    }()
}
```

### 5.4 WebSocket em Tempo Real

```javascript
// Frontend conecta automaticamente
const ws = new WebSocket(`ws://${location.host}/ws`);
ws.onmessage = (event) => {
    const notification = JSON.parse(event.data);
    showNotification(notification);
    updateOrdersList(); // Atualizar interface
};
```

## 6. REGRAS DE SEGURANÇA

### 6.1 Bloqueio Absoluto da Ordem #18

```go
// Em TODOS os endpoints de ordem
func (h *UnifiedOrderHandler) GetOrder(c *gin.Context) {
    id, _ := strconv.ParseUint(c.Param("id"), 10, 32)
    
    // BLOQUEIO OBRIGATÓRIO
    if id == 18 {
        c.JSON(http.StatusForbidden, StandardResponse{
            Success: false,
            Message: "Ordem não disponível",
            Error:   "Esta ordem não está disponível para visualização",
        })
        return
    }
}
```

### 6.2 Sistema RBAC

```go
// Middleware de autenticação obrigatório
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := extractToken(c)
        claims, err := validateJWT(token)
        if err != nil {
            c.JSON(401, StandardResponse{Success: false, Message: "Token inválido"})
            c.Abort()
            return
        }
        
        c.Set("userID", claims.UserID)
        c.Set("userRole", claims.Role)
        c.Next()
    }
}
```

### 6.3 Validação de Permissões por Recurso

```go
// Verificação granular por filial
func (h *UnifiedOrderHandler) ListOrders(c *gin.Context) {
    userID, userRole := h.getUserInfo(c)
    
    switch userRole {
    case "admin":
        // Vê todas as ordens
    case "filial":
        // Vê apenas ordens da sua filial
        filters.BranchID = getUserBranchID(userID)
    case "technician":
        // Vê apenas ordens atribuídas
        filters.TechnicianID = userID
    }
}
```

## 7. PADRÕES DE DESENVOLVIMENTO

### 7.1 Convenções de Nomenclatura

- **Idioma**: Inglês para código, português para UI
- **Tabelas**: Plural (maintenance_orders, users, notifications)
- **Modelos**: Singular (MaintenanceOrder, User, Notification)
- **Handlers**: Sufixo Handler (UnifiedOrderHandler)
- **Services**: Sufixo Service (MaintenanceOrderService)

### 7.2 Estrutura de Testes Obrigatórios

```go
// tests/integration/unified_order_flow_test.go
func TestCompleteOrderFlow(t *testing.T) {
    // 1. Criar ordem
    // 2. Validar vínculos
    // 3. Atribuir técnico
    // 4. Verificar notificações
    // 5. Atualizar status
    // 6. Finalizar ordem
}

func TestOrderSecurity(t *testing.T) {
    // Verificar bloqueio ordem #18
    // Testar RBAC
    // Validar permissões
}
```

### 7.3 Métricas de Qualidade

- **Duplicação**: 0% (obrigatório)
- **Cobertura de testes**: ≥80%
- **Performance**: ≥90% endpoints < 200ms
- **Conformidade arquitetural**: 100%

### 7.4 Processo de Desenvolvimento

```bash
# 1. Desenvolvimento
make dev

# 2. Testes
make test
make test-coverage

# 3. Verificação de qualidade
make check-architecture
make check-duplications

# 4. Deploy (só se 100% conforme)
make deploy-check
make deploy
```

## 8. CONFIGURAÇÃO E DEPLOY

### 8.1 Variáveis de Ambiente

```bash
# Banco de dados
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradicao
DB_USER=postgres
DB_PASSWORD=senha

# JWT
JWT_SECRET=chave-secreta-jwt

# Notificações
VAPID_PUBLIC_KEY=BK8...
VAPID_PRIVATE_KEY=abc...
VAPID_SUBJECT=mailto:<EMAIL>

# WebSocket
WS_PORT=8080
WS_ALLOWED_ORIGINS=*

# Redis (opcional)
REDIS_URL=redis://localhost:6379
```

### 8.2 Configuração de Banco de Dados

```sql
-- Migrações obrigatórias
CREATE TABLE maintenance_orders (
    id SERIAL PRIMARY KEY,
    number VARCHAR(50) UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    branch_id INTEGER NOT NULL,
    equipment_id INTEGER,
    technician_id INTEGER,
    provider_id INTEGER,
    created_by_user_id INTEGER NOT NULL,
    open_date TIMESTAMP DEFAULT NOW(),
    completion_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Vínculos técnico-filial
CREATE TABLE technician_branches (
    technician_id INTEGER,
    branch_id INTEGER,
    PRIMARY KEY (technician_id, branch_id)
);

-- Vínculos técnico-tipo_equipamento
CREATE TABLE technician_equipment_types (
    technician_id INTEGER,
    equipment_type_id INTEGER,
    PRIMARY KEY (technician_id, equipment_type_id)
);
```

### 8.3 Comandos Úteis

```bash
# Desenvolvimento
make dev          # Executar em modo desenvolvimento
make test         # Executar todos os testes
make migrate-up   # Aplicar migrações
make seed-dev     # Popular dados de desenvolvimento

# Qualidade
make check-architecture  # Verificar conformidade
make check-duplications  # Verificar duplicações
make test-coverage      # Relatório de cobertura

# Deploy
make build        # Compilar para produção
make deploy-check # Verificação pré-deploy
make deploy       # Deploy com verificações
```

## 9. TROUBLESHOOTING

### 9.1 Problemas Comuns

**Erro: "Ordem não disponível"**
- Causa: Tentativa de acessar ordem #18
- Solução: Bloqueio intencional por segurança

**Erro: "Técnico não qualificado"**
- Causa: Técnico sem vínculo com filial ou tipo de equipamento
- Solução: Verificar tabelas technician_branches e technician_equipment_types

**Erro: "Notificação não enviada"**
- Causa: VAPID keys não configuradas
- Solução: Configurar variáveis VAPID_* no ambiente

### 9.2 Logs Estruturados

```go
log.Printf("[DEBUG] GetAvailableTechnicians - Filtros: branchID=%d, equipmentTypeID=%d", branchID, equipmentTypeID)
log.Printf("[ERROR] CreateOrder - Erro ao criar ordem: %v", err)
log.Printf("[INFO] NotificationSent - UserID=%d, OrderID=%d", userID, orderID)
```

### 9.3 Monitoramento

- **Métricas**: Prometheus + Grafana
- **Logs**: ELK Stack ou similar
- **Alertas**: PagerDuty ou similar
- **Health Check**: `/health` endpoint

## 10. EXEMPLOS PRÁTICOS

### 10.1 Handler Completo

```go
func (h *UnifiedOrderHandler) CreateOrder(c *gin.Context) {
    // 1. Autenticação
    userID, _ := h.getUserInfo(c)
    if userID == 0 {
        c.JSON(401, StandardResponse{Success: false, Message: "Não autenticado"})
        return
    }

    // 2. Validação de dados
    var orderData models.MaintenanceOrder
    if err := c.ShouldBindJSON(&orderData); err != nil {
        c.JSON(400, StandardResponse{Success: false, Error: err.Error()})
        return
    }

    // 3. Lógica de negócio
    orderData.CreatedByUserID = userID
    orderData.Status = models.StatusPending
    
    // 4. Persistência
    if err := h.service.CreateOrder(&orderData, int(userID)); err != nil {
        c.JSON(500, StandardResponse{Success: false, Error: err.Error()})
        return
    }

    // 5. Notificação
    go h.notificationService.NotifyOrderCreated(&orderData)

    // 6. Resposta padronizada
    c.JSON(201, StandardResponse{
        Success: true,
        Message: "Ordem criada com sucesso",
        Data:    orderData,
    })
}
```

### 10.2 Teste de Integração

```go
func TestOrderCreationFlow(t *testing.T) {
    // Setup
    router := setupTestRouter()
    
    // 1. Criar ordem
    orderData := map[string]interface{}{
        "title": "Teste",
        "branch_id": 1,
        "equipment_id": 1,
    }
    
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/api/orders", jsonBody(orderData))
    req.Header.Set("Authorization", "Bearer "+testToken)
    router.ServeHTTP(w, req)
    
    // 2. Verificar resposta
    assert.Equal(t, 201, w.Code)
    
    var response StandardResponse
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response.Success)
    
    // 3. Verificar notificação
    // ... verificar se notificação foi enviada
}
```

### 10.3 Frontend Integration

```javascript
// unified_orders.js
class UnifiedOrderManager {
    constructor() {
        this.baseURL = '/api/orders';
        this.cache = new Map();
    }
    
    async createOrder(orderData) {
        const response = await this.makeRequest('', 'POST', orderData);
        this.invalidateCache();
        return response;
    }
    
    async makeRequest(endpoint, method = 'GET', data = null) {
        const url = `${this.baseURL}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getToken()}`
            }
        };
        
        if (data) options.body = JSON.stringify(data);
        
        const response = await fetch(url, options);
        return response.json();
    }
}
```

---

## CONCLUSÃO

Esta documentação estabelece os padrões obrigatórios para o Sistema Tradição, garantindo:

✅ **Arquitetura unificada** com eliminação total de duplicações  
✅ **Fluxo completo** de criação → atribuição → notificação  
✅ **Interface moderna** para técnicos com flip-cards  
✅ **Segurança robusta** com RBAC e bloqueios  
✅ **Qualidade garantida** com testes e métricas  
✅ **Deploy seguro** com verificações automáticas  

O sistema está preparado para escalar mantendo performance, segurança e experiência do usuário em alto nível.