package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Test models - these should match your actual models
type User struct {
	ID       uint   `gorm:"primaryKey" json:"id"`
	Name     string `gorm:"not null" json:"name"`
	Email    string `gorm:"unique;not null" json:"email"`
	Role     string `gorm:"not null" json:"role"` // admin, filial, technician, provider
	BranchID *uint  `json:"branch_id"`
	Active   bool   `gorm:"default:true" json:"active"`
}

type Branch struct {
	ID     uint   `gorm:"primaryKey" json:"id"`
	Name   string `gorm:"not null" json:"name"`
	Code   string `gorm:"unique;not null" json:"code"`
	Active bool   `gorm:"default:true" json:"active"`
}

type EquipmentType struct {
	ID   uint   `gorm:"primaryKey" json:"id"`
	Name string `gorm:"not null" json:"name"`
	Code string `gorm:"unique;not null" json:"code"`
}

type Equipment struct {
	ID              uint          `gorm:"primaryKey" json:"id"`
	Name            string        `gorm:"not null" json:"name"`
	Code            string        `gorm:"unique;not null" json:"code"`
	BranchID        uint          `gorm:"not null" json:"branch_id"`
	EquipmentTypeID uint          `gorm:"not null" json:"equipment_type_id"`
	Branch          Branch        `gorm:"foreignKey:BranchID" json:"branch"`
	EquipmentType   EquipmentType `gorm:"foreignKey:EquipmentTypeID" json:"equipment_type"`
}

type Provider struct {
	ID       uint   `gorm:"primaryKey" json:"id"`
	Name     string `gorm:"not null" json:"name"`
	Email    string `gorm:"unique;not null" json:"email"`
	Phone    string `json:"phone"`
	BranchID uint   `gorm:"not null" json:"branch_id"`
	Active   bool   `gorm:"default:true" json:"active"`
}

type TechnicianEquipmentLink struct {
	ID              uint `gorm:"primaryKey" json:"id"`
	TechnicianID    uint `gorm:"not null" json:"technician_id"`
	EquipmentTypeID uint `gorm:"not null" json:"equipment_type_id"`
	BranchID        uint `gorm:"not null" json:"branch_id"`
}

type MaintenanceOrder struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	Description     string    `gorm:"not null" json:"description"`
	Status          string    `gorm:"not null;default:'pending'" json:"status"`
	Priority        string    `gorm:"not null;default:'medium'" json:"priority"`
	BranchID        uint      `gorm:"not null" json:"branch_id"`
	EquipmentID     uint      `gorm:"not null" json:"equipment_id"`
	CreatedByID     uint      `gorm:"not null" json:"created_by_id"`
	AssignedToID    *uint     `json:"assigned_to_id"`
	AssignedType    string    `json:"assigned_type"` // technician or provider
	ProviderID      *uint     `json:"provider_id"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	Branch          Branch    `gorm:"foreignKey:BranchID" json:"branch"`
	Equipment       Equipment `gorm:"foreignKey:EquipmentID" json:"equipment"`
	CreatedBy       User      `gorm:"foreignKey:CreatedByID" json:"created_by"`
	AssignedTo      *User     `gorm:"foreignKey:AssignedToID" json:"assigned_to"`
	Provider        *Provider `gorm:"foreignKey:ProviderID" json:"provider"`
}

type StandardResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    interface{} `json:"meta,omitempty"`
}

type PaginationMeta struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// Mock notification service
type MockNotificationService struct {
	SentNotifications []NotificationEvent
}

type NotificationEvent struct {
	Type      string      `json:"type"`
	UserID    uint        `json:"user_id"`
	OrderID   uint        `json:"order_id"`
	Message   string      `json:"message"`
	Timestamp time.Time   `json:"timestamp"`
	Data      interface{} `json:"data"`
}

func (m *MockNotificationService) NotifyOrderCreated(order *MaintenanceOrder) error {
	m.SentNotifications = append(m.SentNotifications, NotificationEvent{
		Type:      "order_created",
		UserID:    *order.AssignedToID,
		OrderID:   order.ID,
		Message:   fmt.Sprintf("New order #%d assigned to you", order.ID),
		Timestamp: time.Now(),
		Data:      order,
	})
	return nil
}

func (m *MockNotificationService) NotifyOrderAssigned(orderID, assignedToID uint, assignedType string) error {
	m.SentNotifications = append(m.SentNotifications, NotificationEvent{
		Type:      "order_assigned",
		UserID:    assignedToID,
		OrderID:   orderID,
		Message:   fmt.Sprintf("Order #%d has been assigned to you", orderID),
		Timestamp: time.Now(),
	})
	return nil
}

func (m *MockNotificationService) NotifyStatusChanged(orderID uint, newStatus string, userID uint) error {
	m.SentNotifications = append(m.SentNotifications, NotificationEvent{
		Type:      "status_changed",
		UserID:    userID,
		OrderID:   orderID,
		Message:   fmt.Sprintf("Order #%d status changed to %s", orderID, newStatus),
		Timestamp: time.Now(),
	})
	return nil
}

func (m *MockNotificationService) Reset() {
	m.SentNotifications = []NotificationEvent{}
}

// UnifiedOrderFlowTestSuite contains all integration tests for the unified order flow
type UnifiedOrderFlowTestSuite struct {
	suite.Suite
	DB                  *gorm.DB
	Router              *gin.Engine
	NotificationService *MockNotificationService
	TestUsers           map[string]*User
	TestBranches        map[string]*Branch
	TestEquipmentTypes  map[string]*EquipmentType
	TestEquipments      map[string]*Equipment
	TestProviders       map[string]*Provider
}

// SetupSuite runs once before all tests in the suite
func (s *UnifiedOrderFlowTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	s.Require().NoError(err)
	s.DB = db

	// Run migrations
	err = s.DB.AutoMigrate(
		&User{},
		&Branch{},
		&EquipmentType{},
		&Equipment{},
		&Provider{},
		&TechnicianEquipmentLink{},
		&MaintenanceOrder{},
	)
	s.Require().NoError(err)

	// Setup mock notification service
	s.NotificationService = &MockNotificationService{}

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	s.Router = gin.New()
	s.setupRoutes()

	// Create test data
	s.createTestData()
}

// TearDownTest runs after each test
func (s *UnifiedOrderFlowTestSuite) TearDownTest() {
	// Clean up orders and reset notification service
	s.DB.Exec("DELETE FROM maintenance_orders")
	s.NotificationService.Reset()
}

// TearDownSuite runs once after all tests in the suite
func (s *UnifiedOrderFlowTestSuite) TearDownSuite() {
	db, _ := s.DB.DB()
	db.Close()
}

// setupRoutes configures the test routes
func (s *UnifiedOrderFlowTestSuite) setupRoutes() {
	// Auth middleware mock
	authMiddleware := func(c *gin.Context) {
		userID := c.GetHeader("X-User-ID")
		userRole := c.GetHeader("X-User-Role")
		branchID := c.GetHeader("X-Branch-ID")

		if userID != "" {
			c.Set("user_id", userID)
			c.Set("user_role", userRole)
			c.Set("branch_id", branchID)
		}
		c.Next()
	}

	api := s.Router.Group("/api", authMiddleware)
	{
		orders := api.Group("/orders")
		{
			orders.GET("", s.listOrders)
			orders.GET("/:id", s.getOrder)
			orders.POST("", s.createOrder)
			orders.PUT("/:id/status", s.updateOrderStatus)
			orders.POST("/:id/assign", s.assignOrder)
			orders.GET("/technician", s.getTechnicianOrders)
			orders.GET("/available-providers", s.getAvailableProviders)
			orders.GET("/available-technicians", s.getAvailableTechnicians)
		}
	}

	// Legacy redirects (should return 301)
	s.Router.GET("/api/ordens", s.legacyRedirect)
	s.Router.GET("/api/ordens/:id", s.legacyRedirect)
	s.Router.POST("/api/ordens", s.legacyRedirect)
	s.Router.GET("/verordens", s.legacyRedirect)
}

// createTestData creates test data for all tests
func (s *UnifiedOrderFlowTestSuite) createTestData() {
	s.TestUsers = make(map[string]*User)
	s.TestBranches = make(map[string]*Branch)
	s.TestEquipmentTypes = make(map[string]*EquipmentType)
	s.TestEquipments = make(map[string]*Equipment)
	s.TestProviders = make(map[string]*Provider)

	// Create branches
	branch1 := &Branch{Name: "Branch 1", Code: "BR001", Active: true}
	s.DB.Create(branch1)
	s.TestBranches["branch1"] = branch1

	branch2 := &Branch{Name: "Branch 2", Code: "BR002", Active: true}
	s.DB.Create(branch2)
	s.TestBranches["branch2"] = branch2

	// Create equipment types
	equipType1 := &EquipmentType{Name: "Air Conditioner", Code: "AC"}
	s.DB.Create(equipType1)
	s.TestEquipmentTypes["ac"] = equipType1

	equipType2 := &EquipmentType{Name: "Elevator", Code: "EL"}
	s.DB.Create(equipType2)
	s.TestEquipmentTypes["elevator"] = equipType2

	// Create equipment
	equipment1 := &Equipment{
		Name:            "AC Unit 1",
		Code:            "AC001",
		BranchID:        branch1.ID,
		EquipmentTypeID: equipType1.ID,
	}
	s.DB.Create(equipment1)
	s.TestEquipments["ac1"] = equipment1

	equipment2 := &Equipment{
		Name:            "Elevator 1",
		Code:            "EL001",
		BranchID:        branch1.ID,
		EquipmentTypeID: equipType2.ID,
	}
	s.DB.Create(equipment2)
	s.TestEquipments["el1"] = equipment2

	// Create users
	admin := &User{Name: "Admin User", Email: "<EMAIL>", Role: "admin", Active: true}
	s.DB.Create(admin)
	s.TestUsers["admin"] = admin

	filial := &User{Name: "Branch User", Email: "<EMAIL>", Role: "filial", BranchID: &branch1.ID, Active: true}
	s.DB.Create(filial)
	s.TestUsers["filial"] = filial

	technician := &User{Name: "Technician", Email: "<EMAIL>", Role: "technician", BranchID: &branch1.ID, Active: true}
	s.DB.Create(technician)
	s.TestUsers["technician"] = technician

	provider := &User{Name: "Provider User", Email: "<EMAIL>", Role: "provider", Active: true}
	s.DB.Create(provider)
	s.TestUsers["provider"] = provider

	// Create providers
	testProvider := &Provider{
		Name:     "Test Provider",
		Email:    "<EMAIL>",
		Phone:    "123456789",
		BranchID: branch1.ID,
		Active:   true,
	}
	s.DB.Create(testProvider)
	s.TestProviders["provider1"] = testProvider

	// Create technician-equipment links
	techLink := &TechnicianEquipmentLink{
		TechnicianID:    technician.ID,
		EquipmentTypeID: equipType1.ID,
		BranchID:        branch1.ID,
	}
	s.DB.Create(techLink)
}

// Test 1: Complete order creation flow
func (s *UnifiedOrderFlowTestSuite) TestCompleteOrderCreationFlow() {
	// Test the complete flow: branch → equipment → providers/technicians → creation → notification

	// Step 1: Get available providers for equipment type
	req := httptest.NewRequest("GET", "/api/orders/available-providers?branch_id=1&equipment_type_id=1", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var providersResp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &providersResp)
	s.NoError(err)
	s.True(providersResp.Success)

	// Step 2: Get available technicians for equipment type
	req = httptest.NewRequest("GET", "/api/orders/available-technicians?branch_id=1&equipment_type_id=1", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var techniciansResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &techniciansResp)
	s.NoError(err)
	s.True(techniciansResp.Success)

	// Verify only linked technicians appear
	technicians := techniciansResp.Data.([]interface{})
	s.Len(technicians, 1) // Only one technician linked to AC equipment type

	// Step 3: Create order with technician assignment
	orderData := map[string]interface{}{
		"description":     "AC maintenance required",
		"priority":        "high",
		"branch_id":       1,
		"equipment_id":    s.TestEquipments["ac1"].ID,
		"assigned_to_id":  s.TestUsers["technician"].ID,
		"assigned_type":   "technician",
	}

	orderJSON, _ := json.Marshal(orderData)
	req = httptest.NewRequest("POST", "/api/orders", bytes.NewBuffer(orderJSON))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusCreated, w.Code)
	var createResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &createResp)
	s.NoError(err)
	s.True(createResp.Success)

	// Step 4: Verify notification was sent
	s.Len(s.NotificationService.SentNotifications, 1)
	notification := s.NotificationService.SentNotifications[0]
	s.Equal("order_created", notification.Type)
	s.Equal(s.TestUsers["technician"].ID, notification.UserID)
}

func (s *UnifiedOrderFlowTestSuite) TestOrderCreationBlocksOrder18() {
	// Test that order #18 is blocked
	orderData := map[string]interface{}{
		"id":              18,
		"description":     "Blocked order",
		"priority":        "high",
		"branch_id":       1,
		"equipment_id":    s.TestEquipments["ac1"].ID,
		"assigned_to_id":  s.TestUsers["technician"].ID,
		"assigned_type":   "technician",
	}

	orderJSON, _ := json.Marshal(orderData)
	req := httptest.NewRequest("POST", "/api/orders", bytes.NewBuffer(orderJSON))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusForbidden, w.Code)
	var resp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	s.NoError(err)
	s.False(resp.Success)
	s.Contains(resp.Error, "Order #18 is blocked")
}

// Test 2: Order assignment
func (s *UnifiedOrderFlowTestSuite) TestOrderAssignment() {
	// Create an order first
	order := &MaintenanceOrder{
		Description:  "Test assignment",
		Status:       "pending",
		Priority:     "medium",
		BranchID:     s.TestBranches["branch1"].ID,
		EquipmentID:  s.TestEquipments["ac1"].ID,
		CreatedByID:  s.TestUsers["filial"].ID,
	}
	s.DB.Create(order)

	// Test assignment to technician
	assignData := map[string]interface{}{
		"assigned_to_id": s.TestUsers["technician"].ID,
		"assigned_type":  "technician",
	}

	assignJSON, _ := json.Marshal(assignData)
	req := httptest.NewRequest("POST", fmt.Sprintf("/api/orders/%d/assign", order.ID), bytes.NewBuffer(assignJSON))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	s.NoError(err)
	s.True(resp.Success)

	// Verify assignment notification was sent
	s.Len(s.NotificationService.SentNotifications, 1)
	notification := s.NotificationService.SentNotifications[0]
	s.Equal("order_assigned", notification.Type)
	s.Equal(s.TestUsers["technician"].ID, notification.UserID)
}

// Test 3: Unified endpoints
func (s *UnifiedOrderFlowTestSuite) TestUnifiedEndpoints() {
	// Create test orders
	order1 := &MaintenanceOrder{
		Description:    "Order 1",
		Status:         "pending",
		Priority:       "high",
		BranchID:       s.TestBranches["branch1"].ID,
		EquipmentID:    s.TestEquipments["ac1"].ID,
		CreatedByID:    s.TestUsers["filial"].ID,
		AssignedToID:   &s.TestUsers["technician"].ID,
		AssignedType:   "technician",
	}
	s.DB.Create(order1)

	order2 := &MaintenanceOrder{
		Description:  "Order 2",
		Status:       "in_progress",
		Priority:     "medium",
		BranchID:     s.TestBranches["branch2"].ID,
		EquipmentID:  s.TestEquipments["el1"].ID,
		CreatedByID:  s.TestUsers["admin"].ID,
	}
	s.DB.Create(order2)

	// Test GET /api/orders with pagination and filters
	req := httptest.NewRequest("GET", "/api/orders?page=1&per_page=10&status=pending", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var listResp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &listResp)
	s.NoError(err)
	s.True(listResp.Success)
	s.NotNil(listResp.Meta)

	// Test GET /api/orders/:id
	req = httptest.NewRequest("GET", fmt.Sprintf("/api/orders/%d", order1.ID), nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var getResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &getResp)
	s.NoError(err)
	s.True(getResp.Success)

	// Test GET /api/orders/technician
	req = httptest.NewRequest("GET", "/api/orders/technician", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["technician"].ID))
	req.Header.Set("X-User-Role", "technician")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var techResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &techResp)
	s.NoError(err)
	s.True(techResp.Success)
}

// Test 4: RBAC permissions
func (s *UnifiedOrderFlowTestSuite) TestRBACPermissions() {
	// Create orders for different branches
	order1 := &MaintenanceOrder{
		Description:  "Branch 1 Order",
		Status:       "pending",
		Priority:     "high",
		BranchID:     s.TestBranches["branch1"].ID,
		EquipmentID:  s.TestEquipments["ac1"].ID,
		CreatedByID:  s.TestUsers["filial"].ID,
	}
	s.DB.Create(order1)

	order2 := &MaintenanceOrder{
		Description:  "Branch 2 Order",
		Status:       "pending",
		Priority:     "medium",
		BranchID:     s.TestBranches["branch2"].ID,
		EquipmentID:  s.TestEquipments["el1"].ID,
		CreatedByID:  s.TestUsers["admin"].ID,
	}
	s.DB.Create(order2)

	// Test admin sees all orders
	req := httptest.NewRequest("GET", "/api/orders", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var adminResp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &adminResp)
	s.NoError(err)
	s.True(adminResp.Success)

	// Test filial user sees only their branch orders
	req = httptest.NewRequest("GET", "/api/orders", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var filialResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &filialResp)
	s.NoError(err)
	s.True(filialResp.Success)

	// Test technician sees only assigned orders
	order1.AssignedToID = &s.TestUsers["technician"].ID
	order1.AssignedType = "technician"
	s.DB.Save(order1)

	req = httptest.NewRequest("GET", "/api/orders/technician", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["technician"].ID))
	req.Header.Set("X-User-Role", "technician")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var techResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &techResp)
	s.NoError(err)
	s.True(techResp.Success)
}

// Test 5: Legacy redirects
func (s *UnifiedOrderFlowTestSuite) TestLegacyRedirects() {
	// Test legacy endpoints return 301 redirects
	legacyEndpoints := []string{
		"/api/ordens",
		"/api/ordens/1",
		"/verordens",
	}

	for _, endpoint := range legacyEndpoints {
		req := httptest.NewRequest("GET", endpoint, nil)
		w := httptest.NewRecorder()
		s.Router.ServeHTTP(w, req)

		s.Equal(http.StatusMovedPermanently, w.Code, "Endpoint %s should redirect", endpoint)
		s.NotEmpty(w.Header().Get("Location"), "Redirect location should be set for %s", endpoint)
	}
}

// Test 6: Performance and optimization
func (s *UnifiedOrderFlowTestSuite) TestPerformanceOptimizations() {
	// Create multiple orders to test pagination performance
	for i := 0; i < 50; i++ {
		order := &MaintenanceOrder{
			Description:  fmt.Sprintf("Order %d", i),
			Status:       "pending",
			Priority:     "medium",
			BranchID:     s.TestBranches["branch1"].ID,
			EquipmentID:  s.TestEquipments["ac1"].ID,
			CreatedByID:  s.TestUsers["filial"].ID,
		}
		s.DB.Create(order)
	}

	// Test pagination with large dataset
	start := time.Now()
	req := httptest.NewRequest("GET", "/api/orders?page=1&per_page=20", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	duration := time.Since(start)

	s.Equal(http.StatusOK, w.Code)
	s.Less(duration, 100*time.Millisecond, "Query should be fast")

	var resp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	s.NoError(err)
	s.True(resp.Success)
	s.NotNil(resp.Meta)

	// Verify pagination metadata
	meta := resp.Meta.(map[string]interface{})
	s.Equal(float64(1), meta["page"])
	s.Equal(float64(20), meta["per_page"])
	s.Greater(meta["total"], float64(0))
}

// Test 7: Notification system
func (s *UnifiedOrderFlowTestSuite) TestNotificationSystem() {
	// Create order
	order := &MaintenanceOrder{
		Description:    "Notification test",
		Status:         "pending",
		Priority:       "high",
		BranchID:       s.TestBranches["branch1"].ID,
		EquipmentID:    s.TestEquipments["ac1"].ID,
		CreatedByID:    s.TestUsers["filial"].ID,
		AssignedToID:   &s.TestUsers["technician"].ID,
		AssignedType:   "technician",
	}
	s.DB.Create(order)

	// Simulate order creation notification
	err := s.NotificationService.NotifyOrderCreated(order)
	s.NoError(err)

	// Test status change notification
	err = s.NotificationService.NotifyStatusChanged(order.ID, "in_progress", s.TestUsers["technician"].ID)
	s.NoError(err)

	// Verify notifications were sent
	s.Len(s.NotificationService.SentNotifications, 2)

	// Verify notification content
	createNotification := s.NotificationService.SentNotifications[0]
	s.Equal("order_created", createNotification.Type)
	s.Equal(s.TestUsers["technician"].ID, createNotification.UserID)

	statusNotification := s.NotificationService.SentNotifications[1]
	s.Equal("status_changed", statusNotification.Type)
	s.Contains(statusNotification.Message, "in_progress")
}

// Test 8: Frontend integration simulation
func (s *UnifiedOrderFlowTestSuite) TestFrontendIntegration() {
	// Simulate frontend workflow: get equipment types, then technicians, then create order
	
	// Step 1: Frontend gets available technicians for equipment type
	req := httptest.NewRequest("GET", "/api/orders/available-technicians?branch_id=1&equipment_type_id=1", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var techResp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &techResp)
	s.NoError(err)
	s.True(techResp.Success)

	// Step 2: Frontend creates order with selected technician
	orderData := map[string]interface{}{
		"description":     "Frontend integration test",
		"priority":        "high",
		"branch_id":       1,
		"equipment_id":    s.TestEquipments["ac1"].ID,
		"assigned_to_id":  s.TestUsers["technician"].ID,
		"assigned_type":   "technician",
	}

	orderJSON, _ := json.Marshal(orderData)
	req = httptest.NewRequest("POST", "/api/orders", bytes.NewBuffer(orderJSON))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)

	s.Equal(http.StatusCreated, w.Code)
	var createResp StandardResponse
	err = json.Unmarshal(w.Body.Bytes(), &createResp)
	s.NoError(err)
	s.True(createResp.Success)

	// Step 3: Verify real-time notification would be sent
	s.Len(s.NotificationService.SentNotifications, 1)
}

// Handler implementations for testing
func (s *UnifiedOrderFlowTestSuite) listOrders(c *gin.Context) {
	userRole := c.GetString("user_role")
	branchID := c.GetString("branch_id")
	
	page := 1
	perPage := 10
	
	query := s.DB.Model(&MaintenanceOrder{}).Preload("Branch").Preload("Equipment").Preload("CreatedBy")
	
	// Apply RBAC filters
	if userRole == "filial" && branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}
	
	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	
	var total int64
	query.Count(&total)
	
	var orders []MaintenanceOrder
	offset := (page - 1) * perPage
	query.Offset(offset).Limit(perPage).Find(&orders)
	
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Orders retrieved successfully",
		Data:    orders,
		Meta: PaginationMeta{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

func (s *UnifiedOrderFlowTestSuite) getOrder(c *gin.Context) {
	id := c.Param("id")
	
	// Block order #18
	if id == "18" {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Error:   "Order #18 is blocked for security reasons",
		})
		return
	}
	
	var order MaintenanceOrder
	if err := s.DB.Preload("Branch").Preload("Equipment").Preload("CreatedBy").First(&order, id).Error; err != nil {
		c.JSON(http.StatusNotFound, StandardResponse{
			Success: false,
			Error:   "Order not found",
		})
		return
	}
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Order retrieved successfully",
		Data:    order,
	})
}

func (s *UnifiedOrderFlowTestSuite) createOrder(c *gin.Context) {
	var orderData MaintenanceOrder
	if err := c.ShouldBindJSON(&orderData); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Error:   "Invalid request data",
		})
		return
	}
	
	// Block order #18
	if orderData.ID == 18 {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Error:   "Order #18 is blocked for security reasons",
		})
		return
	}
	
	// Set created by from context
	userID := c.GetString("user_id")
	if userID != "" {
		fmt.Sscanf(userID, "%d", &orderData.CreatedByID)
	}
	
	if err := s.DB.Create(&orderData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Error:   "Failed to create order",
		})
		return
	}
	
	// Send notification if assigned
	if orderData.AssignedToID != nil {
		s.NotificationService.NotifyOrderCreated(&orderData)
	}
	
	c.JSON(http.StatusCreated, StandardResponse{
		Success: true,
		Message: "Order created successfully",
		Data:    orderData,
	})
}

func (s *UnifiedOrderFlowTestSuite) updateOrderStatus(c *gin.Context) {
	id := c.Param("id")
	
	var updateData struct {
		Status string `json:"status"`
	}
	
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Error:   "Invalid request data",
		})
		return
	}
	
	var order MaintenanceOrder
	if err := s.DB.First(&order, id).Error; err != nil {
		c.JSON(http.StatusNotFound, StandardResponse{
			Success: false,
			Error:   "Order not found",
		})
		return
	}
	
	order.Status = updateData.Status
	s.DB.Save(&order)
	
	// Send status change notification
	if order.AssignedToID != nil {
		s.NotificationService.NotifyStatusChanged(order.ID, updateData.Status, *order.AssignedToID)
	}
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Order status updated successfully",
		Data:    order,
	})
}

func (s *UnifiedOrderFlowTestSuite) assignOrder(c *gin.Context) {
	id := c.Param("id")
	
	var assignData struct {
		AssignedToID uint   `json:"assigned_to_id"`
		AssignedType string `json:"assigned_type"`
	}
	
	if err := c.ShouldBindJSON(&assignData); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Error:   "Invalid request data",
		})
		return
	}
	
	var order MaintenanceOrder
	if err := s.DB.First(&order, id).Error; err != nil {
		c.JSON(http.StatusNotFound, StandardResponse{
			Success: false,
			Error:   "Order not found",
		})
		return
	}
	
	order.AssignedToID = &assignData.AssignedToID
	order.AssignedType = assignData.AssignedType
	s.DB.Save(&order)
	
	// Send assignment notification
	s.NotificationService.NotifyOrderAssigned(order.ID, assignData.AssignedToID, assignData.AssignedType)
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Order assigned successfully",
		Data:    order,
	})
}

func (s *UnifiedOrderFlowTestSuite) getTechnicianOrders(c *gin.Context) {
	userID := c.GetString("user_id")
	
	var orders []MaintenanceOrder
	s.DB.Where("assigned_to_id = ? AND assigned_type = ?", userID, "technician").
		Preload("Branch").Preload("Equipment").Find(&orders)
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Technician orders retrieved successfully",
		Data:    orders,
	})
}

func (s *UnifiedOrderFlowTestSuite) getAvailableProviders(c *gin.Context) {
	branchID := c.Query("branch_id")
	
	var providers []Provider
	query := s.DB.Where("active = ?", true)
	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}
	query.Find(&providers)
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Available providers retrieved successfully",
		Data:    providers,
	})
}

func (s *UnifiedOrderFlowTestSuite) getAvailableTechnicians(c *gin.Context) {
	branchID := c.Query("branch_id")
	equipmentTypeID := c.Query("equipment_type_id")
	
	var technicians []User
	query := s.DB.Where("role = ? AND active = ?", "technician", true)
	
	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}
	
	// Filter by equipment type link if provided
	if equipmentTypeID != "" {
		var linkedTechnicianIDs []uint
		s.DB.Model(&TechnicianEquipmentLink{}).
			Where("equipment_type_id = ?", equipmentTypeID).
			Pluck("technician_id", &linkedTechnicianIDs)
		
		if len(linkedTechnicianIDs) > 0 {
			query = query.Where("id IN ?", linkedTechnicianIDs)
		} else {
			// No technicians linked to this equipment type
			technicians = []User{}
		}
	}
	
	if len(technicians) == 0 && equipmentTypeID != "" {
		// Only find if we haven't already determined no links exist
		var linkedTechnicianIDs []uint
		s.DB.Model(&TechnicianEquipmentLink{}).
			Where("equipment_type_id = ?", equipmentTypeID).
			Pluck("technician_id", &linkedTechnicianIDs)
		
		if len(linkedTechnicianIDs) > 0 {
			query.Where("id IN ?", linkedTechnicianIDs).Find(&technicians)
		}
	} else if equipmentTypeID == "" {
		query.Find(&technicians)
	}
	
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Available technicians retrieved successfully",
		Data:    technicians,
	})
}

func (s *UnifiedOrderFlowTestSuite) legacyRedirect(c *gin.Context) {
	// Map legacy endpoints to new unified endpoints
	legacyToNew := map[string]string{
		"/api/ordens":    "/api/orders",
		"/verordens":     "/api/orders/technician",
	}
	
	path := c.Request.URL.Path
	if newPath, exists := legacyToNew[path]; exists {
		c.Redirect(http.StatusMovedPermanently, newPath)
		return
	}
	
	// Handle parameterized routes
	if len(path) > 12 && path[:12] == "/api/ordens/" {
		id := path[12:]
		c.Redirect(http.StatusMovedPermanently, "/api/orders/"+id)
		return
	}
	
	c.Redirect(http.StatusMovedPermanently, "/api/orders")
}

// TestMain sets up the test environment
func TestMain(m *testing.M) {
	// Set test environment
	os.Setenv("GIN_MODE", "test")
	
	// Run tests
	code := m.Run()
	
	// Exit with the same code as the tests
	os.Exit(code)
}

// TestUnifiedOrderFlowTestSuite runs the test suite
func TestUnifiedOrderFlowTestSuite(t *testing.T) {
	suite.Run(t, new(UnifiedOrderFlowTestSuite))
}

// Additional specific tests for edge cases
func (s *UnifiedOrderFlowTestSuite) TestTechnicianEquipmentLinkValidation() {
	// Test that technicians without equipment type links don't appear in available technicians
	
	// Create a technician without any equipment links
	unlinkedTech := &User{
		Name:     "Unlinked Technician",
		Email:    "<EMAIL>",
		Role:     "technician",
		BranchID: &s.TestBranches["branch1"].ID,
		Active:   true,
	}
	s.DB.Create(unlinkedTech)
	
	// Request technicians for AC equipment type
	req := httptest.NewRequest("GET", "/api/orders/available-technicians?branch_id=1&equipment_type_id=1", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["filial"].ID))
	req.Header.Set("X-User-Role", "filial")
	req.Header.Set("X-Branch-ID", "1")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	
	s.Equal(http.StatusOK, w.Code)
	var resp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	s.NoError(err)
	s.True(resp.Success)
	
	// Verify only linked technician appears
	technicians := resp.Data.([]interface{})
	s.Len(technicians, 1) // Only the linked technician should appear
}

func (s *UnifiedOrderFlowTestSuite) TestCacheAndPerformanceOptimizations() {
	// Test that queries are optimized and don't cause N+1 problems
	
	// Create multiple orders with relationships
	for i := 0; i < 10; i++ {
		order := &MaintenanceOrder{
			Description:  fmt.Sprintf("Performance test order %d", i),
			Status:       "pending",
			Priority:     "medium",
			BranchID:     s.TestBranches["branch1"].ID,
			EquipmentID:  s.TestEquipments["ac1"].ID,
			CreatedByID:  s.TestUsers["filial"].ID,
		}
		s.DB.Create(order)
	}
	
	// Measure query performance
	start := time.Now()
	req := httptest.NewRequest("GET", "/api/orders?page=1&per_page=10", nil)
	req.Header.Set("X-User-ID", fmt.Sprintf("%d", s.TestUsers["admin"].ID))
	req.Header.Set("X-User-Role", "admin")
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	duration := time.Since(start)
	
	s.Equal(http.StatusOK, w.Code)
	s.Less(duration, 50*time.Millisecond, "Query should be optimized and fast")
	
	var resp StandardResponse
	err := json.Unmarshal(w.Body.Bytes(), &resp)
	s.NoError(err)
	s.True(resp.Success)
	s.NotNil(resp.Meta)
}

func (s *UnifiedOrderFlowTestSuite) TestWebSocketNotificationIntegration() {
	// Test WebSocket notification delivery simulation
	
	order := &MaintenanceOrder{
		Description:    "WebSocket test",
		Status:         "pending",
		Priority:       "high",
		BranchID:       s.TestBranches["branch1"].ID,
		EquipmentID:    s.TestEquipments["ac1"].ID,
		CreatedByID:    s.TestUsers["filial"].ID,
		AssignedToID:   &s.TestUsers["technician"].ID,
		AssignedType:   "technician",
	}
	s.DB.Create(order)
	
	// Simulate WebSocket notification
	err := s.NotificationService.NotifyOrderCreated(order)
	s.NoError(err)
	
	// Verify notification structure for WebSocket delivery
	s.Len(s.NotificationService.SentNotifications, 1)
	notification := s.NotificationService.SentNotifications[0]
	
	s.Equal("order_created", notification.Type)
	s.Equal(s.TestUsers["technician"].ID, notification.UserID)
	s.Equal(order.ID, notification.OrderID)
	s.NotEmpty(notification.Message)
	s.NotNil(notification.Data)
	s.WithinDuration(time.Now(), notification.Timestamp, time.Second)
}