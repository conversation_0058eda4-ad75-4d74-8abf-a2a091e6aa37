{{ define "admin/novo_link_management.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .title }}</title>
    <link rel="icon" type="image/svg+xml" href="/static/images/favicon.svg">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <link href="/static/css/common.css" rel="stylesheet">
    <link href="/static/css/styles.css" rel="stylesheet">
    <link href="/static/css/shell-theme.css" rel="stylesheet">
    <style>
        .novo-link-management { padding: 20px; background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); min-height: 100vh; color: #fff; }
        .page-header { background: linear-gradient(135deg, #f4b31d 0%, #ffd700 100%); color: #1a1a1a; padding: 20px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 4px 15px rgba(244, 179, 29, 0.3); }
        .page-title { font-size: 2.5rem; font-weight: 700; margin: 0; text-transform: uppercase; letter-spacing: 2px; font-family: 'Rajdhani', sans-serif; }
        .page-subtitle { font-size: 1.1rem; margin: 10px 0 0 0; opacity: 0.9; font-family: 'Rajdhani', sans-serif; }
        .management-section { background: rgba(255,255,255,0.05); border: 1px solid rgba(244,179,29,0.3); border-radius: 10px; padding: 25px; margin-bottom: 30px; backdrop-filter: blur(10px); }
        .section-title { color: #f4b31d; font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; text-transform: uppercase; letter-spacing: 1px; font-family: 'Rajdhani', sans-serif; border-bottom: 2px solid #f4b31d; padding-bottom: 10px; }
        .branch-card, .result-card { background: var(--shell-container); border-radius: 18px; border: 1.5px solid rgba(244,179,29,0.3); padding: 1.5rem; text-align: center; cursor: pointer; transition: all 0.2s ease; position: relative; }
        .branch-card:hover, .result-card:hover { transform: translateY(-5px); box-shadow: 0 8px 32px 0 rgba(244,179,29,0.2); border-color: #f4b31d; }
        .result-card { text-align: left; }
        .status-dot { position: absolute; top: 1rem; right: 1rem; width: 12px; height: 12px; border-radius: 50%; }
        .status-dot.online { background-color: #22c55e; }
        .status-dot.offline { background-color: #ef4444; }
        .btn { padding: 8px 16px; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-family: 'Rajdhani', sans-serif; text-transform: uppercase; letter-spacing: 0.5px; font-size: 12px; }
        .btn-warning { background: linear-gradient(135deg, #f4b31d 0%, #ffd700 100%); color: #1a1a1a; }
        .btn-warning:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(244,179,29,0.4); }
        .btn-success { background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); color: #fff; }
        .btn-success:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(34,197,94,0.4); }
        .btn-danger { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: #fff; }
        .btn-danger:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(239,68,68,0.4); }
        .btn-secondary { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: #fff; }
        .btn-secondary:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(107,114,128,0.4); }
        .modal-overlay {
          opacity: 0;
          visibility: hidden;
          position: fixed;
          inset: 0;
          z-index: 9999;
          background-color: rgba(0,0,0,0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: opacity 0.3s, visibility 0.3s;
        }
        .modal-overlay.is-visible {
          opacity: 1;
          visibility: visible;
        }
        .modal-content-custom {
          background: #232a34;
          border-radius: 18px;
          box-shadow: 0 8px 32px 0 rgba(0,0,0,0.37);
          padding: 20px;
          max-height: 90vh;
          overflow-y: auto;
        }
        .card-dark {
          background: rgba(255,255,255,0.05);
          border: 1px solid rgba(244,179,29,0.3);
          border-radius: 10px;
        }
        .card-body {
          padding: 20px;
        }
        .table-dark {
          background: rgba(255,255,255,0.02);
        }
        .table-dark th, .table-dark td {
          border-color: rgba(244,179,29,0.2);
          padding: 12px;
        }
        .table-striped tbody tr:nth-of-type(odd) {
          background: rgba(255,255,255,0.02);
        }
        .form-select, .form-control {
          background: rgba(255,255,255,0.1) !important;
          border: 1px solid rgba(244,179,29,0.5) !important;
          color: #fff !important;
        }
        .form-select option {
          background: #232a34;
          color: #fff;
        }
        .alert-info {
          background: rgba(244,179,29,0.1);
          border: 1px solid rgba(244,179,29,0.3);
          color: #f4b31d;
        }
    </style>
</head>
<body class="shell-theme">
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="novo-link-management">
                <div class="page-header">
                    <h1 class="page-title">Gerenciamento de Vínculos</h1>
                    <p class="page-subtitle">Busque por filial, técnico ou prestadora para gerenciar seus vínculos.</p>
                </div>
                <!-- Explicação do Sistema -->
                <div class="management-section">
                    <div class="alert alert-info" style="background: rgba(244,179,29,0.1); border: 1px solid rgba(244,179,29,0.3); color: #f4b31d; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Como funciona:</strong> Para que um técnico apareça na criação de ordens, ele precisa estar vinculado à filial <strong>E</strong> apto ao tipo de equipamento selecionado. Gerencie esses vínculos separadamente nas seções abaixo.
                    </div>

                    <!-- Barra de Pesquisa -->
                    <div class="mb-3">
                        <input type="text" id="search-input" class="form-control" placeholder="Buscar por filial..." style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                    </div>

                    <!-- Área de Exibição de Cards -->
                    <div id="main-display-area" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                        <!-- Cards de filiais serão inseridos aqui via JavaScript -->
                    </div>
                </div>

                <!-- Seção 1: Vínculos Técnico-Filial -->
                <div class="management-section">
                    <h2 class="section-title">
                        <i class="fas fa-building me-2"></i>Vínculos Técnico-Filial
                    </h2>
                    
                    <!-- Filtros para Técnico-Filial -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label text-warning">Filtrar por Técnico</label>
                            <select id="filter-technician-branch" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todos os técnicos</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-warning">Filtrar por Filial</label>
                            <select id="filter-branch" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todas as filiais</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-warning">Status</label>
                            <select id="filter-status-branch" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todos</option>
                                <option value="true">Ativo</option>
                                <option value="false">Inativo</option>
                            </select>
                        </div>
                    </div>

                    <!-- Formulário para Adicionar Vínculo Técnico-Filial -->
                    <div class="card mb-4" style="background: rgba(255,255,255,0.05); border: 1px solid rgba(244,179,29,0.3);">
                        <div class="card-header" style="background: rgba(244,179,29,0.1); border-bottom: 1px solid rgba(244,179,29,0.3);">
                            <h5 class="mb-0 text-warning">
                                <i class="fas fa-plus me-2"></i>Adicionar Vínculo Técnico-Filial
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="form-add-technician-branch">
                                <div class="row">
                                    <div class="col-md-5">
                                        <label class="form-label text-warning">Técnico</label>
                                        <select id="add-technician-branch" class="form-select" required style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                            <option value="">Selecione um técnico</option>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label class="form-label text-warning">Filial</label>
                                        <select id="add-branch" class="form-select" required style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                            <option value="">Selecione uma filial</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-plus me-1"></i>Adicionar
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Tabela de Vínculos Técnico-Filial -->
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr style="background: rgba(244,179,29,0.1);">
                                    <th class="text-warning">Técnico</th>
                                    <th class="text-warning">Filial</th>
                                    <th class="text-warning">Status</th>
                                    <th class="text-warning">Ações</th>
                                </tr>
                            </thead>
                            <tbody id="technician-branch-links-table">
                                <!-- Conteúdo carregado via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Seção 2: Vínculos Técnico-Tipo de Equipamento -->
                <div class="management-section">
                    <h2 class="section-title">
                        <i class="fas fa-tools me-2"></i>Vínculos Técnico-Tipo de Equipamento
                    </h2>
                    
                    <!-- Filtros para Técnico-Tipo de Equipamento -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label text-warning">Filtrar por Técnico</label>
                            <select id="filter-technician-equipment" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todos os técnicos</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-warning">Filtrar por Tipo de Equipamento</label>
                            <select id="filter-equipment-type" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todos os tipos</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-warning">Status</label>
                            <select id="filter-status-equipment" class="form-select" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                <option value="">Todos</option>
                                <option value="true">Ativo</option>
                                <option value="false">Inativo</option>
                            </select>
                        </div>
                    </div>

                    <!-- Formulário para Adicionar Vínculo Técnico-Tipo de Equipamento -->
                    <div class="card mb-4" style="background: rgba(255,255,255,0.05); border: 1px solid rgba(244,179,29,0.3);">
                        <div class="card-header" style="background: rgba(244,179,29,0.1); border-bottom: 1px solid rgba(244,179,29,0.3);">
                            <h5 class="mb-0 text-warning">
                                <i class="fas fa-plus me-2"></i>Adicionar Vínculo Técnico-Tipo de Equipamento
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="form-add-technician-equipment">
                                <div class="row">
                                    <div class="col-md-5">
                                        <label class="form-label text-warning">Técnico</label>
                                        <select id="add-technician-equipment" class="form-select" required style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                            <option value="">Selecione um técnico</option>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label class="form-label text-warning">Tipo de Equipamento</label>
                                        <select id="add-equipment-type" class="form-select" required style="background: rgba(255,255,255,0.1); border: 1px solid rgba(244,179,29,0.5); color: #fff;">
                                            <option value="">Selecione um tipo</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-plus me-1"></i>Adicionar
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Tabela de Vínculos Técnico-Tipo de Equipamento -->
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr style="background: rgba(244,179,29,0.1);">
                                    <th class="text-warning">Técnico</th>
                                    <th class="text-warning">Tipo de Equipamento</th>
                                    <th class="text-warning">Status</th>
                                    <th class="text-warning">Ações</th>
                                </tr>
                            </thead>
                            <tbody id="technician-equipment-links-table">
                                <!-- Conteúdo carregado via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Seção de Convites -->
                <div class="management-section">
                    <h2 class="section-title">
                        <i class="fas fa-envelope me-2"></i>Convites
                    </h2>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-warning w-100" id="btnConvidarTecnico" type="button">
                                <i class="fas fa-envelope me-2"></i>Convidar Técnico
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-warning w-100" id="btnConvidarPrestadora" type="button">
                                <i class="fas fa-envelope me-2"></i>Convidar Prestadora
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modais de convite e sistema de cards/modais -->
    <!-- Modal: Convidar Técnico -->
    <div class="modal fade" id="modalConvidarTecnico" tabindex="-1" aria-labelledby="modalConvidarTecnicoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="modalConvidarTecnicoLabel">
                        <i class="fas fa-envelope me-2 text-warning"></i>Convidar Técnico
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="conviteAlertBox"></div>
                    <form id="formConvidarTecnico" autocomplete="off">
                        <div class="mb-3">
                            <label for="conviteEmail" class="form-label text-warning">E-mail do Técnico</label>
                            <input type="email" class="form-control" id="conviteEmail" name="email" required maxlength="120" placeholder="<EMAIL>">
                        </div>
                        <button type="submit" class="btn btn-warning w-100">Enviar Convite</button>
                    </form>
                    <div class="mt-3" id="conviteLinkBox" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal: Convidar Prestadora -->
    <div class="modal fade" id="modalConvidarPrestadora" tabindex="-1" aria-labelledby="modalConvidarPrestadoraLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="modalConvidarPrestadoraLabel">
                        <i class="fas fa-envelope me-2 text-warning"></i>Convidar Prestadora
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="convitePrestadoraAlertBox"></div>
                    <form id="formConvidarPrestadora" autocomplete="off">
                        <div class="mb-3">
                            <label for="convitePrestadoraEmail" class="form-label text-warning">E-mail da Prestadora</label>
                            <input type="email" class="form-control" id="convitePrestadoraEmail" name="email" required maxlength="120" placeholder="<EMAIL>">
                        </div>
                        <button type="submit" class="btn btn-warning w-100">Enviar Convite</button>
                    </form>
                    <div class="mt-3" id="convitePrestadoraLinkBox" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal de Confirmação -->
    <div id="confirm-modal" class="modal-overlay" style="z-index: 10000;">
        <div class="modal-content-custom max-w-sm text-center" style="max-width: 28rem;">
            <div class="card-dark">
                <div class="card-body">
                    <h3 id="confirm-title" class="text-lg font-bold text-white mb-2" style="font-size: 1.125rem; font-weight: 700; color: #fff; margin-bottom: 0.5rem;">Confirmar Ação</h3>
                    <p id="confirm-text" class="text-gray-300 mb-6" style="color: #d1d5db; margin-bottom: 1.5rem;">Você tem certeza que deseja realizar esta ação?</p>
                    <div class="flex justify-center gap-4" style="display: flex; justify-content: center; gap: 1rem;">
                        <button id="confirm-cancel-btn" class="btn btn-primary">Cancelar</button>
                        <button id="confirm-action-btn" class="btn btn-danger">Confirmar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes das Filiais -->
    <div id="details-modal" class="modal-overlay">
        <div class="modal-content-custom" style="max-width: 800px; width: 90%;">
            <div id="details-modal-content">
                <!-- Conteúdo será inserido via JavaScript -->
            </div>
        </div>
    </div>

    <div id="toast" class="toast"></div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/novo_link_management.js"></script>
</body>
</html>
{{ end }} 
