<!--
ATENÇÃO: <PERSON>ste é o template oficial da página de criação de ordens de manutenção.
Toda a lógica de frontend (JavaScript) relevante está embutida neste arquivo ou referenciada diretamente aqui.
Não utilize outros arquivos como create_order.tmpl ou order_creation_new.html, pois estão obsoletos.
-->
{{ define "ordens/create_order.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sistema de gerenciamento de manutenção para a Rede Tradição Shell">
    <meta name="theme-color" content="#FDB813">
    <title>{{ .title }}</title>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style-guide-final.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="/static/css/ordens_create_order.css">
    <style>
        /* Estilos adicionais para a página de criação de ordens */
        body {
            background-color: var(--shell-dark);
            color: #fff;
        }

        .main-content {
            background-color: var(--shell-dark);
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--shell-yellow);
        }

        .card-form {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            margin-bottom: 15px;
            border: none;
            overflow: hidden;
            background-color: #2d2d2d;
        }

        .card-header-shell {
            background: linear-gradient(90deg, var(--shell-yellow) 0%, var(--shell-red) 100%);
            color: white;
            font-weight: 600;
            padding: 10px 15px;
            border: none;
            font-size: 0.9rem;
        }

        .card-body-shell {
            padding: 15px;
            background-color: #2d2d2d;
        }

        .form-control, .form-select {
            background-color: #1e1e1e;
            border: 1px solid #444;
            color: #fff;
            font-size: 0.9rem;
            padding: 0.375rem 0.75rem;
        }

        .form-control:focus, .form-select:focus {
            background-color: #1e1e1e;
            border-color: var(--shell-yellow);
            color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(253, 184, 19, 0.25);
        }

        .form-floating > label {
            color: #aaa;
            padding: 0.375rem 0.75rem;
        }

        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: var(--shell-yellow);
            padding-left: 0.75rem;
            padding-top: 0.25rem;
            height: auto;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }

        .form-floating > .form-control {
            height: calc(2.5rem + 2px);
            padding: 0.5rem 0.75rem;
        }

        .form-label {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
            color: #ddd;
        }

        .required-field::after {
            content: " *";
            color: var(--shell-red);
            font-weight: bold;
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }

        .progress-indicator::before {
            content: "";
            position: absolute;
            top: 12px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #444;
            z-index: 0;
        }

        .progress-step {
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #444;
            color: #aaa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            font-size: 0.7rem;
        }

        .step-text {
            font-size: 0.7rem;
            color: #aaa;
            text-align: center;
        }

        .progress-step.active .step-icon {
            background-color: var(--shell-yellow);
            color: #000;
        }

        .progress-step.active .step-text {
            color: var(--shell-yellow);
            font-weight: 600;
        }

        .progress-step.completed .step-icon {
            background-color: #28a745;
            color: white;
        }

        .info-card {
            background-color: rgba(253, 184, 19, 0.15);
            border-left: 3px solid var(--shell-yellow);
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.85rem;
        }

        .photo-upload-area {
            background-color: rgba(253, 184, 19, 0.05);
            border: 1px dashed var(--shell-yellow);
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .photo-upload-area:hover {
            background-color: rgba(253, 184, 19, 0.1);
        }

        .photo-preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
            gap: 8px;
            margin-top: 12px;
        }

        .photo-preview-item {
            position: relative;
            border-radius: 4px;
            overflow: hidden;
            aspect-ratio: 1;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }

        .photo-preview-item:hover {
            transform: scale(1.05);
        }

        .photo-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-preview-remove {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: rgba(0,0,0,0.6);
            color: #fff;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .photo-preview-remove:hover {
            background-color: #dc3545;
        }

        .btn-shell {
            background: linear-gradient(90deg, var(--shell-yellow) 0%, var(--shell-red) 100%);
            border: none;
            color: #000;
            font-weight: 600;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .btn-shell:hover {
            background: linear-gradient(90deg, var(--shell-red) 0%, var(--shell-yellow) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-outline-shell {
            background: transparent;
            border: 1px solid var(--shell-yellow);
            color: var(--shell-yellow);
            font-weight: 600;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .btn-outline-shell:hover {
            background-color: var(--shell-yellow);
            color: #000;
            transform: translateY(-2px);
        }

        .alert {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            border-radius: 4px;
        }

        .input-group-text {
            background-color: #444;
            border-color: #444;
            color: #aaa;
        }

        .form-text {
            font-size: 0.75rem;
            color: #aaa;
        }

        .form-check-label {
            font-size: 0.85rem;
        }

        /* Ajustes para os cards de status */
        .status-cards {
            margin-top: 10px;
        }

        .status-card {
            border: 1px solid #444;
            border-radius: 6px;
            padding: 10px;
            transition: all 0.3s ease;
            height: 100%;
            background-color: #1e1e1e;
            cursor: pointer;
        }

        .status-card:hover {
            border-color: var(--shell-yellow);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .status-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 0.9rem;
        }

        .status-icon.warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #f1c40f;
        }

        .status-icon.danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #e74c3c;
        }

        .status-title {
            display: block;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .status-desc {
            display: block;
            font-size: 0.75rem;
            color: #ddd;
        }

        .form-check-inline {
            margin-right: 0;
        }

        .form-check-input {
            position: absolute;
            opacity: 0;
        }
    </style>
</head>
<body>
    <!-- Menu lateral -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <img src="/static/images/logo-tradicio-shell.png" alt="Logo Rede Tradição Shell" class="logo">
            <button id="sidebarClose" class="btn-close btn-close-white d-lg-none"></button>
        </div>
        <ul class="sidebar-menu">
            <li class="menu-item {{ if eq .ActivePage "dashboard" }}active{{ end }}">
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            </li>
            <li class="menu-item {{ if eq .ActivePage "calendar" }}active{{ end }}">
                <a href="/calendario"><i class="fas fa-calendar-alt"></i> Calendário</a>
            </li>
            <li class="menu-item {{ if eq .ActivePage "orders" }}active{{ end }}">
                <a href="/orders"><i class="fas fa-clipboard-list"></i> Ordens de Serviço</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-gas-pump"></i> Postos</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-tools"></i> Manutenções</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-truck"></i> Fornecedores</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-chart-line"></i> Relatórios</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-users"></i> Usuários</a>
            </li>
            <li class="menu-item">
                <a href="#"><i class="fas fa-cog"></i> Configurações</a>
            </li>
            <li class="menu-item">
                <a href="#" id="btnLogout"><i class="fas fa-sign-out-alt"></i> Sair</a>
            </li>
        </ul>
        <div class="sidebar-footer">
            <small>&copy; 2025 Rede Tradição Shell</small>
        </div>
    </nav>

    <!-- Conteúdo principal -->
    <div class="main-content">
        <header class="top-nav">
            <button id="sidebarToggle" class="menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="top-nav-right">
                <div class="user-info">
                    <span class="user-name">{{ .User.Name }}</span>
                    <span class="user-role">{{ .User.Role }}</span>
                </div>
            </div>
        </header>

        <div class="container-fluid py-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="page-title animate__animated animate__fadeIn">
                    <i class="fas fa-clipboard-list me-2 text-warning"></i> Nova Ordem de Serviço
                </h2>
                <div>
                    <button type="button" id="btnBack" class="btn btn-outline-secondary btn-sm me-2">
                        <i class="fas fa-arrow-left me-1"></i> Voltar
                    </button>
                    <button class="btn btn-outline-shell btn-sm" id="refreshButton">
                        <i class="fas fa-sync-alt me-1"></i> Atualizar
                    </button>
                </div>
            </div>

            <!-- Indicador de progresso -->
            <div class="progress-indicator animate__animated animate__fadeIn">
                <div class="progress-step active">
                    <div class="step-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="step-text">Informações</div>
                </div>
                <div class="progress-step">
                    <div class="step-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="step-text">Equipamento</div>
                </div>
                <div class="progress-step">
                    <div class="step-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="step-text">Detalhes</div>
                </div>
                <div class="progress-step">
                    <div class="step-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="step-text">Fotos</div>
                </div>
                <div class="progress-step">
                    <div class="step-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-text">Finalizar</div>
                </div>
            </div>

            <form id="createOrderForm" class="needs-validation" novalidate>
                <!-- Card 1: Informações Básicas -->
                <div class="card card-form animate__animated animate__fadeIn">
                    <div class="card-header card-header-shell">
                        <i class="fas fa-info-circle me-2"></i> Informações Básicas
                    </div>
                    <div class="card-body card-body-shell">
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <div class="info-card">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-building fa-2x text-warning"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 text-light">Filial</h6>
                                            <p class="mb-0 fw-bold text-white">{{ if .BranchName }}{{ .BranchName }}{{ else }}{{ .User.Name }}{{ end }}</p>
                                            <input type="hidden" id="branch_id_input" name="branch_id" value="{{ if .BranchID }}{{ .BranchID }}{{ else }}{{ .User.ID }}{{ end }}">
                                            <small class="text-muted" id="branch_id_display"></small>
                                            <!-- Comentário para debug: BranchID={{ .BranchID }}, User.ID={{ .User.ID }} -->
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-warning" id="showBranchId">
                                            <i class="fas fa-info-circle me-1"></i> Verificar ID da Filial
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-user fa-2x text-warning"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 text-light">Solicitante</h6>
                                            <p class="mb-0 fw-bold text-white">{{ .User.Name }}</p>
                                            <input type="hidden" name="requester_id" value="{{ .User.ID }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="orderNumber" class="form-label">Número da Ordem</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                    <input type="text" class="form-control" id="orderNumber" name="order_number" placeholder="Número da Ordem" readonly value="OS-AUTOMÁTICO">
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="creationDate" class="form-label">Data de Criação</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    <input type="text" class="form-control" id="creationDate" name="creation_date" placeholder="Data de Criação" readonly value="Gerada automaticamente">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card 2: Equipamento e Tipo de Manutenção -->
                <div class="card card-form animate__animated animate__fadeIn">
                    <div class="card-header card-header-shell">
                        <i class="fas fa-tools me-2"></i> Equipamento e Tipo de Manutenção
                    </div>
                    <div class="card-body card-body-shell">
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="equipmentId" class="form-label required-field">Equipamento</label>
                                <select id="equipmentId" name="equipment_id" class="form-select" required>
                                    <option value="">Carregando equipamentos...</option>
                                </select>
                                <div class="invalid-feedback">
                                    Por favor, selecione um equipamento.
                                </div>
                                <div class="form-text" id="equipmentHelp">
                                    <i class="fas fa-info-circle me-1"></i> Apenas equipamentos da sua filial são exibidos
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="maintenanceType" class="form-label required-field">Tipo de Manutenção</label>
                                <select id="maintenanceType" name="type" class="form-select" required>
                                    <option value="">Selecione o tipo</option>
                                    <option value="corretiva">Corretiva</option>
                                    <option value="preventiva">Preventiva</option>
                                    <option value="emergencial">Emergencial</option>
                                    <option value="calibracao">Calibração</option>
                                    <option value="inspecao">Inspeção</option>
                                </select>
                                <div class="invalid-feedback">
                                    Por favor, selecione o tipo de manutenção.
                                </div>
                            </div>
                        </div>
                        <!-- Container de prioridade removido conforme solicitado -->
                        <input type="hidden" name="priority" value="media">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="serviceProviderId" class="form-label">Prestador de Serviço</label>
                                <div class="input-group">
                                    <select id="serviceProviderId" name="service_provider_id" class="form-select">
                                        <option value="">Carregando prestadores...</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-warning" id="refreshProviders" title="Atualizar lista de prestadores">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i> Apenas prestadores vinculados à sua filial são exibidos
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="technicianId" class="form-label">Técnico Específico</label>
                                <div class="input-group">
                                    <select id="technicianId" name="technician_id" class="form-select" disabled>
                                        <option value="">Selecione um prestador ou equipamento primeiro</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-warning" id="refreshTechnicians" title="Atualizar lista de técnicos">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i> Selecione um prestador e equipamento para ver os técnicos disponíveis
                                </div>
                            </div>
                        </div>
                        <!-- Dica sobre prioridade removida -->
                    </div>
                </div>

                <!-- Estilos dos botões de prioridade foram movidos para o CSS principal -->

                <!-- Card 3: Detalhes da Ordem -->
                <div class="card card-form animate__animated animate__fadeIn">
                    <div class="card-header card-header-shell">
                        <i class="fas fa-clipboard-list me-2"></i> Detalhes da Ordem
                    </div>
                    <div class="card-body card-body-shell">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label for="problemDescription" class="form-label required-field">Problema</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-heading"></i></span>
                                    <input type="text" class="form-control" id="problemDescription" name="problem" placeholder="Ex: Manutenção no Compressor da Pista 2" required>
                                </div>
                                <div class="invalid-feedback">
                                    Por favor, informe o problema.
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label for="notes" class="form-label">Observações</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Forneça informações adicionais sobre o problema. Quanto mais informações, melhor será o diagnóstico."></textarea>
                                <div class="form-text mt-1">
                                    <i class="fas fa-info-circle me-1"></i> <small>Descreva quando começou, sintomas e comportamentos observados.</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label class="form-label">Status do Equipamento</label>
                                <div class="status-cards">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="status-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="partialFunctioning" name="partial_functioning">
                                                    <label class="form-check-label" for="partialFunctioning">
                                                        <div class="d-flex align-items-center">
                                                            <div class="status-icon warning">
                                                                <i class="fas fa-exclamation-circle"></i>
                                                            </div>
                                                            <div>
                                                                <span class="status-title text-warning">Funcionamento Parcial</span>
                                                                <span class="status-desc text-light">O equipamento está funcionando, mas com limitações</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="status-card">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="impactsOperation" name="impacts_operation">
                                                    <label class="form-check-label" for="impactsOperation">
                                                        <div class="d-flex align-items-center">
                                                            <div class="status-icon danger">
                                                                <i class="fas fa-exclamation-triangle"></i>
                                                            </div>
                                                            <div>
                                                                <span class="status-title text-danger">Impacta Operação</span>
                                                                <span class="status-desc text-light">O problema afeta a operação normal do posto</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estilos dos status cards foram movidos para o CSS principal -->

                <!-- Card 4: Fotos do Equipamento -->
                <div class="card card-form animate__animated animate__fadeIn">
                    <div class="card-header card-header-shell">
                        <i class="fas fa-camera me-2"></i> Fotos do Equipamento
                    </div>
                    <div class="card-body card-body-shell">
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-warning py-2 px-3">
                                    <i class="fas fa-info-circle me-1"></i> <small><strong>Importante:</strong> Anexe pelo menos 3 fotos do equipamento para ajudar na identificação do problema.</small>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="photo-upload-area" id="photoDropzone">
                                    <div class="upload-icon mb-2">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-warning"></i>
                                    </div>
                                    <h6>Arraste e solte fotos aqui</h6>
                                    <p class="text-muted small mb-2">ou</p>
                                    <button type="button" class="btn btn-shell btn-sm" id="btnSelectPhotos">
                                        <i class="fas fa-image me-1"></i> Selecionar Fotos
                                    </button>
                                    <input type="file" id="photoInput" multiple accept="image/*" style="display: none;">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="photo-preview-grid" id="photoPreviewContainer">
                                    <!-- As miniaturas das fotos carregadas aparecerão aqui -->
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="upload-progress d-none">
                                    <label class="form-label">Progresso do Upload</label>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card 5: Botões de Ação -->
                <div class="card card-form animate__animated animate__fadeIn">
                    <div class="card-body card-body-shell">
                        <div class="d-flex flex-wrap justify-content-end gap-2">
                            <button type="button" id="btnSaveDraft" class="btn btn-outline-shell">
                                <i class="fas fa-save me-1"></i> Salvar Rascunho
                            </button>
                            <button type="button" id="btnSave" class="btn btn-shell">
                                <i class="fas fa-paper-plane me-1"></i> Enviar Ordem
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Toast Container -->
                <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>
        </form>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/pt.js"></script>
    <script src="/static/js/unified_orders.js"></script>
    <script>
        // Unified Order Manager Instance
        let orderManager = null;
        let currentStep = 1;
        const totalSteps = 5;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('[INIT] Página carregada, inicializando sistema unificado...');

            // Initialize Unified Order Manager
            if (typeof UnifiedOrderManager !== 'undefined') {
                orderManager = new UnifiedOrderManager({
                    apiBaseUrl: '/api/orders',
                    enableCache: true,
                    cacheTTL: 300000, // 5 minutes
                    enableNotifications: true
                });
                console.log('[INIT] UnifiedOrderManager inicializado');
            } else {
                console.warn('[INIT] UnifiedOrderManager não encontrado, usando fallback');
                orderManager = createFallbackOrderManager();
            }

            // Initialize branch ID
            initializeBranchId();

            // Initialize UI components
            initializeSidebar();
            initializeSelect2();
            initializeProgressIndicator();
            initializeFormValidation();
            initializePhotoUpload();
            initializeFormHandlers();

            // Start the order creation flow
            startOrderCreationFlow();
        });

        // Initialize branch ID from multiple sources
        function initializeBranchId() {
            const branchIdInput = document.getElementById('branch_id_input');
            if (branchIdInput && branchIdInput.value) {
                const branchId = branchIdInput.value;
                console.log(`[INIT] ID da filial obtido do input hidden: ${branchId}`);
                localStorage.setItem('branch_id', branchId);
            } else {
                console.log('[INIT] ID da filial não encontrado no input hidden');
                const branchFromCtx = '{{ .BranchID }}';
                if (branchFromCtx) {
                    console.log(`[INIT] ID da filial obtido do contexto: ${branchFromCtx}`);
                    localStorage.setItem('branch_id', branchFromCtx);
                } else {
                    console.warn('[INIT] ID da filial não encontrado no contexto');
                }
            }
        }

        // Initialize sidebar functionality
        function initializeSidebar() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    mainContent.classList.toggle('sidebar-open');
                });
            }
            if (sidebarClose) {
                sidebarClose.addEventListener('click', function() {
                    sidebar.classList.remove('open');
                    mainContent.classList.remove('sidebar-open');
                });
            }
        }

        // Initialize Select2 components
        function initializeSelect2() {
            if ($.fn.select2) {
                $('.form-select').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dropdownParent: $('body')
                });
                $('#technicianId').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dropdownParent: $('body'),
                    placeholder: 'Selecione um técnico (opcional)'
                });
                console.log('[SELECT2] Inicialização do Select2 concluída');
            }
        }

        // Initialize progress indicator
        function initializeProgressIndicator() {
            updateProgressIndicator(1);
        }

        // Update progress indicator
        function updateProgressIndicator(step) {
            currentStep = step;
            const steps = document.querySelectorAll('.progress-step');
            
            steps.forEach((stepEl, index) => {
                const stepNumber = index + 1;
                const icon = stepEl.querySelector('.step-icon');
                const text = stepEl.querySelector('.step-text');
                
                stepEl.classList.remove('active', 'completed');
                
                if (stepNumber < step) {
                    stepEl.classList.add('completed');
                    icon.innerHTML = '<i class="fas fa-check"></i>';
                } else if (stepNumber === step) {
                    stepEl.classList.add('active');
                } else {
                    // Reset future steps
                    const originalIcons = [
                        '<i class="fas fa-info-circle"></i>',
                        '<i class="fas fa-tools"></i>',
                        '<i class="fas fa-clipboard-list"></i>',
                        '<i class="fas fa-camera"></i>',
                        '<i class="fas fa-check"></i>'
                    ];
                    icon.innerHTML = originalIcons[index] || '<i class="fas fa-circle"></i>';
                }
            });
        }

        // Start the order creation flow
        function startOrderCreationFlow() {
            console.log('[FLOW] Iniciando fluxo de criação de ordem unificado');
            updateProgressIndicator(1);
            
            // Step 1: Branch selection (already handled by template)
            const branchId = getBranchId();
            if (branchId) {
                console.log(`[FLOW] Passo 1 - Filial selecionada: ${branchId}`);
                updateProgressIndicator(2);
                loadEquipments();
            } else {
                showToast('Erro: Filial não identificada. Recarregue a página.', 'danger');
            }
        }

        // Load equipments using unified order manager
        async function loadEquipments() {
            console.log('[FLOW] Passo 2 - Carregando equipamentos...');
            const equipmentSelect = document.getElementById('equipmentId');
            if (!equipmentSelect) return;

            try {
                setLoadingState(equipmentSelect, 'Carregando equipamentos...');
                
                const branchId = getBranchId();
                const equipments = await orderManager.getAvailableEquipments(branchId);
                
                populateEquipmentSelect(equipments);
                
                // Add equipment change handler
                equipmentSelect.addEventListener('change', onEquipmentChange);
                
                console.log(`[FLOW] Equipamentos carregados: ${equipments.length}`);
                
            } catch (error) {
                console.error('[FLOW] Erro ao carregar equipamentos:', error);
                setErrorState(equipmentSelect, 'Erro ao carregar equipamentos');
                showToast(`Erro ao carregar equipamentos: ${error.message}`, 'danger');
            }
        }

        // Handle equipment selection
        async function onEquipmentChange(event) {
            const equipmentId = event.target.value;
            const selectedOption = event.target.options[event.target.selectedIndex];
            const equipmentTypeId = selectedOption.dataset.equipmentTypeId;
            
            console.log(`[FLOW] Passo 3 - Equipamento selecionado: ${equipmentId}, Tipo: ${equipmentTypeId}`);
            
            if (equipmentId && equipmentTypeId) {
                localStorage.setItem('equipment_type_id', equipmentTypeId);
                updateProgressIndicator(3);
                
                // Load providers and technicians in parallel
                await Promise.all([
                    loadServiceProviders(),
                    loadTechnicians()
                ]);
                
                updateProgressIndicator(4);
            } else {
                // Reset subsequent steps
                resetProvidersAndTechnicians();
            }
        }

        // Load service providers using unified endpoints
        async function loadServiceProviders() {
            console.log('[FLOW] Carregando prestadores...');
            const providerSelect = document.getElementById('serviceProviderId');
            if (!providerSelect) return;

            try {
                setLoadingState(providerSelect, 'Carregando prestadores...');
                
                const branchId = getBranchId();
                const providers = await orderManager.getAvailableProviders(branchId);
                
                populateProviderSelect(providers);
                
                // Add provider change handler
                providerSelect.addEventListener('change', onProviderChange);
                
                console.log(`[FLOW] Prestadores carregados: ${providers.length}`);
                
            } catch (error) {
                console.error('[FLOW] Erro ao carregar prestadores:', error);
                setErrorState(providerSelect, 'Erro ao carregar prestadores');
                showToast(`Erro ao carregar prestadores: ${error.message}`, 'warning');
            }
        }

        // Load technicians with equipment type validation
        async function loadTechnicians() {
            console.log('[FLOW] Carregando técnicos qualificados...');
            const technicianSelect = document.getElementById('technicianId');
            if (!technicianSelect) return;

            try {
                setLoadingState(technicianSelect, 'Carregando técnicos...');
                
                const branchId = getBranchId();
                const equipmentTypeId = localStorage.getItem('equipment_type_id');
                
                if (!equipmentTypeId) {
                    setErrorState(technicianSelect, 'Selecione um equipamento primeiro');
                    return;
                }
                
                const technicians = await orderManager.getAvailableTechnicians(branchId, equipmentTypeId);
                
                populateTechnicianSelect(technicians);
                
                // Add technician change handler
                technicianSelect.addEventListener('change', onTechnicianChange);
                
                console.log(`[FLOW] Técnicos qualificados carregados: ${technicians.length}`);
                
            } catch (error) {
                console.error('[FLOW] Erro ao carregar técnicos:', error);
                setErrorState(technicianSelect, 'Erro ao carregar técnicos');
                showToast(`Erro ao carregar técnicos: ${error.message}`, 'warning');
            }
        }

        // Handle provider selection (mutually exclusive with technician)
        function onProviderChange(event) {
            const providerId = event.target.value;
            const technicianSelect = document.getElementById('technicianId');
            
            if (providerId) {
                console.log(`[FLOW] Prestador selecionado: ${providerId} - Desabilitando seleção de técnico`);
                technicianSelect.disabled = true;
                technicianSelect.value = '';
                if ($.fn.select2) $(technicianSelect).trigger('change');
                
                showToast('Prestador selecionado. A seleção de técnico foi desabilitada.', 'info');
            } else {
                console.log(`[FLOW] Prestador desmarcado - Reabilitando seleção de técnico`);
                technicianSelect.disabled = false;
            }
            
            validateAssignment();
        }

        // Handle technician selection (mutually exclusive with provider)
        function onTechnicianChange(event) {
            const technicianId = event.target.value;
            const providerSelect = document.getElementById('serviceProviderId');
            
            if (technicianId) {
                console.log(`[FLOW] Técnico selecionado: ${technicianId} - Desabilitando seleção de prestador`);
                providerSelect.disabled = true;
                providerSelect.value = '';
                if ($.fn.select2) $(providerSelect).trigger('change');
                
                showToast('Técnico selecionado. A seleção de prestador foi desabilitada.', 'info');
            } else {
                console.log(`[FLOW] Técnico desmarcado - Reabilitando seleção de prestador`);
                providerSelect.disabled = false;
            }
            
            validateAssignment();
        }

        // Validate assignment selection
        function validateAssignment() {
            const providerId = document.getElementById('serviceProviderId').value;
            const technicianId = document.getElementById('technicianId').value;
            
            if (providerId || technicianId) {
                updateProgressIndicator(5);
                console.log(`[FLOW] Passo 4 - Atribuição validada: ${providerId ? 'Prestador' : 'Técnico'}`);
            } else {
                updateProgressIndicator(4);
            }
        }

        // Populate equipment select
        function populateEquipmentSelect(equipments) {
            const select = document.getElementById('equipmentId');
            select.innerHTML = '';
            
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione o equipamento';
            select.appendChild(defaultOption);
            
            if (equipments && equipments.length > 0) {
                equipments.forEach(eq => {
                    const option = document.createElement('option');
                    option.value = eq.id;
                    if (eq.equipment_type_id) {
                        option.dataset.equipmentTypeId = eq.equipment_type_id;
                    }
                    
                    let text = eq.name || `Equipamento #${eq.id}`;
                    if (eq.type) text += ` (${eq.type})`;
                    if (eq.serial_number) text += ` - S/N: ${eq.serial_number}`;
                    option.textContent = text;
                    
                    select.appendChild(option);
                });
                
                updateHelpText('equipmentHelp', `${equipments.length} equipamentos disponíveis na sua filial`);
            } else {
                const noOption = document.createElement('option');
                noOption.value = '';
                noOption.textContent = 'Nenhum equipamento encontrado';
                noOption.disabled = true;
                select.appendChild(noOption);
                
                updateHelpText('equipmentHelp', 'Nenhum equipamento encontrado para sua filial', 'warning');
            }
            
            select.disabled = false;
            if ($.fn.select2) $(select).trigger('change');
        }

        // Populate provider select
        function populateProviderSelect(providers) {
            const select = document.getElementById('serviceProviderId');
            select.innerHTML = '';
            
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione um prestador (opcional)';
            select.appendChild(defaultOption);
            
            if (providers && providers.length > 0) {
                providers.forEach(provider => {
                    const option = document.createElement('option');
                    option.value = provider.id;
                    option.textContent = provider.name || `Prestador #${provider.id}`;
                    select.appendChild(option);
                });
                
                updateHelpText('serviceProviderId', `${providers.length} prestadores disponíveis para sua filial`);
            } else {
                const noOption = document.createElement('option');
                noOption.value = '';
                noOption.textContent = 'Nenhum prestador encontrado';
                noOption.disabled = true;
                select.appendChild(noOption);
                
                updateHelpText('serviceProviderId', 'Nenhum prestador vinculado à sua filial', 'warning');
            }
            
            select.disabled = false;
            if ($.fn.select2) {
                $(select).select2('destroy');
                $(select).select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dropdownParent: $('body'),
                    placeholder: 'Selecione um prestador (opcional)'
                });
                $(select).trigger('change');
            }
        }

        // Populate technician select
        function populateTechnicianSelect(technicians) {
            const select = document.getElementById('technicianId');
            select.innerHTML = '';
            
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'Selecione um técnico (opcional)';
            select.appendChild(defaultOption);
            
            if (technicians && technicians.length > 0) {
                technicians.forEach(technician => {
                    const option = document.createElement('option');
                    option.value = technician.id;
                    option.textContent = technician.name || `Técnico #${technician.id}`;
                    select.appendChild(option);
                });
                
                updateHelpText('technicianId', `${technicians.length} técnicos qualificados (vinculados à filial e aptos ao tipo de equipamento)`);
            } else {
                const noOption = document.createElement('option');
                noOption.value = '';
                noOption.textContent = 'Nenhum técnico qualificado encontrado';
                noOption.disabled = true;
                select.appendChild(noOption);
                
                updateHelpText('technicianId', 'Nenhum técnico vinculado à filial e apto ao tipo de equipamento', 'warning');
            }
            
            select.disabled = false;
            if ($.fn.select2) {
                $(select).select2('destroy');
                $(select).select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    dropdownParent: $('body'),
                    placeholder: 'Selecione um técnico (opcional)'
                });
                $(select).trigger('change');
            }
        }

        // Utility functions
        function setLoadingState(select, message) {
            select.innerHTML = `<option value="">${message}</option>`;
            select.disabled = true;
        }

        function setErrorState(select, message) {
            select.innerHTML = `<option value="">${message}</option>`;
            select.disabled = true;
        }

        function updateHelpText(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const helpElement = element?.nextElementSibling?.classList.contains('form-text') 
                ? element.nextElementSibling 
                : element?.parentElement?.nextElementSibling?.classList.contains('form-text')
                ? element.parentElement.nextElementSibling
                : null;
                
            if (helpElement) {
                const iconClass = type === 'warning' ? 'fas fa-exclamation-circle text-warning' : 'fas fa-info-circle';
                helpElement.innerHTML = `<i class="${iconClass} me-1"></i> ${message}`;
            }
        }

        function resetProvidersAndTechnicians() {
            const providerSelect = document.getElementById('serviceProviderId');
            const technicianSelect = document.getElementById('technicianId');
            
            setLoadingState(providerSelect, 'Selecione um equipamento primeiro');
            setLoadingState(technicianSelect, 'Selecione um equipamento primeiro');
            
            updateProgressIndicator(2);
        }

        // Get branch ID robustly
        function getBranchId() {
            const sources = [
                {name:'input', get:()=>document.getElementById('branch_id_input')?.value},
                {name:'ctx', get:()=> '{{ .BranchID }}'},
                {name:'local', get:()=>localStorage.getItem('branch_id')},
                {name:'url', get:()=> new URLSearchParams(window.location.search).get('branch_id')}
            ];
            let bid=null, srcName=null;
            for (const s of sources) {
                const v = s.get();
                if (v && v!=='undefined' && v!=='') { bid=v; srcName=s.name; break; }
            }
            if (!bid) { bid='54'; srcName='default'; console.warn(`[BRANCH_ID] Usando padrão ${bid}`); }
            console.log(`[BRANCH_ID] Obtido de ${srcName}: ${bid}`);
            localStorage.setItem('branch_id', bid);
            document.cookie=`branch_id=${bid};path=/;max-age=86400`;
            return bid;
        }

        // Create fallback order manager if unified_orders.js is not available
        function createFallbackOrderManager() {
            return {
                async getAvailableEquipments(branchId) {
                    const url = `/api/equipments${branchId ? `?branch_id=${branchId}` : ''}`;
                    const response = await fetch(url);
                    if (!response.ok) throw new Error(`Erro ${response.status}: ${response.statusText}`);
                    return response.json();
                },
                
                async getAvailableProviders(branchId) {
                    const urls = [
                        `/api/orders/available-providers?branch_id=${branchId}`,
                        `/api/providers?branch_id=${branchId}`,
                        `/api/providers/by-branch?branch_id=${branchId}`,
                        `/api/links/branch/${branchId}/providers`
                    ];
                    
                    for (const url of urls) {
                        try {
                            const response = await fetch(url);
                            if (response.ok) {
                                const data = await response.json();
                                return Array.isArray(data) ? data : data.data || data.providers || data.links || [];
                            }
                        } catch (error) {
                            console.warn(`[FALLBACK] URL ${url} falhou:`, error);
                        }
                    }
                    throw new Error('Nenhum endpoint de prestadores disponível');
                },
                
                async getAvailableTechnicians(branchId, equipmentTypeId) {
                    const url = `/api/orders/available-technicians?branch_id=${branchId}&equipment_type_id=${equipmentTypeId}`;
                    const response = await fetch(url);
                    if (!response.ok) throw new Error(`Erro ${response.status}: ${response.statusText}`);
                    const data = await response.json();
                    return Array.isArray(data) ? data : data.data || [];
                },
                
                async createOrder(orderData) {
                    const response = await fetch('/api/orders', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(orderData)
                    });
                    if (!response.ok) throw new Error(`Erro ${response.status}: ${response.statusText}`);
                    return response.json();
                }
            };
        }

        // Initialize form validation
        function initializeFormValidation() {
            const form = document.getElementById('createOrderForm');
            if (form) {
                form.addEventListener('submit', handleFormSubmit);
                
                // Add real-time validation
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    field.addEventListener('blur', validateField);
                    field.addEventListener('change', validateField);
                });
            }
        }

        // Validate individual field
        function validateField(event) {
            const field = event.target;
            const isValid = field.checkValidity();
            
            field.classList.toggle('is-valid', isValid);
            field.classList.toggle('is-invalid', !isValid);
            
            return isValid;
        }

        // Initialize photo upload functionality
        function initializePhotoUpload() {
            const photoInput = document.getElementById('photoInput');
            const photoDropzone = document.getElementById('photoDropzone');
            const btnSelectPhotos = document.getElementById('btnSelectPhotos');
            const photoPreviewContainer = document.getElementById('photoPreviewContainer');
            
            if (btnSelectPhotos) {
                btnSelectPhotos.addEventListener('click', () => photoInput?.click());
            }
            
            if (photoInput) {
                photoInput.addEventListener('change', handlePhotoSelection);
            }
            
            if (photoDropzone) {
                photoDropzone.addEventListener('dragover', handleDragOver);
                photoDropzone.addEventListener('drop', handlePhotoDrop);
            }
        }

        // Handle photo selection
        function handlePhotoSelection(event) {
            const files = Array.from(event.target.files);
            processPhotoFiles(files);
        }

        // Handle drag and drop
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.backgroundColor = 'rgba(253, 184, 19, 0.2)';
        }

        function handlePhotoDrop(event) {
            event.preventDefault();
            event.currentTarget.style.backgroundColor = '';
            
            const files = Array.from(event.dataTransfer.files).filter(file => file.type.startsWith('image/'));
            processPhotoFiles(files);
        }

        // Process photo files
        function processPhotoFiles(files) {
            const container = document.getElementById('photoPreviewContainer');
            if (!container) return;
            
            files.forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = createPhotoPreview(e.target.result, file.name);
                    container.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            });
        }

        // Create photo preview element
        function createPhotoPreview(src, filename) {
            const item = document.createElement('div');
            item.className = 'photo-preview-item';
            
            item.innerHTML = `
                <img src="${src}" alt="${filename}" title="${filename}">
                <button type="button" class="photo-preview-remove" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            return item;
        }

        // Initialize form handlers
        function initializeFormHandlers() {
            const btnSave = document.getElementById('btnSave');
            const btnSaveDraft = document.getElementById('btnSaveDraft');
            const btnBack = document.getElementById('btnBack');
            const refreshButton = document.getElementById('refreshButton');
            
            if (btnSave) {
                btnSave.addEventListener('click', () => handleFormSubmit(false));
            }
            
            if (btnSaveDraft) {
                btnSaveDraft.addEventListener('click', () => handleFormSubmit(true));
            }
            
            if (btnBack) {
                btnBack.addEventListener('click', () => window.history.back());
            }
            
            if (refreshButton) {
                refreshButton.addEventListener('click', () => window.location.reload());
            }
        }

        // Handle form submission
        async function handleFormSubmit(isDraft = false) {
            console.log(`[FLOW] Passo 7 - ${isDraft ? 'Salvando rascunho' : 'Enviando ordem'}...`);
            
            const form = document.getElementById('createOrderForm');
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                showToast('Por favor, preencha todos os campos obrigatórios.', 'warning');
                return;
            }
            
            try {
                const orderData = collectFormData(isDraft);
                
                // Show loading state
                const submitBtn = isDraft ? document.getElementById('btnSaveDraft') : document.getElementById('btnSave');
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processando...';
                
                const result = await orderManager.createOrder(orderData);
                
                // Success feedback
                showToast(
                    isDraft 
                        ? 'Rascunho salvo com sucesso!' 
                        : 'Ordem criada com sucesso! Prestador/técnico foi notificado automaticamente.',
                    'success'
                );
                
                if (!isDraft) {
                    // Show notification confirmation
                    setTimeout(() => {
                        showToast('Notificação enviada para o responsável pela ordem.', 'info');
                    }, 2000);
                    
                    // Redirect to order details or list
                    setTimeout(() => {
                        window.location.href = `/orders/${result.id || result.data?.id}`;
                    }, 3000);
                }
                
                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                
            } catch (error) {
                console.error('[FLOW] Erro ao processar ordem:', error);
                showToast(`Erro ao ${isDraft ? 'salvar rascunho' : 'criar ordem'}: ${error.message}`, 'danger');
                
                // Reset button
                const submitBtn = isDraft ? document.getElementById('btnSaveDraft') : document.getElementById('btnSave');
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.dataset.originalText || (isDraft ? '<i class="fas fa-save me-1"></i> Salvar Rascunho' : '<i class="fas fa-paper-plane me-1"></i> Enviar Ordem');
            }
        }

        // Collect form data
        function collectFormData(isDraft) {
            const formData = new FormData(document.getElementById('createOrderForm'));
            const data = {};
            
            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Add additional fields
            data.status = isDraft ? 'draft' : 'pending';
            data.created_at = new Date().toISOString();
            
            // Validate mutually exclusive assignment
            if (data.service_provider_id && data.technician_id) {
                throw new Error('Selecione apenas um prestador OU um técnico, não ambos.');
            }
            
            if (!data.service_provider_id && !data.technician_id) {
                throw new Error('Selecione um prestador ou um técnico para a ordem.');
            }
            
            return data;
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) return;
            
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${type === 'danger' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: type === 'success' ? 5000 : 3000
            });
            
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }
    </script>
</body>
</html>
{{ end }}
