{{ define "tecnico/ordem_detail_flip.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Ordem - Técnico - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/tecnico_unified.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        /* Flip Card Styles */
        .flip-container {
            perspective: 1000px;
            width: 100%;
            height: 600px;
            margin: 2rem auto;
        }

        .flip-card {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform 0.6s ease-in-out;
        }

        .flip-container.flipped .flip-card {
            transform: rotateY(180deg);
        }

        .flip-face {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border: 1px solid #333;
        }

        .flip-face-back {
            transform: rotateY(180deg);
        }

        /* Orders List (Front) */
        .orders-front {
            padding: 2rem;
            overflow-y: auto;
        }

        .orders-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--shell-yellow);
            padding-bottom: 1rem;
        }

        .orders-title {
            color: var(--shell-yellow);
            font-weight: 700;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .orders-subtitle {
            color: #adb5bd;
            font-size: 1rem;
        }

        .order-item {
            background: linear-gradient(145deg, #2a2a2a, #1f1f1f);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #333;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .order-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            border-color: var(--shell-yellow);
        }

        .order-item.selected {
            border-color: var(--shell-yellow);
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
        }

        .order-priority {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .priority-alta { background-color: #dc3545; color: white; }
        .priority-media { background-color: #ffc107; color: #212529; }
        .priority-baixa { background-color: #28a745; color: white; }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .order-id {
            color: var(--shell-yellow);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pendente { background-color: #6c757d; color: white; }
        .status-em_andamento { background-color: #007bff; color: white; }
        .status-aguardando_pecas { background-color: #ffc107; color: #212529; }
        .status-concluida { background-color: #28a745; color: white; }

        .order-details {
            color: #ddd;
        }

        .order-detail-row {
            display: flex;
            margin-bottom: 0.5rem;
        }

        .order-detail-label {
            font-weight: 500;
            color: var(--shell-yellow);
            width: 100px;
            flex-shrink: 0;
        }

        .order-detail-value {
            color: #ddd;
        }

        /* Order Detail (Back) */
        .order-detail-back {
            padding: 2rem;
            overflow-y: auto;
        }

        .detail-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--shell-yellow);
            padding-bottom: 1rem;
        }

        .detail-title {
            color: var(--shell-yellow);
            font-weight: 700;
            font-size: 1.6rem;
            margin-bottom: 0.5rem;
        }

        .back-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--shell-yellow);
            color: #222;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: #e6a700;
            transform: scale(1.1);
        }

        /* Sequential Cards */
        .sequential-cards {
            display: none;
        }

        .sequential-cards.active {
            display: block;
        }

        .card-navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .nav-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #333;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .nav-step.active {
            background: var(--shell-yellow);
            color: #222;
        }

        .nav-step.completed {
            background: #28a745;
            color: white;
        }

        .nav-step:not(.active):not(.completed):hover {
            background: #444;
            color: #ddd;
        }

        .card-content {
            background: linear-gradient(145deg, #2a2a2a, #1f1f1f);
            border-radius: 10px;
            padding: 2rem;
            border: 1px solid #333;
            min-height: 300px;
        }

        .card-step {
            display: none;
        }

        .card-step.active {
            display: block;
        }

        .step-title {
            color: var(--shell-yellow);
            font-weight: 600;
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: var(--shell-yellow);
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            background: #1a1a1a;
            border: 1px solid #444;
            color: #ddd;
            border-radius: 6px;
            padding: 0.75rem;
        }

        .form-control:focus {
            background: #1a1a1a;
            border-color: var(--shell-yellow);
            color: #ddd;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        .form-select {
            background: #1a1a1a;
            border: 1px solid #444;
            color: #ddd;
        }

        .form-select:focus {
            background: #1a1a1a;
            border-color: var(--shell-yellow);
            color: #ddd;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed #444;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: var(--shell-yellow);
            background: rgba(255, 193, 7, 0.05);
        }

        .file-upload-area.dragover {
            border-color: var(--shell-yellow);
            background: rgba(255, 193, 7, 0.1);
        }

        .uploaded-files {
            margin-top: 1rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #1a1a1a;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .file-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 6px;
        }

        /* Interactions Timeline */
        .interactions-timeline {
            max-height: 300px;
            overflow-y: auto;
        }

        .interaction-item {
            background: #1a1a1a;
            border-left: 3px solid var(--shell-yellow);
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 6px 6px 0;
        }

        .interaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .interaction-author {
            color: var(--shell-yellow);
            font-weight: 500;
        }

        .interaction-date {
            color: #adb5bd;
            font-size: 0.85rem;
        }

        .interaction-message {
            color: #ddd;
            line-height: 1.5;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            gap: 1rem;
        }

        .btn-shell {
            background: var(--shell-yellow);
            color: #222;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-shell:hover {
            background: #e6a700;
            transform: translateY(-2px);
        }

        .btn-shell:disabled {
            background: #666;
            color: #999;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* Loading States */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #333;
            border-radius: 50%;
            border-top-color: var(--shell-yellow);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .flip-container {
                height: 500px;
                margin: 1rem;
            }

            .orders-front, .order-detail-back {
                padding: 1rem;
            }

            .card-content {
                padding: 1rem;
                min-height: 250px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .nav-step {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }
        }

        /* Notification Badge */
        .notification-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--shell-yellow);
            color: #222;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification-badge.show {
            transform: translateX(0);
        }

        /* WebSocket Status */
        .connection-status {
            position: fixed;
            bottom: 20px;
            left: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
        }

        .status-connected {
            background: #28a745;
            color: white;
        }

        .status-disconnected {
            background: #dc3545;
            color: white;
        }

        .status-connecting {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Flip Container -->
            <div class="flip-container" id="flipContainer">
                <div class="flip-card">
                    <!-- Front Face - Orders List -->
                    <div class="flip-face flip-face-front">
                        <div class="orders-front">
                            <div class="orders-header">
                                <h1 class="orders-title">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    Minhas Ordens
                                </h1>
                                <p class="orders-subtitle">
                                    Técnico: {{ .TechnicianName }} | 
                                    <span id="ordersCount">0</span> ordens atribuídas
                                </p>
                            </div>

                            <div id="ordersLoading" class="text-center py-5">
                                <div class="loading-spinner"></div>
                                <p class="mt-3 text-light">Carregando ordens...</p>
                            </div>

                            <div id="ordersList" class="orders-list" style="display: none;">
                                <!-- Orders will be populated here -->
                            </div>

                            <div id="noOrdersMessage" class="text-center py-5" style="display: none;">
                                <i class="fas fa-clipboard-check fa-3x text-shell-yellow mb-3"></i>
                                <p class="text-light">Nenhuma ordem atribuída no momento.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Back Face - Order Details -->
                    <div class="flip-face flip-face-back">
                        <div class="order-detail-back">
                            <button class="back-button" id="backToList">
                                <i class="fas fa-arrow-left"></i>
                            </button>

                            <div class="detail-header">
                                <h2 class="detail-title" id="selectedOrderTitle">
                                    Detalhes da Ordem
                                </h2>
                            </div>

                            <!-- Sequential Cards Navigation -->
                            <div class="card-navigation">
                                <div class="nav-step active" data-step="1">1</div>
                                <div class="nav-step" data-step="2">2</div>
                                <div class="nav-step" data-step="3">3</div>
                                <div class="nav-step" data-step="4">4</div>
                                <div class="nav-step" data-step="5">5</div>
                                <div class="nav-step" data-step="6">6</div>
                            </div>

                            <!-- Card Content -->
                            <div class="card-content">
                                <!-- Step 1: Order Information (readonly) -->
                                <div class="card-step active" data-step="1">
                                    <h3 class="step-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Informações da Ordem
                                    </h3>
                                    <div id="orderInfoContent">
                                        <!-- Content populated by JavaScript -->
                                    </div>
                                </div>

                                <!-- Step 2: Status and Progress -->
                                <div class="card-step" data-step="2">
                                    <h3 class="step-title">
                                        <i class="fas fa-tasks me-2"></i>
                                        Status e Progresso
                                    </h3>
                                    <div class="form-group">
                                        <label class="form-label">Status da Ordem</label>
                                        <select class="form-select" id="orderStatus">
                                            <option value="pendente">Pendente</option>
                                            <option value="em_andamento">Em Andamento</option>
                                            <option value="aguardando_pecas">Aguardando Peças</option>
                                            <option value="concluida">Concluída</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Progresso (%)</label>
                                        <input type="range" class="form-range" id="progressRange" min="0" max="100" value="0">
                                        <div class="text-center mt-2">
                                            <span id="progressValue" class="text-shell-yellow">0%</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Observações do Progresso</label>
                                        <textarea class="form-control" id="progressNotes" rows="3" placeholder="Descreva o progresso atual..."></textarea>
                                    </div>
                                </div>

                                <!-- Step 3: Materials and Costs -->
                                <div class="card-step" data-step="3">
                                    <h3 class="step-title">
                                        <i class="fas fa-tools me-2"></i>
                                        Materiais e Custos
                                    </h3>
                                    <div class="form-group">
                                        <label class="form-label">Materiais Utilizados</label>
                                        <textarea class="form-control" id="materialsUsed" rows="4" placeholder="Liste os materiais utilizados..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Custo Estimado (R$)</label>
                                        <input type="number" class="form-control" id="estimatedCost" step="0.01" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Tempo Gasto (horas)</label>
                                        <input type="number" class="form-control" id="timeSpent" step="0.5" placeholder="0.0">
                                    </div>
                                </div>

                                <!-- Step 4: Photos and Attachments -->
                                <div class="card-step" data-step="4">
                                    <h3 class="step-title">
                                        <i class="fas fa-camera me-2"></i>
                                        Fotos e Anexos
                                    </h3>
                                    <div class="file-upload-area" id="fileUploadArea">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-shell-yellow mb-2"></i>
                                        <p class="text-light">Clique ou arraste arquivos aqui</p>
                                        <p class="text-muted small">Formatos aceitos: JPG, PNG, PDF (máx. 10MB)</p>
                                        <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.pdf" style="display: none;">
                                    </div>
                                    <div class="uploaded-files" id="uploadedFiles">
                                        <!-- Uploaded files will appear here -->
                                    </div>
                                </div>

                                <!-- Step 5: Interactions and Comments -->
                                <div class="card-step" data-step="5">
                                    <h3 class="step-title">
                                        <i class="fas fa-comments me-2"></i>
                                        Interações e Comentários
                                    </h3>
                                    <div class="form-group">
                                        <label class="form-label">Adicionar Comentário</label>
                                        <textarea class="form-control" id="newComment" rows="3" placeholder="Digite seu comentário..."></textarea>
                                        <button class="btn btn-shell mt-2" id="addCommentBtn">
                                            <i class="fas fa-plus me-2"></i>Adicionar Comentário
                                        </button>
                                    </div>
                                    <div class="interactions-timeline" id="interactionsTimeline">
                                        <!-- Interactions will be populated here -->
                                    </div>
                                </div>

                                <!-- Step 6: Completion and Approval -->
                                <div class="card-step" data-step="6">
                                    <h3 class="step-title">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Finalização e Aprovação
                                    </h3>
                                    <div class="form-group">
                                        <label class="form-label">Resumo Final</label>
                                        <textarea class="form-control" id="finalSummary" rows="4" placeholder="Descreva o trabalho realizado e resultados..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="workCompleted">
                                            <label class="form-check-label text-light" for="workCompleted">
                                                Confirmo que o trabalho foi concluído satisfatoriamente
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="clientApproval">
                                            <label class="form-check-label text-light" for="clientApproval">
                                                Cliente aprovou o serviço realizado
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button class="btn-secondary" id="prevStepBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Anterior
                                </button>
                                <button class="btn-shell" id="nextStepBtn">
                                    Próximo<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button class="btn-shell" id="saveOrderBtn" style="display: none;">
                                    <i class="fas fa-save me-2"></i>Salvar Ordem
                                </button>
                                <button class="btn-shell" id="completeOrderBtn" style="display: none;">
                                    <i class="fas fa-check me-2"></i>Finalizar Ordem
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Badge -->
    <div class="notification-badge" id="notificationBadge">
        <i class="fas fa-bell me-2"></i>
        <span id="notificationText">Nova ordem atribuída!</span>
    </div>

    <!-- Connection Status -->
    <div class="connection-status status-connecting" id="connectionStatus">
        <i class="fas fa-wifi me-2"></i>
        <span>Conectando...</span>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/unified_orders.js"></script>
    <script src="/static/js/tecnico_unified.js"></script>

    <script>
        // Initialize the technician order management system
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the technician order manager
            const technicianManager = new TechnicianOrderManager({
                technicianId: {{ .TechnicianID }},
                apiBaseUrl: '/api/orders',
                websocketUrl: 'ws://localhost:8080/ws',
                enableNotifications: true
            });

            // Initialize the flip card interface
            const flipInterface = new FlipCardInterface();

            // Load initial data
            technicianManager.loadTechnicianOrders();

            // Set up real-time updates
            technicianManager.connectWebSocket();
        });

        // Flip Card Interface Controller
        class FlipCardInterface {
            constructor() {
                this.currentStep = 1;
                this.selectedOrder = null;
                this.isFlipped = false;
                
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // Back to list button
                document.getElementById('backToList').addEventListener('click', () => {
                    this.flipToFront();
                });

                // Step navigation
                document.querySelectorAll('.nav-step').forEach(step => {
                    step.addEventListener('click', (e) => {
                        const stepNumber = parseInt(e.target.dataset.step);
                        this.goToStep(stepNumber);
                    });
                });

                // Navigation buttons
                document.getElementById('prevStepBtn').addEventListener('click', () => {
                    this.previousStep();
                });

                document.getElementById('nextStepBtn').addEventListener('click', () => {
                    this.nextStep();
                });

                // Progress range
                document.getElementById('progressRange').addEventListener('input', (e) => {
                    document.getElementById('progressValue').textContent = e.target.value + '%';
                });

                // File upload
                this.initializeFileUpload();

                // Add comment button
                document.getElementById('addCommentBtn').addEventListener('click', () => {
                    this.addComment();
                });
            }

            flipToBack(order) {
                this.selectedOrder = order;
                this.isFlipped = true;
                document.getElementById('flipContainer').classList.add('flipped');
                document.getElementById('selectedOrderTitle').textContent = `Ordem #${order.id}`;
                this.populateOrderDetails(order);
                this.goToStep(1);
            }

            flipToFront() {
                this.isFlipped = false;
                document.getElementById('flipContainer').classList.remove('flipped');
                this.selectedOrder = null;
                this.currentStep = 1;
            }

            goToStep(stepNumber) {
                if (stepNumber < 1 || stepNumber > 6) return;

                // Hide all steps
                document.querySelectorAll('.card-step').forEach(step => {
                    step.classList.remove('active');
                });

                // Show target step
                document.querySelector(`[data-step="${stepNumber}"]`).classList.add('active');

                // Update navigation
                document.querySelectorAll('.nav-step').forEach(step => {
                    const num = parseInt(step.dataset.step);
                    step.classList.remove('active', 'completed');
                    
                    if (num === stepNumber) {
                        step.classList.add('active');
                    } else if (num < stepNumber) {
                        step.classList.add('completed');
                    }
                });

                // Update buttons
                this.updateNavigationButtons(stepNumber);
                this.currentStep = stepNumber;
            }

            updateNavigationButtons(stepNumber) {
                const prevBtn = document.getElementById('prevStepBtn');
                const nextBtn = document.getElementById('nextStepBtn');
                const saveBtn = document.getElementById('saveOrderBtn');
                const completeBtn = document.getElementById('completeOrderBtn');

                // Hide all buttons first
                [prevBtn, nextBtn, saveBtn, completeBtn].forEach(btn => {
                    btn.style.display = 'none';
                });

                // Show appropriate buttons
                if (stepNumber > 1) {
                    prevBtn.style.display = 'inline-block';
                }

                if (stepNumber < 6) {
                    nextBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'inline-block';
                } else {
                    completeBtn.style.display = 'inline-block';
                }
            }

            nextStep() {
                if (this.currentStep < 6) {
                    this.goToStep(this.currentStep + 1);
                }
            }

            previousStep() {
                if (this.currentStep > 1) {
                    this.goToStep(this.currentStep - 1);
                }
            }

            populateOrderDetails(order) {
                // Populate step 1 - Order Information
                const orderInfoContent = document.getElementById('orderInfoContent');
                orderInfoContent.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="order-detail-row">
                                <span class="order-detail-label">ID:</span>
                                <span class="order-detail-value">#${order.id}</span>
                            </div>
                            <div class="order-detail-row">
                                <span class="order-detail-label">Filial:</span>
                                <span class="order-detail-value">${order.branch_name}</span>
                            </div>
                            <div class="order-detail-row">
                                <span class="order-detail-label">Equipamento:</span>
                                <span class="order-detail-value">${order.equipment_name}</span>
                            </div>
                            <div class="order-detail-row">
                                <span class="order-detail-label">Tipo:</span>
                                <span class="order-detail-value">${order.equipment_type}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="order-detail-row">
                                <span class="order-detail-label">Prioridade:</span>
                                <span class="order-detail-value">
                                    <span class="priority-${order.priority}">${order.priority.toUpperCase()}</span>
                                </span>
                            </div>
                            <div class="order-detail-row">
                                <span class="order-detail-label">Criada em:</span>
                                <span class="order-detail-value">${new Date(order.created_at).toLocaleString('pt-BR')}</span>
                            </div>
                            <div class="order-detail-row">
                                <span class="order-detail-label">Solicitante:</span>
                                <span class="order-detail-value">${order.requested_by}</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="order-detail-row">
                            <span class="order-detail-label">Descrição:</span>
                        </div>
                        <div class="order-detail-value mt-2">
                            ${order.description || 'Nenhuma descrição fornecida.'}
                        </div>
                    </div>
                `;

                // Set current values in form fields
                document.getElementById('orderStatus').value = order.status;
                document.getElementById('progressRange').value = order.progress || 0;
                document.getElementById('progressValue').textContent = (order.progress || 0) + '%';
                document.getElementById('progressNotes').value = order.progress_notes || '';
                document.getElementById('materialsUsed').value = order.materials_used || '';
                document.getElementById('estimatedCost').value = order.estimated_cost || '';
                document.getElementById('timeSpent').value = order.time_spent || '';
                document.getElementById('finalSummary').value = order.final_summary || '';
                document.getElementById('workCompleted').checked = order.work_completed || false;
                document.getElementById('clientApproval').checked = order.client_approval || false;

                // Load interactions
                this.loadInteractions(order.id);
            }

            initializeFileUpload() {
                const uploadArea = document.getElementById('fileUploadArea');
                const fileInput = document.getElementById('fileInput');

                uploadArea.addEventListener('click', () => {
                    fileInput.click();
                });

                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });

                fileInput.addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });
            }

            handleFiles(files) {
                const uploadedFiles = document.getElementById('uploadedFiles');
                
                Array.from(files).forEach(file => {
                    if (this.validateFile(file)) {
                        const fileItem = this.createFileItem(file);
                        uploadedFiles.appendChild(fileItem);
                        this.uploadFile(file);
                    }
                });
            }

            validateFile(file) {
                const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
                const maxSize = 10 * 1024 * 1024; // 10MB

                if (!allowedTypes.includes(file.type)) {
                    alert('Tipo de arquivo não permitido. Use JPG, PNG ou PDF.');
                    return false;
                }

                if (file.size > maxSize) {
                    alert('Arquivo muito grande. Máximo 10MB.');
                    return false;
                }

                return true;
            }

            createFileItem(file) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const isImage = file.type.startsWith('image/');
                const preview = isImage ? 
                    `<img src="${URL.createObjectURL(file)}" class="file-preview" alt="${file.name}">` :
                    `<i class="fas fa-file-pdf fa-2x text-danger"></i>`;

                fileItem.innerHTML = `
                    <div class="d-flex align-items-center">
                        ${preview}
                        <div class="ms-3">
                            <div class="text-light">${file.name}</div>
                            <div class="text-muted small">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()">
                        <i class="fas fa-trash"></i>
                    </button>
                `;

                return fileItem;
            }

            uploadFile(file) {
                // Implementation for file upload to server
                console.log('Uploading file:', file.name);
                // This would integrate with the unified orders API
            }

            addComment() {
                const commentText = document.getElementById('newComment').value.trim();
                if (!commentText) return;

                const interaction = {
                    author: '{{ .TechnicianName }}',
                    message: commentText,
                    date: new Date(),
                    type: 'comment'
                };

                this.addInteractionToTimeline(interaction);
                document.getElementById('newComment').value = '';

                // Save to server
                if (this.selectedOrder) {
                    this.saveInteraction(this.selectedOrder.id, interaction);
                }
            }

            loadInteractions(orderId) {
                // Load interactions from server
                // This would integrate with the unified orders API
                console.log('Loading interactions for order:', orderId);
            }

            addInteractionToTimeline(interaction) {
                const timeline = document.getElementById('interactionsTimeline');
                const interactionElement = document.createElement('div');
                interactionElement.className = 'interaction-item';
                
                interactionElement.innerHTML = `
                    <div class="interaction-header">
                        <span class="interaction-author">${interaction.author}</span>
                        <span class="interaction-date">${interaction.date.toLocaleString('pt-BR')}</span>
                    </div>
                    <div class="interaction-message">${interaction.message}</div>
                `;

                timeline.insertBefore(interactionElement, timeline.firstChild);
            }

            saveInteraction(orderId, interaction) {
                // Save interaction to server via unified API
                console.log('Saving interaction for order:', orderId, interaction);
            }
        }

        // Global flip interface instance
        let flipInterface;

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            flipInterface = new FlipCardInterface();
        });
    </script>
</body>
</html>
{{ end }}