{{ define "galeria/galeria_tecnico_new.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Técnico - Rede Tradição</title>
    <link rel="icon" href="/static/images/favicon.ico" type="image/x-icon">

    <!-- Bootstrap e estilos base -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/sidebar.css">
    <link rel="stylesheet" href="/static/css/sidebar-profile.css">
    <link rel="stylesheet" href="/static/css/galeria.css">
    <link rel="stylesheet" href="/static/css/galeria_new.css">
    <link rel="stylesheet" href="/static/css/tecnico_unified.css">

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap" rel="stylesheet">

    <style>
        /* Dashboard específico para técnicos */
        .dashboard-header {
            background: linear-gradient(135deg, #1a1a1a, #252525);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #333;
        }

        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid #333;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            border-color: var(--shell-yellow);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--shell-yellow);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #ddd;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .orders-section {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #333;
        }

        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #333;
        }

        .section-title {
            color: var(--shell-yellow);
            font-weight: 600;
            font-size: 1.5rem;
            margin: 0;
        }

        .order-card {
            background: linear-gradient(145deg, #1a1a1a, #151515);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #333;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .order-card:hover {
            transform: translateY(-2px);
            border-color: var(--shell-yellow);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .order-id {
            color: var(--shell-yellow);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .status-in-progress {
            background-color: rgba(13, 202, 240, 0.2);
            color: #0dcaf0;
            border: 1px solid #0dcaf0;
        }

        .status-completed {
            background-color: rgba(25, 135, 84, 0.2);
            color: #198754;
            border: 1px solid #198754;
        }

        .order-details {
            color: #ddd;
            font-size: 0.9rem;
        }

        .order-meta {
            display: flex;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
            color: #adb5bd;
        }

        .notification-badge {
            position: relative;
        }

        .notification-badge::after {
            content: attr(data-count);
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--shell-red);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .branch-card {
            background: linear-gradient(145deg, #252525, #1a1a1a);
            border-radius: 12px;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            border: 1px solid #333;
            transition: all 0.3s ease;
        }

        .branch-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
            border-color: var(--shell-yellow);
        }

        .branch-card .card-img-top {
            height: 160px;
            background-color: #151515;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .branch-card .card-img-top .branch-icon {
            font-size: 4rem;
            color: var(--shell-yellow);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .branch-card:hover .card-img-top .branch-icon {
            transform: scale(1.1);
            opacity: 1;
        }

        .branch-card .card-img-top::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
            opacity: 0.7;
        }

        .branch-card .card-body {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .branch-card .card-title {
            color: var(--shell-yellow);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            text-align: center;
        }

        .branch-card .card-text {
            color: #ddd;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
        }

        .branch-card .card-text i {
            color: var(--shell-yellow);
            width: 20px;
            margin-right: 0.75rem;
        }

        .branch-card .card-footer {
            background-color: #1a1a1a;
            border-top: 1px solid #333;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            gap: 0.5rem;
        }

        .branch-card .btn-shell {
            background-color: var(--shell-yellow);
            color: #222;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            flex: 1;
            font-size: 0.85rem;
        }

        .branch-card .btn-shell:hover {
            background-color: #e6a700;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary-shell {
            background-color: transparent;
            color: var(--shell-yellow);
            border: 1px solid var(--shell-yellow);
        }

        .btn-secondary-shell:hover {
            background-color: var(--shell-yellow);
            color: #222;
        }

        .page-title-container {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title {
            color: var(--shell-yellow);
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
            display: inline-block;
            position: relative;
        }

        .page-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--shell-red), var(--shell-yellow));
            border-radius: 3px;
        }

        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 1rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            grid-column: 1 / -1;
        }

        .empty-state-icon {
            font-size: 4rem;
            color: var(--shell-yellow);
            opacity: 0.7;
            margin-bottom: 1.5rem;
        }

        .empty-state-text {
            color: #adb5bd;
            font-size: 1.2rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--shell-yellow);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .connection-online {
            background-color: rgba(25, 135, 84, 0.9);
            color: white;
            border: 1px solid #198754;
        }

        .connection-offline {
            background-color: rgba(220, 53, 69, 0.9);
            color: white;
            border: 1px solid #dc3545;
        }

        @media (max-width: 768px) {
            .dashboard-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .branches-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1.5rem;
            }

            .page-title {
                font-size: 1.75rem;
            }

            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .branches-grid {
                grid-template-columns: 1fr;
                gap: 1.25rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .branch-card .card-footer {
                flex-direction: column;
            }
        }
    </style>
</head>
<body class="dark-theme">
    <!-- Incluindo o menu lateral -->
    {{ template "layouts/sidebar.html" . }}

    <!-- Status de conexão -->
    <div id="connectionStatus" class="connection-status connection-online">
        <i class="fas fa-wifi me-1"></i> Online
    </div>

    <!-- Conteúdo principal -->
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Header do Dashboard -->
            <div class="dashboard-header">
                <div class="page-title-container">
                    <h1 class="page-title">
                        <i class="fas fa-user-cog me-2"></i> Dashboard Técnico
                    </h1>
                    <p class="text-muted small">Bem-vindo, {{ .TechnicianName }} | ID: {{ .UserID }}</p>
                </div>

                <!-- Estatísticas do Dashboard -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="pendingOrdersCount">-</div>
                        <div class="stat-label">Ordens Pendentes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="inProgressOrdersCount">-</div>
                        <div class="stat-label">Em Andamento</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completedTodayCount">-</div>
                        <div class="stat-label">Concluídas Hoje</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalBranchesCount">{{ len .Branches }}</div>
                        <div class="stat-label">Postos Atendidos</div>
                    </div>
                </div>
            </div>

            <!-- Seção de Ordens Atribuídas -->
            <div class="orders-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-clipboard-list me-2"></i> Minhas Ordens
                        <span class="notification-badge" id="ordersNotificationBadge" data-count="0" style="display: none;"></span>
                    </h3>
                    <button type="button" class="btn btn-shell" id="refreshOrdersBtn">
                        <i class="fas fa-sync-alt me-1"></i> Atualizar
                    </button>
                </div>

                <div id="ordersLoading" class="text-center py-4">
                    <div class="loading-spinner"></div>
                    <p class="mt-2 text-light">Carregando suas ordens...</p>
                </div>

                <div id="ordersList" style="display: none;">
                    <!-- Preenchido via JavaScript -->
                </div>

                <div id="noOrdersMessage" class="text-center py-4" style="display: none;">
                    <i class="fas fa-clipboard-check fa-3x text-shell-yellow mb-3"></i>
                    <p class="text-light">Nenhuma ordem atribuída no momento.</p>
                </div>
            </div>

            <!-- Seção de Postos -->
            <div class="orders-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-gas-pump me-2"></i> Postos Atendidos
                    </h3>
                </div>

                <!-- Grid de cards de postos -->
                <div class="branches-grid">
                    {{ if eq (len .Branches) 0 }}
                    <div class="empty-state">
                        <i class="fas fa-exclamation-circle empty-state-icon"></i>
                        <p class="empty-state-text">Nenhum posto encontrado para o seu perfil.</p>
                    </div>
                    {{ else }}
                    {{ range .Branches }}
                    <div class="branch-item">
                        <div class="branch-card">
                            <div class="card-img-top">
                                <div class="branch-icon">
                                    <i class="fas fa-gas-pump"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">{{ .Name }}</h5>
                                {{ if .Address }}
                                <p class="card-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ .Address }}
                                </p>
                                {{ end }}
                                {{ if .City }}
                                <p class="card-text">
                                    <i class="fas fa-city"></i>
                                    {{ .City }}{{ if .State }}, {{ .State }}{{ end }}
                                </p>
                                {{ end }}
                                {{ if .Phone }}
                                <p class="card-text">
                                    <i class="fas fa-phone"></i>
                                    {{ .Phone }}
                                </p>
                                {{ end }}
                            </div>
                            <div class="card-footer">
                                <button type="button" class="btn btn-shell view-branch-equipment" data-branch-id="{{ .ID }}" data-branch-name="{{ .Name }}">
                                    <i class="fas fa-tools me-1"></i> Equipamentos
                                </button>
                                <button type="button" class="btn btn-shell btn-secondary-shell view-branch-orders" data-branch-id="{{ .ID }}" data-branch-name="{{ .Name }}">
                                    <i class="fas fa-list me-1"></i> Ordens
                                </button>
                            </div>
                        </div>
                    </div>
                    {{ end }}
                    {{ end }}
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para exibir equipamentos da filial -->
    <div class="modal fade" id="branchEquipmentModal" tabindex="-1" aria-labelledby="branchEquipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="branchEquipmentModalLabel">
                        <i class="fas fa-tools me-2 text-shell-yellow"></i>
                        Equipamentos do Posto <span id="branchNameInModal"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="equipmentLoading" class="text-center py-5">
                        <div class="spinner-border text-shell-yellow" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-3 text-light">Carregando equipamentos...</p>
                    </div>

                    <div id="equipmentList" class="row g-4" style="display: none;">
                        <!-- Preenchido via JavaScript -->
                    </div>

                    <div id="noEquipmentMessage" class="text-center py-5" style="display: none;">
                        <i class="fas fa-exclamation-circle fa-3x text-shell-yellow mb-3"></i>
                        <p class="text-light">Nenhum equipamento encontrado para este posto.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para exibir detalhes do equipamento -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content modal-shell">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-info-circle me-2 text-shell-yellow"></i>
                        Detalhes do Equipamento
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div id="equipmentDetailLoading" class="text-center py-5">
                        <div class="spinner-border text-shell-yellow" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-3 text-light">Carregando detalhes...</p>
                    </div>

                    <div id="equipmentDetailContent" style="display: none;">
                        <!-- Preenchido via JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-shell" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/unified_orders.js"></script>
    <script src="/static/js/tecnico_unified.js"></script>

    <script>
        // Configuração global para o dashboard do técnico
        window.TECHNICIAN_CONFIG = {
            userId: {{ .UserID }},
            technicianName: '{{ .TechnicianName }}',
            wsUrl: '{{ .WebSocketURL }}',
            apiBaseUrl: '/api',
            refreshInterval: 30000, // 30 segundos
            notificationSound: true,
            vibration: true
        };

        // Inicialização do dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar o gerenciador de ordens unificado
            const orderManager = new UnifiedOrderManager({
                baseUrl: window.TECHNICIAN_CONFIG.apiBaseUrl,
                cacheTTL: 300000, // 5 minutos
                enableCache: true
            });

            // Inicializar o gerenciador específico do técnico
            const technicianManager = new TechnicianOrderManager(orderManager, window.TECHNICIAN_CONFIG);

            // Elementos do DOM
            const elements = {
                // Estatísticas
                pendingOrdersCount: document.getElementById('pendingOrdersCount'),
                inProgressOrdersCount: document.getElementById('inProgressOrdersCount'),
                completedTodayCount: document.getElementById('completedTodayCount'),
                totalBranchesCount: document.getElementById('totalBranchesCount'),
                
                // Ordens
                ordersLoading: document.getElementById('ordersLoading'),
                ordersList: document.getElementById('ordersList'),
                noOrdersMessage: document.getElementById('noOrdersMessage'),
                refreshOrdersBtn: document.getElementById('refreshOrdersBtn'),
                ordersNotificationBadge: document.getElementById('ordersNotificationBadge'),
                
                // Conexão
                connectionStatus: document.getElementById('connectionStatus'),
                
                // Modais
                branchEquipmentModal: document.getElementById('branchEquipmentModal'),
                branchNameInModal: document.getElementById('branchNameInModal'),
                equipmentLoading: document.getElementById('equipmentLoading'),
                equipmentList: document.getElementById('equipmentList'),
                noEquipmentMessage: document.getElementById('noEquipmentMessage'),
                equipmentDetailModal: document.getElementById('equipmentDetailModal'),
                equipmentDetailLoading: document.getElementById('equipmentDetailLoading'),
                equipmentDetailContent: document.getElementById('equipmentDetailContent')
            };

            // Instanciar modais do Bootstrap
            const branchModal = new bootstrap.Modal(elements.branchEquipmentModal);
            const detailModal = new bootstrap.Modal(elements.equipmentDetailModal);

            // Carregar dados iniciais
            loadDashboardData();

            // Configurar event listeners
            setupEventListeners();

            // Configurar atualização automática
            setInterval(loadDashboardData, window.TECHNICIAN_CONFIG.refreshInterval);

            // Função para carregar dados do dashboard
            async function loadDashboardData() {
                try {
                    // Carregar estatísticas e ordens do técnico
                    await Promise.all([
                        loadTechnicianStats(),
                        loadTechnicianOrders()
                    ]);
                } catch (error) {
                    console.error('Erro ao carregar dados do dashboard:', error);
                    technicianManager.showNotification('Erro ao carregar dados', 'error');
                }
            }

            // Função para carregar estatísticas do técnico
            async function loadTechnicianStats() {
                try {
                    const stats = await technicianManager.getTechnicianStats();
                    
                    elements.pendingOrdersCount.textContent = stats.pending || 0;
                    elements.inProgressOrdersCount.textContent = stats.inProgress || 0;
                    elements.completedTodayCount.textContent = stats.completedToday || 0;
                    
                    // Atualizar badge de notificação
                    const totalPending = stats.pending || 0;
                    if (totalPending > 0) {
                        elements.ordersNotificationBadge.setAttribute('data-count', totalPending);
                        elements.ordersNotificationBadge.style.display = 'inline-block';
                    } else {
                        elements.ordersNotificationBadge.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Erro ao carregar estatísticas:', error);
                }
            }

            // Função para carregar ordens do técnico
            async function loadTechnicianOrders() {
                elements.ordersLoading.style.display = 'block';
                elements.ordersList.style.display = 'none';
                elements.noOrdersMessage.style.display = 'none';

                try {
                    const orders = await technicianManager.getTechnicianOrders();
                    
                    elements.ordersLoading.style.display = 'none';

                    if (orders && orders.length > 0) {
                        renderOrdersList(orders);
                        elements.ordersList.style.display = 'block';
                    } else {
                        elements.noOrdersMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Erro ao carregar ordens:', error);
                    elements.ordersLoading.style.display = 'none';
                    elements.noOrdersMessage.style.display = 'block';
                    elements.noOrdersMessage.querySelector('p').textContent = 'Erro ao carregar ordens. Tente novamente.';
                }
            }

            // Função para renderizar lista de ordens
            function renderOrdersList(orders) {
                elements.ordersList.innerHTML = '';

                orders.forEach(order => {
                    const orderCard = createOrderCard(order);
                    elements.ordersList.appendChild(orderCard);
                });
            }

            // Função para criar card de ordem
            function createOrderCard(order) {
                const statusClass = getStatusClass(order.status);
                const statusText = getStatusText(order.status);
                const createdDate = new Date(order.created_at).toLocaleDateString('pt-BR');
                const priority = order.priority || 'normal';

                const orderCard = document.createElement('div');
                orderCard.className = 'order-card';
                orderCard.setAttribute('data-order-id', order.id);
                
                orderCard.innerHTML = `
                    <div class="order-header">
                        <div class="order-id">Ordem #${order.id}</div>
                        <div class="order-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="order-details">
                        <strong>${order.equipment_name || 'Equipamento não especificado'}</strong>
                        <br>
                        <small>${order.branch_name || 'Filial não especificada'}</small>
                    </div>
                    <div class="order-meta">
                        <span><i class="fas fa-calendar me-1"></i> ${createdDate}</span>
                        <span><i class="fas fa-flag me-1"></i> ${priority}</span>
                        ${order.estimated_completion ? `<span><i class="fas fa-clock me-1"></i> ${new Date(order.estimated_completion).toLocaleDateString('pt-BR')}</span>` : ''}
                    </div>
                `;

                // Adicionar event listener para abrir detalhes
                orderCard.addEventListener('click', () => {
                    window.location.href = `/tecnico/ordem/${order.id}/flip`;
                });

                return orderCard;
            }

            // Função para configurar event listeners
            function setupEventListeners() {
                // Botão de atualizar ordens
                elements.refreshOrdersBtn.addEventListener('click', loadTechnicianOrders);

                // Botões de visualização de equipamentos
                document.querySelectorAll('.view-branch-equipment').forEach(button => {
                    button.addEventListener('click', function() {
                        const branchId = this.getAttribute('data-branch-id');
                        const branchName = this.getAttribute('data-branch-name');
                        showBranchEquipments(branchId, branchName);
                    });
                });

                // Botões de visualização de ordens por filial
                document.querySelectorAll('.view-branch-orders').forEach(button => {
                    button.addEventListener('click', function() {
                        const branchId = this.getAttribute('data-branch-id');
                        const branchName = this.getAttribute('data-branch-name');
                        showBranchOrders(branchId, branchName);
                    });
                });

                // Configurar WebSocket para notificações em tempo real
                technicianManager.setupWebSocket();

                // Monitorar status de conexão
                window.addEventListener('online', () => updateConnectionStatus(true));
                window.addEventListener('offline', () => updateConnectionStatus(false));
            }

            // Função para mostrar equipamentos da filial
            async function showBranchEquipments(branchId, branchName) {
                elements.branchNameInModal.textContent = branchName;
                elements.equipmentLoading.style.display = 'block';
                elements.equipmentList.style.display = 'none';
                elements.noEquipmentMessage.style.display = 'none';

                branchModal.show();

                try {
                    const equipments = await orderManager.getBranchEquipments(branchId);
                    
                    elements.equipmentLoading.style.display = 'none';

                    if (equipments && equipments.length > 0) {
                        renderEquipmentsList(equipments);
                        elements.equipmentList.style.display = 'flex';
                    } else {
                        elements.noEquipmentMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Erro ao carregar equipamentos:', error);
                    elements.equipmentLoading.style.display = 'none';
                    elements.noEquipmentMessage.style.display = 'block';
                    elements.noEquipmentMessage.querySelector('p').textContent = 'Erro ao carregar equipamentos.';
                }
            }

            // Função para mostrar ordens da filial
            function showBranchOrders(branchId, branchName) {
                window.location.href = `/orders?branch_id=${branchId}`;
            }

            // Função para renderizar lista de equipamentos
            function renderEquipmentsList(equipments) {
                elements.equipmentList.innerHTML = '';

                equipments.forEach(equipment => {
                    const equipmentCard = createEquipmentCard(equipment);
                    elements.equipmentList.appendChild(equipmentCard);
                });

                // Adicionar event listeners aos botões de detalhes
                document.querySelectorAll('.view-equipment-detail').forEach(button => {
                    button.addEventListener('click', function() {
                        const equipmentId = this.getAttribute('data-equipment-id');
                        showEquipmentDetails(equipmentId);
                    });
                });
            }

            // Função para criar card de equipamento
            function createEquipmentCard(equipment) {
                const statusClass = equipment.status === 'ativo' ? 'success' :
                                   (equipment.status === 'manutencao' ? 'warning' : 'danger');
                const statusText = equipment.status === 'ativo' ? 'Ativo' :
                                  (equipment.status === 'manutencao' ? 'Em Manutenção' : 'Inativo');

                const equipmentCard = document.createElement('div');
                equipmentCard.className = 'col-md-4';
                equipmentCard.innerHTML = `
                    <div class="card h-100 bg-dark border-secondary">
                        <div class="card-body">
                            <h5 class="card-title text-shell-yellow">${equipment.name}</h5>
                            <p class="card-text text-light">
                                <strong>Tipo:</strong> ${formatEquipmentType(equipment.type)}
                            </p>
                            <p class="card-text text-light">
                                <strong>Modelo:</strong> ${equipment.model || 'N/A'}
                            </p>
                            <p class="card-text text-light">
                                <strong>Status:</strong>
                                <span class="badge bg-${statusClass}">${statusText}</span>
                            </p>
                        </div>
                        <div class="card-footer bg-dark border-secondary">
                            <button type="button" class="btn btn-sm btn-shell view-equipment-detail"
                                    data-equipment-id="${equipment.id}">
                                <i class="fas fa-info-circle me-1"></i> Detalhes
                            </button>
                        </div>
                    </div>
                `;

                return equipmentCard;
            }

            // Função para mostrar detalhes do equipamento
            async function showEquipmentDetails(equipmentId) {
                elements.equipmentDetailLoading.style.display = 'block';
                elements.equipmentDetailContent.style.display = 'none';

                detailModal.show();

                try {
                    const equipment = await orderManager.getEquipmentDetails(equipmentId);
                    
                    elements.equipmentDetailLoading.style.display = 'none';
                    renderEquipmentDetails(equipment);
                    elements.equipmentDetailContent.style.display = 'block';
                } catch (error) {
                    console.error('Erro ao carregar detalhes:', error);
                    elements.equipmentDetailLoading.style.display = 'none';
                    elements.equipmentDetailContent.innerHTML = `
                        <div class="alert alert-danger">
                            Erro ao carregar detalhes do equipamento. Tente novamente.
                        </div>
                    `;
                    elements.equipmentDetailContent.style.display = 'block';
                }
            }

            // Função para renderizar detalhes do equipamento
            function renderEquipmentDetails(equipment) {
                const lastMaintenance = equipment.last_maintenance_date ?
                    new Date(equipment.last_maintenance_date).toLocaleDateString('pt-BR') : 'N/A';
                const installationDate = equipment.installation_date ?
                    new Date(equipment.installation_date).toLocaleDateString('pt-BR') : 'N/A';

                const statusClass = equipment.status === 'ativo' ? 'success' :
                                   (equipment.status === 'manutencao' ? 'warning' : 'danger');
                const statusText = equipment.status === 'ativo' ? 'Ativo' :
                                  (equipment.status === 'manutencao' ? 'Em Manutenção' : 'Inativo');

                elements.equipmentDetailContent.innerHTML = `
                    <div class="equipment-detail-header mb-4">
                        <h3 class="equipment-detail-title text-shell-yellow">${equipment.name}</h3>
                        <span class="badge bg-${statusClass}">${statusText}</span>
                    </div>

                    <div class="row">
                        <div class="col-md-7">
                            <div class="equipment-detail-info">
                                <div class="equipment-detail-item mb-2">
                                    <span class="equipment-detail-label text-light">Tipo:</span>
                                    <span class="equipment-detail-value text-light">${formatEquipmentType(equipment.type)}</span>
                                </div>
                                <div class="equipment-detail-item mb-2">
                                    <span class="equipment-detail-label text-light">Modelo:</span>
                                    <span class="equipment-detail-value text-light">${equipment.model || 'N/A'}</span>
                                </div>
                                <div class="equipment-detail-item mb-2">
                                    <span class="equipment-detail-label text-light">Número de Série:</span>
                                    <span class="equipment-detail-value text-light">${equipment.serial_number || 'N/A'}</span>
                                </div>
                                <div class="equipment-detail-item mb-2">
                                    <span class="equipment-detail-label text-light">Data de Instalação:</span>
                                    <span class="equipment-detail-value text-light">${installationDate}</span>
                                </div>
                                <div class="equipment-detail-item mb-2">
                                    <span class="equipment-detail-label text-light">Última Manutenção:</span>
                                    <span class="equipment-detail-value text-light">${lastMaintenance}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 text-center">
                            <img src="/static/images/shell-pump.svg" alt="${equipment.name}" class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>

                    ${equipment.notes ? `
                    <div class="equipment-detail-notes mt-3">
                        <span class="equipment-detail-label text-light">Observações:</span>
                        <p class="equipment-detail-value text-light">${equipment.notes}</p>
                    </div>
                    ` : ''}
                `;
            }

            // Função para atualizar status de conexão
            function updateConnectionStatus(isOnline) {
                if (isOnline) {
                    elements.connectionStatus.className = 'connection-status connection-online';
                    elements.connectionStatus.innerHTML = '<i class="fas fa-wifi me-1"></i> Online';
                } else {
                    elements.connectionStatus.className = 'connection-status connection-offline';
                    elements.connectionStatus.innerHTML = '<i class="fas fa-wifi-slash me-1"></i> Offline';
                }
            }

            // Funções utilitárias
            function getStatusClass(status) {
                switch (status) {
                    case 'pending': return 'status-pending';
                    case 'in_progress': return 'status-in-progress';
                    case 'completed': return 'status-completed';
                    default: return 'status-pending';
                }
            }

            function getStatusText(status) {
                switch (status) {
                    case 'pending': return 'Pendente';
                    case 'in_progress': return 'Em Andamento';
                    case 'completed': return 'Concluída';
                    default: return 'Pendente';
                }
            }

            function formatEquipmentType(type) {
                const types = {
                    'bomba': 'Bomba de Combustível',
                    'tanque': 'Tanque de Armazenamento',
                    'compressor': 'Compressor de Ar',
                    'gerador': 'Gerador de Energia',
                    'sistema_pagamento': 'Sistema de Pagamento',
                    'outro': 'Outro'
                };

                return types[type] || type;
            }
        });
    </script>
</body>
</html>
{{ end }}
