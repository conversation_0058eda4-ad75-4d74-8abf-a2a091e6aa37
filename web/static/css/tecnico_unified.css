/* ===================================================================
   TÉCNICO UNIFIED CSS
   Sistema unificado de estilos para interfaces de técnicos
   Consolidação de Ordemtecnico.css + calendar_flip.css + design system
   =================================================================== */

/* ===================================================================
   1. VARIÁVEIS CSS UNIFICADAS
   =================================================================== */

:root {
  /* Shell Design System Variables - Base */
  --shell-primary: #2563eb;
  --shell-secondary: #64748b;
  --shell-success: #059669;
  --shell-warning: #d97706;
  --shell-error: #dc2626;
  --shell-info: #0891b2;
  
  /* Shell Background Colors */
  --shell-bg-primary: #ffffff;
  --shell-bg-secondary: #f8fafc;
  --shell-bg-tertiary: #f1f5f9;
  --shell-bg-dark: #0f172a;
  --shell-bg-dark-secondary: #1e293b;
  --shell-bg-dark-tertiary: #334155;
  
  /* Shell Text Colors */
  --shell-text-primary: #0f172a;
  --shell-text-secondary: #475569;
  --shell-text-tertiary: #64748b;
  --shell-text-inverse: #f8fafc;
  
  /* Shell Border Colors */
  --shell-border-light: #e2e8f0;
  --shell-border-medium: #cbd5e1;
  --shell-border-dark: #475569;
  
  /* Technician Specific Variables */
  --tech-primary: var(--shell-primary);
  --tech-success: var(--shell-success);
  --tech-warning: var(--shell-warning);
  --tech-error: var(--shell-error);
  --tech-info: var(--shell-info);
  
  /* Technician Status Colors */
  --tech-status-pending: #f59e0b;
  --tech-status-progress: #3b82f6;
  --tech-status-completed: #10b981;
  --tech-status-cancelled: #ef4444;
  --tech-status-approved: #8b5cf6;
  
  /* Technician Card Colors */
  --tech-card-bg: var(--shell-bg-primary);
  --tech-card-border: var(--shell-border-light);
  --tech-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --tech-card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* Animation Variables */
  --tech-transition-fast: 0.15s ease-out;
  --tech-transition-normal: 0.3s ease-out;
  --tech-transition-slow: 0.6s cubic-bezier(0.4, 2, 0.6, 1);
  
  /* Touch Target Sizes */
  --tech-touch-target: 44px;
  --tech-touch-target-large: 56px;
  
  /* Spacing Scale */
  --tech-space-xs: 0.25rem;
  --tech-space-sm: 0.5rem;
  --tech-space-md: 1rem;
  --tech-space-lg: 1.5rem;
  --tech-space-xl: 2rem;
  --tech-space-2xl: 3rem;
  
  /* Border Radius */
  --tech-radius-sm: 0.375rem;
  --tech-radius-md: 0.5rem;
  --tech-radius-lg: 0.75rem;
  --tech-radius-xl: 1rem;
}

/* Dark Theme Overrides */
.dark-mode,
[data-theme="dark"] {
  --tech-card-bg: var(--shell-bg-dark-secondary);
  --tech-card-border: var(--shell-border-dark);
  --tech-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  --tech-card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
  
  --shell-text-primary: var(--shell-text-inverse);
  --shell-text-secondary: #cbd5e1;
  --shell-text-tertiary: #94a3b8;
}

/* ===================================================================
   2. COMPONENTES FLIP-CARD
   =================================================================== */

.tech-flip-card {
  width: 100%;
  max-width: 400px;
  height: 300px;
  perspective: 1000px;
  touch-action: manipulation;
  margin: var(--tech-space-md);
}

.tech-flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform var(--tech-transition-slow);
  transform-style: preserve-3d;
  cursor: pointer;
}

.tech-flip-card.flipped .tech-flip-card-inner,
.tech-flip-card:hover .tech-flip-card-inner {
  transform: rotateY(180deg);
}

.tech-flip-card-front,
.tech-flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--tech-radius-lg);
  background: var(--tech-card-bg);
  border: 1px solid var(--tech-card-border);
  box-shadow: var(--tech-card-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: var(--tech-space-lg);
}

.tech-flip-card-back {
  transform: rotateY(180deg);
}

.tech-flip-card:hover .tech-flip-card-front,
.tech-flip-card:hover .tech-flip-card-back {
  box-shadow: var(--tech-card-shadow-hover);
}

/* Loading State for Flip Cards */
.tech-flip-card.loading .tech-flip-card-inner {
  opacity: 0.7;
  pointer-events: none;
}

.tech-flip-card.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 2px solid var(--tech-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: tech-spin 1s linear infinite;
  z-index: 10;
}

/* Error State for Flip Cards */
.tech-flip-card.error .tech-flip-card-front,
.tech-flip-card.error .tech-flip-card-back {
  border-color: var(--tech-error);
  background: rgba(239, 68, 68, 0.05);
}

/* ===================================================================
   3. CARDS SEQUENCIAIS
   =================================================================== */

.tech-sequential-cards {
  display: flex;
  flex-direction: column;
  gap: var(--tech-space-lg);
  max-width: 800px;
  margin: 0 auto;
  padding: var(--tech-space-lg);
}

.tech-card-step {
  background: var(--tech-card-bg);
  border: 1px solid var(--tech-card-border);
  border-radius: var(--tech-radius-lg);
  box-shadow: var(--tech-card-shadow);
  padding: var(--tech-space-lg);
  transition: all var(--tech-transition-normal);
  position: relative;
  overflow: hidden;
}

.tech-card-step.active {
  border-color: var(--tech-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.tech-card-step.completed {
  border-color: var(--tech-success);
  background: rgba(16, 185, 129, 0.05);
}

.tech-card-step.invalid {
  border-color: var(--tech-error);
  background: rgba(239, 68, 68, 0.05);
}

.tech-card-step.pending {
  opacity: 0.6;
  pointer-events: none;
}

/* Progress Indicator */
.tech-progress-indicator {
  display: flex;
  align-items: center;
  margin-bottom: var(--tech-space-lg);
  gap: var(--tech-space-sm);
}

.tech-progress-step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--shell-bg-tertiary);
  border: 2px solid var(--shell-border-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--shell-text-secondary);
  transition: all var(--tech-transition-normal);
}

.tech-progress-step.active {
  background: var(--tech-primary);
  border-color: var(--tech-primary);
  color: white;
}

.tech-progress-step.completed {
  background: var(--tech-success);
  border-color: var(--tech-success);
  color: white;
}

.tech-progress-step.completed::after {
  content: '✓';
}

.tech-progress-line {
  flex: 1;
  height: 2px;
  background: var(--shell-border-light);
  position: relative;
}

.tech-progress-line.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: var(--tech-success);
  transition: width var(--tech-transition-normal);
}

/* Navigation Buttons */
.tech-card-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: var(--tech-space-lg);
  gap: var(--tech-space-md);
}

.tech-nav-button {
  min-height: var(--tech-touch-target);
  padding: var(--tech-space-sm) var(--tech-space-lg);
  border: 1px solid var(--tech-primary);
  border-radius: var(--tech-radius-md);
  background: transparent;
  color: var(--tech-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--tech-transition-fast);
  display: flex;
  align-items: center;
  gap: var(--tech-space-sm);
}

.tech-nav-button:hover {
  background: var(--tech-primary);
  color: white;
}

.tech-nav-button.primary {
  background: var(--tech-primary);
  color: white;
}

.tech-nav-button.primary:hover {
  background: #1d4ed8;
}

.tech-nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===================================================================
   4. COMPONENTES ESPECÍFICOS
   =================================================================== */

/* Upload de Fotos */
.tech-photo-upload {
  border: 2px dashed var(--shell-border-medium);
  border-radius: var(--tech-radius-lg);
  padding: var(--tech-space-xl);
  text-align: center;
  transition: all var(--tech-transition-normal);
  cursor: pointer;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--tech-space-md);
}

.tech-photo-upload:hover,
.tech-photo-upload.dragover {
  border-color: var(--tech-primary);
  background: rgba(37, 99, 235, 0.05);
}

.tech-photo-upload input[type="file"] {
  display: none;
}

.tech-photo-upload-icon {
  width: 48px;
  height: 48px;
  color: var(--shell-text-tertiary);
}

.tech-photo-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--tech-space-md);
  margin-top: var(--tech-space-md);
}

.tech-photo-preview-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--tech-radius-md);
  overflow: hidden;
  background: var(--shell-bg-tertiary);
}

.tech-photo-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tech-photo-preview-remove {
  position: absolute;
  top: var(--tech-space-xs);
  right: var(--tech-space-xs);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all var(--tech-transition-fast);
}

.tech-photo-preview-remove:hover {
  background: var(--tech-error);
  transform: scale(1.1);
}

/* Timeline de Interações */
.tech-timeline {
  position: relative;
  padding-left: var(--tech-space-xl);
}

.tech-timeline::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--shell-border-light);
}

.tech-timeline-item {
  position: relative;
  margin-bottom: var(--tech-space-lg);
  background: var(--tech-card-bg);
  border: 1px solid var(--tech-card-border);
  border-radius: var(--tech-radius-md);
  padding: var(--tech-space-md);
}

.tech-timeline-item::before {
  content: '';
  position: absolute;
  left: -18px;
  top: var(--tech-space-md);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--tech-primary);
  border: 3px solid var(--tech-card-bg);
}

.tech-timeline-item.success::before {
  background: var(--tech-success);
}

.tech-timeline-item.warning::before {
  background: var(--tech-warning);
}

.tech-timeline-item.error::before {
  background: var(--tech-error);
}

.tech-timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--tech-space-sm);
}

.tech-timeline-title {
  font-weight: 600;
  color: var(--shell-text-primary);
}

.tech-timeline-time {
  font-size: 0.875rem;
  color: var(--shell-text-tertiary);
}

.tech-timeline-content {
  color: var(--shell-text-secondary);
  line-height: 1.5;
}

/* Status Badges */
.tech-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--tech-space-xs);
  padding: var(--tech-space-xs) var(--tech-space-sm);
  border-radius: var(--tech-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.tech-status-badge.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--tech-status-pending);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.tech-status-badge.progress {
  background: rgba(59, 130, 246, 0.1);
  color: var(--tech-status-progress);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.tech-status-badge.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--tech-status-completed);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.tech-status-badge.cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--tech-status-cancelled);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.tech-status-badge.approved {
  background: rgba(139, 92, 246, 0.1);
  color: var(--tech-status-approved);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* Botões de Ação Contextuais */
.tech-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--tech-space-sm);
  margin-top: var(--tech-space-md);
}

.tech-action-button {
  min-height: var(--tech-touch-target);
  padding: var(--tech-space-sm) var(--tech-space-md);
  border: 1px solid transparent;
  border-radius: var(--tech-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--tech-transition-fast);
  display: flex;
  align-items: center;
  gap: var(--tech-space-xs);
  text-decoration: none;
}

.tech-action-button.primary {
  background: var(--tech-primary);
  color: white;
}

.tech-action-button.primary:hover {
  background: #1d4ed8;
}

.tech-action-button.success {
  background: var(--tech-success);
  color: white;
}

.tech-action-button.success:hover {
  background: #047857;
}

.tech-action-button.warning {
  background: var(--tech-warning);
  color: white;
}

.tech-action-button.warning:hover {
  background: #b45309;
}

.tech-action-button.error {
  background: var(--tech-error);
  color: white;
}

.tech-action-button.error:hover {
  background: #b91c1c;
}

.tech-action-button.secondary {
  background: transparent;
  color: var(--shell-text-secondary);
  border-color: var(--shell-border-medium);
}

.tech-action-button.secondary:hover {
  background: var(--shell-bg-tertiary);
  border-color: var(--shell-border-dark);
}

/* ===================================================================
   5. LAYOUT RESPONSIVO
   =================================================================== */

.tech-grid {
  display: grid;
  gap: var(--tech-space-lg);
  padding: var(--tech-space-lg);
}

/* Mobile First - Portrait */
@media (max-width: 767px) {
  .tech-grid {
    grid-template-columns: 1fr;
    padding: var(--tech-space-md);
  }
  
  .tech-flip-card {
    height: 250px;
    margin: var(--tech-space-sm);
  }
  
  .tech-sequential-cards {
    padding: var(--tech-space-md);
  }
  
  .tech-card-navigation {
    flex-direction: column;
  }
  
  .tech-nav-button {
    width: 100%;
    justify-content: center;
  }
  
  .tech-action-buttons {
    flex-direction: column;
  }
  
  .tech-action-button {
    width: 100%;
    justify-content: center;
  }
}

/* Tablet - Portrait */
@media (min-width: 768px) and (max-width: 1023px) {
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tech-flip-card {
    height: 280px;
  }
}

/* Tablet - Landscape */
@media (min-width: 1024px) and (max-width: 1279px) {
  .tech-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .tech-flip-card {
    height: 300px;
  }
}

/* Desktop */
@media (min-width: 1280px) {
  .tech-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Landscape Orientation Optimizations */
@media (orientation: landscape) and (max-height: 600px) {
  .tech-sequential-cards {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: var(--tech-space-xl);
  }
  
  .tech-card-step {
    min-width: 300px;
    flex-shrink: 0;
  }
  
  .tech-progress-indicator {
    flex-direction: column;
    position: fixed;
    left: var(--tech-space-md);
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }
  
  .tech-progress-line {
    width: 2px;
    height: var(--tech-space-lg);
  }
}

/* Touch Target Optimizations */
@media (pointer: coarse) {
  .tech-nav-button,
  .tech-action-button {
    min-height: var(--tech-touch-target-large);
    padding: var(--tech-space-md) var(--tech-space-lg);
  }
  
  .tech-progress-step {
    width: 40px;
    height: 40px;
  }
  
  .tech-photo-preview-remove {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

/* ===================================================================
   6. ESTADOS DE CONECTIVIDADE
   =================================================================== */

.tech-connectivity-indicator {
  position: fixed;
  top: var(--tech-space-md);
  right: var(--tech-space-md);
  padding: var(--tech-space-sm) var(--tech-space-md);
  border-radius: var(--tech-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  z-index: 1000;
  transition: all var(--tech-transition-normal);
}

.tech-connectivity-indicator.online {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  opacity: 0;
  pointer-events: none;
}

.tech-connectivity-indicator.offline {
  background: rgba(239, 68, 68, 0.9);
  color: white;
  opacity: 1;
}

.tech-connectivity-indicator.syncing {
  background: rgba(245, 158, 11, 0.9);
  color: white;
  opacity: 1;
}

/* Cached Data Indicators */
.tech-cached-data {
  position: relative;
}

.tech-cached-data::after {
  content: '📱';
  position: absolute;
  top: var(--tech-space-xs);
  right: var(--tech-space-xs);
  font-size: 12px;
  opacity: 0.7;
}

.tech-syncing-data {
  opacity: 0.8;
  position: relative;
}

.tech-syncing-data::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--tech-primary), transparent);
  animation: tech-sync-progress 2s ease-in-out infinite;
}

/* ===================================================================
   7. ACESSIBILIDADE
   =================================================================== */

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --tech-card-border: #000000;
    --shell-border-light: #000000;
    --shell-border-medium: #000000;
  }
  
  .tech-flip-card-front,
  .tech-flip-card-back,
  .tech-card-step {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .tech-flip-card-inner {
    transition: none;
  }
  
  .tech-card-step,
  .tech-nav-button,
  .tech-action-button,
  .tech-progress-step {
    transition: none;
  }
  
  .tech-photo-preview-remove:hover {
    transform: none;
  }
}

/* Focus Indicators */
.tech-nav-button:focus,
.tech-action-button:focus,
.tech-photo-upload:focus {
  outline: 2px solid var(--tech-primary);
  outline-offset: 2px;
}

/* Screen Reader Only Content */
.tech-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.tech-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--tech-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--tech-radius-sm);
  z-index: 1000;
}

.tech-skip-link:focus {
  top: 6px;
}

/* ===================================================================
   8. ANIMAÇÕES E MICRO-INTERAÇÕES
   =================================================================== */

/* Keyframes */
@keyframes tech-spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes tech-sync-progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes tech-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes tech-bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes tech-slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes tech-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Loading States */
.tech-loading {
  animation: tech-pulse 2s ease-in-out infinite;
}

.tech-loading-spinner {
  animation: tech-spin 1s linear infinite;
}

/* Success Animations */
.tech-success-animation {
  animation: tech-bounce-in 0.6s ease-out;
}

/* Error Shake Animation */
@keyframes tech-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

.tech-error-animation {
  animation: tech-shake 0.6s ease-in-out;
}

/* Notification Slide In */
.tech-notification {
  animation: tech-slide-in-right 0.3s ease-out;
}

/* Fade In for New Content */
.tech-fade-in {
  animation: tech-fade-in 0.3s ease-out;
}

/* Hover Effects */
.tech-hover-lift {
  transition: transform var(--tech-transition-fast);
}

.tech-hover-lift:hover {
  transform: translateY(-2px);
}

.tech-hover-scale {
  transition: transform var(--tech-transition-fast);
}

.tech-hover-scale:hover {
  transform: scale(1.02);
}

/* ===================================================================
   9. UTILITÁRIOS E HELPERS
   =================================================================== */

/* Spacing Utilities */
.tech-m-0 { margin: 0; }
.tech-m-xs { margin: var(--tech-space-xs); }
.tech-m-sm { margin: var(--tech-space-sm); }
.tech-m-md { margin: var(--tech-space-md); }
.tech-m-lg { margin: var(--tech-space-lg); }
.tech-m-xl { margin: var(--tech-space-xl); }

.tech-p-0 { padding: 0; }
.tech-p-xs { padding: var(--tech-space-xs); }
.tech-p-sm { padding: var(--tech-space-sm); }
.tech-p-md { padding: var(--tech-space-md); }
.tech-p-lg { padding: var(--tech-space-lg); }
.tech-p-xl { padding: var(--tech-space-xl); }

/* Text Utilities */
.tech-text-center { text-align: center; }
.tech-text-left { text-align: left; }
.tech-text-right { text-align: right; }

.tech-text-primary { color: var(--shell-text-primary); }
.tech-text-secondary { color: var(--shell-text-secondary); }
.tech-text-tertiary { color: var(--shell-text-tertiary); }

.tech-font-bold { font-weight: 700; }
.tech-font-semibold { font-weight: 600; }
.tech-font-medium { font-weight: 500; }

/* Display Utilities */
.tech-hidden { display: none; }
.tech-block { display: block; }
.tech-inline-block { display: inline-block; }
.tech-flex { display: flex; }
.tech-grid { display: grid; }

/* Flex Utilities */
.tech-flex-col { flex-direction: column; }
.tech-flex-row { flex-direction: row; }
.tech-items-center { align-items: center; }
.tech-justify-center { justify-content: center; }
.tech-justify-between { justify-content: space-between; }

/* Border Utilities */
.tech-border { border: 1px solid var(--tech-card-border); }
.tech-border-0 { border: none; }
.tech-rounded { border-radius: var(--tech-radius-md); }
.tech-rounded-lg { border-radius: var(--tech-radius-lg); }

/* Shadow Utilities */
.tech-shadow { box-shadow: var(--tech-card-shadow); }
.tech-shadow-lg { box-shadow: var(--tech-card-shadow-hover); }
.tech-shadow-none { box-shadow: none; }

/* Width Utilities */
.tech-w-full { width: 100%; }
.tech-w-auto { width: auto; }
.tech-max-w-sm { max-width: 24rem; }
.tech-max-w-md { max-width: 28rem; }
.tech-max-w-lg { max-width: 32rem; }

/* ===================================================================
   10. PRINT STYLES
   =================================================================== */

@media print {
  .tech-flip-card {
    perspective: none;
  }
  
  .tech-flip-card-inner {
    transform: none;
  }
  
  .tech-flip-card-back {
    transform: none;
    position: static;
    margin-top: var(--tech-space-md);
  }
  
  .tech-nav-button,
  .tech-action-button {
    display: none;
  }
  
  .tech-connectivity-indicator {
    display: none;
  }
  
  .tech-photo-preview-remove {
    display: none;
  }
  
  .tech-timeline::before {
    background: #000;
  }
  
  .tech-timeline-item::before {
    background: #000;
  }
}

/* ===================================================================
   FIM DO ARQUIVO
   =================================================================== */