/**
 * Technician Unified Order Manager
 * Extends the unified order system with technician-specific functionality
 * 
 * Features:
 * - Real-time WebSocket integration
 * - Offline queue management
 * - Local state persistence
 * - Photo/file upload handling
 * - Flip-card UI animations
 * - Push notification support
 */

class TechnicianOrderManager {
    constructor(config = {}) {
        this.config = {
            apiBaseUrl: config.apiBaseUrl || '/api',
            wsUrl: config.wsUrl || `ws://${window.location.host}/ws`,
            cachePrefix: 'tech_orders_',
            cacheTTL: config.cacheTTL || 300000, // 5 minutes
            maxRetries: config.maxRetries || 3,
            retryDelay: config.retryDelay || 1000,
            ...config
        };

        // State management
        this.state = {
            isOnline: navigator.onLine,
            isConnected: false,
            currentTechnician: null,
            orders: new Map(),
            offlineQueue: [],
            notifications: [],
            currentCard: 0,
            totalCards: 6
        };

        // WebSocket instance
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;

        // Service Worker registration
        this.swRegistration = null;

        // Initialize
        this.init();
    }

    /**
     * Initialize the manager
     */
    async init() {
        try {
            // Load persisted state
            this.loadPersistedState();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize WebSocket
            this.initWebSocket();
            
            // Register service worker
            await this.registerServiceWorker();
            
            // Load technician orders
            await this.loadTechnicianOrders();
            
            console.log('TechnicianOrderManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize TechnicianOrderManager:', error);
        }
    }

    /**
     * Setup event listeners for network and UI events
     */
    setupEventListeners() {
        // Network status
        window.addEventListener('online', () => {
            this.state.isOnline = true;
            this.processOfflineQueue();
            this.showNotification('Conexão restaurada', 'success');
        });

        window.addEventListener('offline', () => {
            this.state.isOnline = false;
            this.showNotification('Modo offline ativado', 'warning');
        });

        // Page visibility for reconnection
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.state.isOnline && !this.state.isConnected) {
                this.initWebSocket();
            }
        });

        // Beforeunload to persist state
        window.addEventListener('beforeunload', () => {
            this.persistState();
        });
    }

    /**
     * Initialize WebSocket connection
     */
    initWebSocket() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return;
        }

        try {
            this.ws = new WebSocket(this.config.wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.state.isConnected = true;
                this.reconnectAttempts = 0;
                
                // Subscribe to technician notifications
                this.ws.send(JSON.stringify({
                    type: 'subscribe',
                    channel: 'technician_orders',
                    technicianId: this.state.currentTechnician?.id
                }));

                this.processOfflineQueue();
            };

            this.ws.onmessage = (event) => {
                this.handleWebSocketMessage(event);
            };

            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.state.isConnected = false;
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.state.isConnected = false;
            };

        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * Handle incoming WebSocket messages
     */
    handleWebSocketMessage(event) {
        try {
            const message = JSON.parse(event.data);
            
            switch (message.type) {
                case 'order_assigned':
                    this.handleOrderAssigned(message.data);
                    break;
                case 'order_updated':
                    this.handleOrderUpdated(message.data);
                    break;
                case 'order_status_changed':
                    this.handleOrderStatusChanged(message.data);
                    break;
                case 'notification':
                    this.handleNotification(message.data);
                    break;
                default:
                    console.log('Unknown message type:', message.type);
            }
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
        }
    }

    /**
     * Schedule WebSocket reconnection with exponential backoff
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
        this.reconnectAttempts++;

        setTimeout(() => {
            if (this.state.isOnline) {
                console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.initWebSocket();
            }
        }, delay);
    }

    /**
     * Register service worker for push notifications
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.swRegistration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered successfully');
                
                // Request notification permission
                await this.requestNotificationPermission();
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    /**
     * Request notification permission
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('Notification permission granted');
                await this.subscribeToPushNotifications();
            }
        }
    }

    /**
     * Subscribe to push notifications
     */
    async subscribeToPushNotifications() {
        if (!this.swRegistration) return;

        try {
            const subscription = await this.swRegistration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(this.config.vapidPublicKey)
            });

            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
        } catch (error) {
            console.error('Failed to subscribe to push notifications:', error);
        }
    }

    /**
     * Convert VAPID key to Uint8Array
     */
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    /**
     * Send push subscription to server
     */
    async sendSubscriptionToServer(subscription) {
        try {
            await this.makeRequest('/api/push-subscription', {
                method: 'POST',
                body: JSON.stringify({
                    subscription,
                    technicianId: this.state.currentTechnician?.id
                })
            });
        } catch (error) {
            console.error('Failed to send subscription to server:', error);
        }
    }

    /**
     * Load technician orders from API
     */
    async loadTechnicianOrders() {
        try {
            const response = await this.makeRequest('/api/orders/technician');
            if (response.success) {
                response.data.orders.forEach(order => {
                    this.state.orders.set(order.id, order);
                });
                this.updateUI();
            }
        } catch (error) {
            console.error('Failed to load technician orders:', error);
            // Try to load from cache
            this.loadOrdersFromCache();
        }
    }

    /**
     * Update order status
     */
    async updateOrderStatus(orderId, status) {
        const action = {
            type: 'updateStatus',
            orderId,
            status,
            timestamp: Date.now()
        };

        if (this.state.isOnline && this.state.isConnected) {
            try {
                const response = await this.makeRequest(`/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    body: JSON.stringify({ status })
                });

                if (response.success) {
                    this.updateLocalOrder(orderId, { status });
                    this.showNotification('Status atualizado com sucesso', 'success');
                    return response;
                }
            } catch (error) {
                console.error('Failed to update order status:', error);
                this.addToOfflineQueue(action);
                this.showNotification('Ação salva para sincronização', 'warning');
            }
        } else {
            this.addToOfflineQueue(action);
            this.showNotification('Ação salva para sincronização', 'warning');
        }
    }

    /**
     * Upload photo for order
     */
    async uploadPhoto(orderId, file) {
        const action = {
            type: 'uploadPhoto',
            orderId,
            file: await this.fileToBase64(file),
            fileName: file.name,
            fileType: file.type,
            timestamp: Date.now()
        };

        if (this.state.isOnline && this.state.isConnected) {
            try {
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('orderId', orderId);

                const response = await this.makeRequest(`/api/orders/${orderId}/photos`, {
                    method: 'POST',
                    body: formData,
                    headers: {} // Let browser set Content-Type for FormData
                });

                if (response.success) {
                    this.updateLocalOrder(orderId, { 
                        photos: [...(this.state.orders.get(orderId)?.photos || []), response.data.photo]
                    });
                    this.showNotification('Foto enviada com sucesso', 'success');
                    return response;
                }
            } catch (error) {
                console.error('Failed to upload photo:', error);
                this.addToOfflineQueue(action);
                this.showNotification('Foto salva para sincronização', 'warning');
            }
        } else {
            this.addToOfflineQueue(action);
            this.showNotification('Foto salva para sincronização', 'warning');
        }
    }

    /**
     * Add interaction/comment to order
     */
    async addInteraction(orderId, message) {
        const action = {
            type: 'addInteraction',
            orderId,
            message,
            timestamp: Date.now()
        };

        if (this.state.isOnline && this.state.isConnected) {
            try {
                const response = await this.makeRequest(`/api/orders/${orderId}/interactions`, {
                    method: 'POST',
                    body: JSON.stringify({ message })
                });

                if (response.success) {
                    this.updateLocalOrder(orderId, {
                        interactions: [...(this.state.orders.get(orderId)?.interactions || []), response.data.interaction]
                    });
                    this.showNotification('Comentário adicionado', 'success');
                    return response;
                }
            } catch (error) {
                console.error('Failed to add interaction:', error);
                this.addToOfflineQueue(action);
                this.showNotification('Comentário salvo para sincronização', 'warning');
            }
        } else {
            this.addToOfflineQueue(action);
            this.showNotification('Comentário salvo para sincronização', 'warning');
        }
    }

    /**
     * Request approval for order
     */
    async requestApproval(orderId) {
        const action = {
            type: 'requestApproval',
            orderId,
            timestamp: Date.now()
        };

        if (this.state.isOnline && this.state.isConnected) {
            try {
                const response = await this.makeRequest(`/api/orders/${orderId}/request-approval`, {
                    method: 'POST'
                });

                if (response.success) {
                    this.updateLocalOrder(orderId, { status: 'pending_approval' });
                    this.showNotification('Aprovação solicitada', 'success');
                    return response;
                }
            } catch (error) {
                console.error('Failed to request approval:', error);
                this.addToOfflineQueue(action);
                this.showNotification('Solicitação salva para sincronização', 'warning');
            }
        } else {
            this.addToOfflineQueue(action);
            this.showNotification('Solicitação salva para sincronização', 'warning');
        }
    }

    /**
     * Mark order as complete
     */
    async markComplete(orderId) {
        return await this.updateOrderStatus(orderId, 'completed');
    }

    /**
     * Flip card animation helpers
     */
    flipCard(direction = 'next') {
        const card = document.querySelector('.flip-card-inner');
        if (!card) return;

        if (direction === 'next' && this.state.currentCard < this.state.totalCards - 1) {
            this.state.currentCard++;
        } else if (direction === 'prev' && this.state.currentCard > 0) {
            this.state.currentCard--;
        }

        card.style.transform = `rotateY(${this.state.currentCard * 180}deg)`;
        this.updateCardIndicators();
    }

    /**
     * Navigate to specific card
     */
    navigateToCard(cardIndex) {
        if (cardIndex >= 0 && cardIndex < this.state.totalCards) {
            this.state.currentCard = cardIndex;
            const card = document.querySelector('.flip-card-inner');
            if (card) {
                card.style.transform = `rotateY(${cardIndex * 180}deg)`;
                this.updateCardIndicators();
            }
        }
    }

    /**
     * Update card indicators
     */
    updateCardIndicators() {
        const indicators = document.querySelectorAll('.card-indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.state.currentCard);
        });

        // Update progress bar
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            const progress = ((this.state.currentCard + 1) / this.state.totalCards) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    /**
     * Form validation helpers
     */
    validateForm(formElement) {
        const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        const errors = [];

        inputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                errors.push(`${input.name || input.id} é obrigatório`);
                input.classList.add('error');
            } else {
                input.classList.remove('error');
            }
        });

        return { isValid, errors };
    }

    /**
     * Show visual feedback for actions
     */
    showFeedback(element, type = 'success') {
        element.classList.add(`feedback-${type}`);
        setTimeout(() => {
            element.classList.remove(`feedback-${type}`);
        }, 2000);
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };

        this.state.notifications.push(notification);
        this.displayNotification(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, 5000);
    }

    /**
     * Display notification in UI
     */
    displayNotification(notification) {
        const container = document.querySelector('.notifications-container') || this.createNotificationContainer();
        
        const notificationEl = document.createElement('div');
        notificationEl.className = `notification notification-${notification.type}`;
        notificationEl.dataset.id = notification.id;
        notificationEl.innerHTML = `
            <span class="notification-message">${notification.message}</span>
            <button class="notification-close" onclick="technicianManager.removeNotification(${notification.id})">×</button>
        `;

        container.appendChild(notificationEl);

        // Animate in
        setTimeout(() => {
            notificationEl.classList.add('show');
        }, 10);
    }

    /**
     * Create notification container if it doesn't exist
     */
    createNotificationContainer() {
        const container = document.createElement('div');
        container.className = 'notifications-container';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Remove notification
     */
    removeNotification(notificationId) {
        const notificationEl = document.querySelector(`[data-id="${notificationId}"]`);
        if (notificationEl) {
            notificationEl.classList.remove('show');
            setTimeout(() => {
                notificationEl.remove();
            }, 300);
        }

        this.state.notifications = this.state.notifications.filter(n => n.id !== notificationId);
    }

    /**
     * Offline queue management
     */
    addToOfflineQueue(action) {
        this.state.offlineQueue.push(action);
        this.persistState();
    }

    /**
     * Process offline queue when connection is restored
     */
    async processOfflineQueue() {
        if (!this.state.isOnline || !this.state.isConnected || this.state.offlineQueue.length === 0) {
            return;
        }

        const queue = [...this.state.offlineQueue];
        this.state.offlineQueue = [];

        for (const action of queue) {
            try {
                await this.processQueuedAction(action);
            } catch (error) {
                console.error('Failed to process queued action:', error);
                // Re-add to queue for retry
                this.state.offlineQueue.push(action);
            }
        }

        this.persistState();
    }

    /**
     * Process individual queued action
     */
    async processQueuedAction(action) {
        switch (action.type) {
            case 'updateStatus':
                return await this.updateOrderStatus(action.orderId, action.status);
            case 'uploadPhoto':
                const file = this.base64ToFile(action.file, action.fileName, action.fileType);
                return await this.uploadPhoto(action.orderId, file);
            case 'addInteraction':
                return await this.addInteraction(action.orderId, action.message);
            case 'requestApproval':
                return await this.requestApproval(action.orderId);
            default:
                console.warn('Unknown action type:', action.type);
        }
    }

    /**
     * Utility: Convert file to base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    /**
     * Utility: Convert base64 to file
     */
    base64ToFile(base64, fileName, fileType) {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], fileName, { type: fileType || mime });
    }

    /**
     * Make HTTP request with error handling and retries
     */
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`,
                ...options.headers
            },
            ...options
        };

        let lastError;
        for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
            try {
                const response = await fetch(this.config.apiBaseUrl + url, defaultOptions);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                lastError = error;
                if (attempt < this.config.maxRetries - 1) {
                    await this.delay(this.config.retryDelay * Math.pow(2, attempt));
                }
            }
        }

        throw lastError;
    }

    /**
     * Get authentication token
     */
    getAuthToken() {
        return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }

    /**
     * Utility: Delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * State persistence
     */
    persistState() {
        const stateToSave = {
            orders: Array.from(this.state.orders.entries()),
            offlineQueue: this.state.offlineQueue,
            currentTechnician: this.state.currentTechnician,
            timestamp: Date.now()
        };

        localStorage.setItem(this.config.cachePrefix + 'state', JSON.stringify(stateToSave));
    }

    /**
     * Load persisted state
     */
    loadPersistedState() {
        try {
            const saved = localStorage.getItem(this.config.cachePrefix + 'state');
            if (saved) {
                const state = JSON.parse(saved);
                
                // Check if cache is still valid
                if (Date.now() - state.timestamp < this.config.cacheTTL) {
                    this.state.orders = new Map(state.orders);
                    this.state.offlineQueue = state.offlineQueue || [];
                    this.state.currentTechnician = state.currentTechnician;
                }
            }
        } catch (error) {
            console.error('Failed to load persisted state:', error);
        }
    }

    /**
     * Load orders from cache
     */
    loadOrdersFromCache() {
        // This would load from IndexedDB or localStorage cache
        // Implementation depends on caching strategy
        console.log('Loading orders from cache...');
    }

    /**
     * Update local order data
     */
    updateLocalOrder(orderId, updates) {
        const order = this.state.orders.get(orderId);
        if (order) {
            Object.assign(order, updates);
            this.state.orders.set(orderId, order);
            this.persistState();
            this.updateUI();
        }
    }

    /**
     * Update UI with current state
     */
    updateUI() {
        // Dispatch custom event for UI updates
        window.dispatchEvent(new CustomEvent('technicianOrdersUpdated', {
            detail: {
                orders: Array.from(this.state.orders.values()),
                isOnline: this.state.isOnline,
                isConnected: this.state.isConnected
            }
        }));
    }

    /**
     * WebSocket message handlers
     */
    handleOrderAssigned(data) {
        this.state.orders.set(data.order.id, data.order);
        this.showNotification(`Nova ordem atribuída: #${data.order.id}`, 'info');
        this.updateUI();
        
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
            new Notification('Nova Ordem Atribuída', {
                body: `Ordem #${data.order.id} foi atribuída a você`,
                icon: '/static/images/notification-icon.png'
            });
        }
    }

    handleOrderUpdated(data) {
        this.updateLocalOrder(data.order.id, data.order);
        this.showNotification(`Ordem #${data.order.id} foi atualizada`, 'info');
    }

    handleOrderStatusChanged(data) {
        this.updateLocalOrder(data.orderId, { status: data.status });
        this.showNotification(`Status da ordem #${data.orderId} alterado para: ${data.status}`, 'info');
    }

    handleNotification(data) {
        this.showNotification(data.message, data.type || 'info');
    }

    /**
     * Cleanup method
     */
    destroy() {
        if (this.ws) {
            this.ws.close();
        }
        this.persistState();
        
        // Remove event listeners
        window.removeEventListener('online', this.handleOnline);
        window.removeEventListener('offline', this.handleOffline);
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
    }
}

// Global instance
let technicianManager;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Get configuration from meta tags or global variables
    const config = {
        apiBaseUrl: document.querySelector('meta[name="api-base-url"]')?.content || '/api',
        wsUrl: document.querySelector('meta[name="ws-url"]')?.content || `ws://${window.location.host}/ws`,
        vapidPublicKey: document.querySelector('meta[name="vapid-public-key"]')?.content
    };

    technicianManager = new TechnicianOrderManager(config);
    
    // Make it globally available
    window.technicianManager = technicianManager;
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TechnicianOrderManager;
}