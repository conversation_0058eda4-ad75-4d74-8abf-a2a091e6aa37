// Novo Link Management JavaScript adaptado para página simplificada
class NovoLinkManagement {
  constructor() {
    this.branches = [];
    this.technicians = [];
    this.providers = [];
    this.init();
  }

  async init() {
    await this.loadInitialData();
    this.populateSelectors();
    this.setupEventListeners();
    this.renderCards();
  }

  async loadInitialData() {
    try {
      console.log('[INIT] Carregando dados iniciais...');

      const [branchesResponse, techniciansResponse, providersResponse] = await Promise.all([
        fetch("/api/branches", { credentials: "include" }),
        fetch("/api/technicians", { credentials: "include" }),
        fetch("/api/providers", { credentials: "include" })
      ]);

      // Verificar se as respostas são válidas
      if (!branchesResponse.ok) {
        console.error('[INIT-ERROR] Erro na resposta de filiais:', branchesResponse.status, branchesResponse.statusText);
        throw new Error(`Erro ao carregar filiais: ${branchesResponse.status}`);
      }
      if (!techniciansResponse.ok) {
        console.error('[INIT-ERROR] Erro na resposta de técnicos:', techniciansResponse.status, techniciansResponse.statusText);
        throw new Error(`Erro ao carregar técnicos: ${techniciansResponse.status}`);
      }
      if (!providersResponse.ok) {
        console.error('[INIT-ERROR] Erro na resposta de prestadoras:', providersResponse.status, providersResponse.statusText);
        throw new Error(`Erro ao carregar prestadoras: ${providersResponse.status}`);
      }

      const branchesData = await branchesResponse.json();
      const techniciansData = await techniciansResponse.json();
      const providersData = await providersResponse.json();

      console.log('[INIT-DEBUG] Estrutura de dados recebida:');
      console.log('[INIT-DEBUG] Filiais:', branchesData);
      console.log('[INIT-DEBUG] Técnicos:', techniciansData);
      console.log('[INIT-DEBUG] Prestadoras:', providersData);

      // Extrair dados com verificação robusta de estrutura
      this.branches = this.extractDataArray(branchesData, ['branches', 'data', 'filiais'], 'filiais');
      this.technicians = this.extractDataArray(techniciansData, ['data', 'technicians', 'tecnico'], 'técnicos');
      this.providers = this.extractDataArray(providersData, ['data', 'providers', 'prestadoras'], 'prestadoras');

      console.log(`[INIT] Carregados: ${this.branches.length} filiais, ${this.technicians.length} técnicos, ${this.providers.length} prestadoras`);

      // Verificar se há dados
      if (this.branches.length === 0) {
        console.warn('[INIT-WARN] Nenhuma filial encontrada');
        this.showToast("Nenhuma filial encontrada", "warning");
      }
      if (this.technicians.length === 0) {
        console.warn('[INIT-WARN] Nenhum técnico encontrado');
      }
      if (this.providers.length === 0) {
        console.warn('[INIT-WARN] Nenhuma prestadora encontrada');
      }

    } catch (error) {
      console.error('[INIT-ERROR] Erro ao carregar dados:', error);
      this.showToast("Erro ao carregar dados. Recarregue a página.", "error");
    }
  }

  // Método auxiliar para extrair arrays de dados com fallbacks robustos
  extractDataArray(responseData, possibleKeys, dataType) {
    if (!responseData || typeof responseData !== 'object') {
      console.warn(`[EXTRACT-WARN] Dados inválidos para ${dataType}:`, responseData);
      return [];
    }

    // Tentar extrair dados usando as chaves possíveis
    for (const key of possibleKeys) {
      if (responseData[key] !== undefined) {
        // Verificar se é um array válido
        if (Array.isArray(responseData[key])) {
          console.log(`[EXTRACT-DEBUG] ${dataType} extraídos usando chave '${key}':`, responseData[key].length, 'itens');
          return responseData[key];
        }
        // Verificar se é null (caso comum quando não há dados)
        else if (responseData[key] === null) {
          console.log(`[EXTRACT-DEBUG] ${dataType} - chave '${key}' é null, retornando array vazio`);
          return [];
        }
      }
    }

    // Se não encontrou em nenhuma chave, verificar se o próprio objeto é um array
    if (Array.isArray(responseData)) {
      console.log(`[EXTRACT-DEBUG] ${dataType} extraídos diretamente como array:`, responseData.length, 'itens');
      return responseData;
    }

    console.warn(`[EXTRACT-WARN] Não foi possível extrair ${dataType}. Estrutura recebida:`, Object.keys(responseData));
    return [];
  }

  populateSelectors() {
    // Popular seletores de filtro de técnicos
    const filterTechnicianSelect = document.getElementById('filter-technician-branch');
    if (filterTechnicianSelect && this.technicians) {
      filterTechnicianSelect.innerHTML = '<option value="">Todos os técnicos</option>';
      this.technicians.forEach(technician => {
        const option = document.createElement('option');
        option.value = technician.id;
        option.textContent = technician.name || technician.nome || `Técnico ${technician.id}`;
        filterTechnicianSelect.appendChild(option);
      });
    }

    // Popular seletores de filtro de filiais
    const filterBranchSelect = document.getElementById('filter-branch');
    if (filterBranchSelect && this.branches) {
      filterBranchSelect.innerHTML = '<option value="">Todas as filiais</option>';
      this.branches.forEach(branch => {
        const option = document.createElement('option');
        option.value = this.getBranchId(branch);
        option.textContent = this.getBranchName(branch) || `Filial ${this.getBranchId(branch)}`;
        filterBranchSelect.appendChild(option);
      });
    }

    // Popular seletores de adição de vínculos
    const addTechnicianSelect = document.getElementById('add-technician-branch');
    if (addTechnicianSelect && this.technicians) {
      addTechnicianSelect.innerHTML = '<option value="">Selecione um técnico</option>';
      this.technicians.forEach(technician => {
        const option = document.createElement('option');
        option.value = technician.id;
        option.textContent = technician.name || technician.nome || `Técnico ${technician.id}`;
        addTechnicianSelect.appendChild(option);
      });
    }

    const addBranchSelect = document.getElementById('add-branch');
    if (addBranchSelect && this.branches) {
      addBranchSelect.innerHTML = '<option value="">Selecione uma filial</option>';
      this.branches.forEach(branch => {
        const option = document.createElement('option');
        option.value = this.getBranchId(branch);
        option.textContent = this.getBranchName(branch) || `Filial ${this.getBranchId(branch)}`;
        addBranchSelect.appendChild(option);
      });
    }

    console.log('[SELECTORS] Seletores populados:', {
      technicians: this.technicians?.length || 0,
      branches: this.branches?.length || 0
    });
  }

  setupEventListeners() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        clearTimeout(searchInput.timer);
        searchInput.timer = setTimeout(() => this.renderCards(), 500);
      });
    }
  }

  renderCards() {
    const searchInput = document.getElementById('search-input');
    const mainDisplayArea = document.getElementById('main-display-area');
    if (!mainDisplayArea) {
      console.error('[RENDER-ERROR] Elemento main-display-area não encontrado');
      return;
    }

    let searchTerm = searchInput ? searchInput.value.trim().toLowerCase() : "";
    let filteredBranches = this.branches || [];

    if (searchTerm) {
      filteredBranches = filteredBranches.filter(branch => {
        if (!branch) return false;
        
        // Verificação robusta de ID
        const branchId = this.getBranchId(branch);
        const branchName = this.getBranchName(branch);
        
        return branchId.toString().includes(searchTerm) || 
               branchName.toLowerCase().includes(searchTerm);
      });
    }

    mainDisplayArea.innerHTML = '';
    
    if (filteredBranches.length === 0) {
      mainDisplayArea.innerHTML = `<p style="color: rgba(255,255,255,0.5); text-align: center; grid-column: 1 / -1;">Nenhuma filial encontrada.</p>`;
      return;
    }

    filteredBranches.forEach((branch, index) => {
      try {
        if (!branch) {
          console.warn(`[RENDER-WARN] Filial inválida no índice ${index}:`, branch);
          return;
        }

        const card = document.createElement('div');
        card.className = 'branch-card';
        
        // Determinação robusta do status
        const status = this.getBranchStatus(branch);
        const branchId = this.getBranchId(branch);
        const responsibleName = this.getBranchResponsible(branch);
        
        console.log(`[RENDER-DEBUG] Renderizando filial ${branchId}: status=${status}, responsável=${responsibleName}`);
        
        card.innerHTML = `
          <div class="status-dot ${status}" title="${this.getStatusTitle(status)}"></div>
          <div style="margin-bottom: 0.5rem;"><i class="fas fa-store-alt" style="font-size: 2.5rem; color: #f4b31d;"></i></div>
          <h3 style="font-weight: 700; font-size: 1.25rem; color: #fff;">Filial ${branchId}</h3>
          <p style="font-size: 0.875rem; color: rgba(255,255,255,0.7);">${responsibleName}</p>
        `;
        
        card.addEventListener('click', async () => { 
          try {
            await this.showBranchDetailsModal(branch); 
          } catch (error) {
            console.error('[RENDER-ERROR] Erro ao abrir modal da filial:', error);
            this.showToast('Erro ao carregar detalhes da filial', 'error');
          }
        });
        
        mainDisplayArea.appendChild(card);
      } catch (error) {
        console.error(`[RENDER-ERROR] Erro ao renderizar filial no índice ${index}:`, error, branch);
      }
    });
  }

  // Métodos auxiliares para extração robusta de dados da filial
  getBranchId(branch) {
    return branch.id || branch.branch_id || branch.filial_id || 'N/A';
  }

  getBranchName(branch) {
    return branch.name || branch.branch_name || branch.nome || branch.filial_nome || '';
  }

  getBranchStatus(branch) {
    // Prioridade: status explícito > is_active > ativo > padrão
    if (branch.status) {
      return branch.status.toLowerCase();
    }
    if (typeof branch.is_active === 'boolean') {
      return branch.is_active ? 'online' : 'offline';
    }
    if (typeof branch.ativo === 'boolean') {
      return branch.ativo ? 'online' : 'offline';
    }
    if (typeof branch.active === 'boolean') {
      return branch.active ? 'online' : 'offline';
    }
    return 'unknown';
  }

  getBranchResponsible(branch) {
    return branch.responsible_name || 
           branch.manager || 
           branch.responsavel || 
           branch.gerente || 
           branch.manager_name || 
           branch.responsible || 
           'Sem responsável';
  }

  getStatusTitle(status) {
    const statusMap = {
      'online': 'Filial Online',
      'offline': 'Filial Offline',
      'unknown': 'Status Desconhecido',
      'ativo': 'Filial Ativa',
      'inativo': 'Filial Inativa'
    };
    return statusMap[status] || `Status: ${status}`;
  }

  async showBranchDetailsModal(branch) {
    const modalContent = document.getElementById('details-modal-content');
    if (!modalContent) {
      console.error('[MODAL-ERROR] Elemento details-modal-content não encontrado');
      return;
    }

    if (!branch) {
      console.error('[MODAL-ERROR] Dados da filial não fornecidos');
      this.showToast('Erro: dados da filial não disponíveis', 'error');
      return;
    }

    const branchId = this.getBranchId(branch);
    console.log(`[MODAL] Abrindo modal para filial ${branchId}`);

    try {
      // Buscar vínculos técnicos e prestadoras da filial
      const [techLinksResp, providerLinksResp] = await Promise.all([
        fetch(`/api/novo-link-management/links/technician-branch?branch_id=${branchId}`, { credentials: 'include' }),
        fetch(`/api/novo-link-management/links/provider-branch?branch_id=${branchId}`, { credentials: 'include' })
      ]);

      // Verificação robusta das respostas
      let techLinksData = {};
      let providerLinksData = {};

      if (techLinksResp.ok) {
        try {
          techLinksData = await techLinksResp.json();
        } catch (error) {
          console.error('[MODAL-ERROR] Erro ao parsear resposta de vínculos técnicos:', error);
          techLinksData = { success: false, data: [] };
        }
      } else {
        console.error('[MODAL-ERROR] Erro na resposta de vínculos técnicos:', techLinksResp.status, techLinksResp.statusText);
        techLinksData = { success: false, data: [] };
      }

      if (providerLinksResp.ok) {
        try {
          providerLinksData = await providerLinksResp.json();
        } catch (error) {
          console.error('[MODAL-ERROR] Erro ao parsear resposta de vínculos prestadoras:', error);
          providerLinksData = { success: false, data: [] };
        }
      } else {
        console.error('[MODAL-ERROR] Erro na resposta de vínculos prestadoras:', providerLinksResp.status, providerLinksResp.statusText);
        providerLinksData = { success: false, data: [] };
      }

      // Extrair dados com verificação robusta
      const techLinks = this.extractLinksData(techLinksData, 'técnicos');
      const providerLinks = this.extractLinksData(providerLinksData, 'prestadoras');

      // Log para debug
      console.log(`[MODAL] Filial ${branchId}: ${techLinks.length} técnicos, ${providerLinks.length} prestadoras`);
      console.log('[MODAL-DEBUG] Vínculos técnicos:', techLinks);
      console.log('[MODAL-DEBUG] Vínculos prestadoras:', providerLinks);

      const branchAddress = branch.address || branch.endereco || '';
      const branchManager = this.getBranchResponsible(branch);

      let html = `
        <div class="modal-header" style="background: linear-gradient(135deg, #f4b31d 0%, #ffd700 100%); color: #1a1a1a; padding: 1.5rem; border-radius: 10px 10px 0 0; border-bottom: 2px solid rgba(244, 179, 29, 0.3);">
          <div style="flex: 1;">
            <h2 style="margin: 0; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.75rem; text-transform: uppercase; letter-spacing: 1px;">
              <i class="fas fa-building me-2" style="color: #ED1C24;"></i>Filial ${branchId}
            </h2>
            <p style="margin: 0.5rem 0 0 0; font-size: 0.95rem; opacity: 0.8; font-weight: 500;">${branchAddress || 'Endereço não informado'}</p>
            <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; opacity: 0.7;">
              <i class="fas fa-user-tie me-1"></i>Responsável: <strong>${branchManager}</strong>
            </p>
          </div>
          <button class="close-modal-btn" style="background: none; border: none; color: #ED1C24; font-size: 1.5rem; cursor: pointer; padding: 0.5rem; border-radius: 50%; transition: all 0.3s ease;">
            <i class="fas fa-times"></i>
          </button>
        </div>
      <div class="modal-body" style="padding: 2rem; background: rgba(255,255,255,0.02); border-radius: 0 0 10px 10px;">
        <div id="management-tabs">
          <div style="border-bottom: 2px solid rgba(244, 179, 29, 0.3); margin-bottom: 1.5rem; padding-bottom: 0.5rem;">
            <nav style="display: flex; gap: 2rem;" aria-label="Tabs">
              <button data-tab="tecnicos" class="tab-button active-tab" style="background: none; border: none; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.1rem; text-transform: uppercase; letter-spacing: 1px; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border-bottom: 3px solid #f4b31d;">
                <i class="fas fa-users me-2"></i>Técnicos
              </button>
              <button data-tab="prestadoras" class="tab-button" style="background: none; border: none; color: rgba(255,255,255,0.6); font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.1rem; text-transform: uppercase; letter-spacing: 1px; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border-bottom: 3px solid transparent;">
                <i class="fas fa-building me-2"></i>Prestadoras
              </button>
            </nav>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-bottom: 1.5rem;">
            <button id="btnNovoVinculo" class="btn btn-shell" style="background: linear-gradient(135deg, #f4b31d 0%, #ED1C24 100%); color: #1a1a1a; border: none; border-radius: 10px; padding: 0.75rem 1.5rem; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1rem; text-transform: uppercase; letter-spacing: 1px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(244, 179, 29, 0.3);">
              <i class="fas fa-plus me-2"></i>Novo Vínculo
            </button>
          </div>
          <div style="background: rgba(255,255,255,0.05); border: 1px solid rgba(244,179,29,0.3); border-radius: 10px; padding: 1.5rem; min-height: 300px; backdrop-filter: blur(10px);">
            <div id="tab-content-tecnicos" class="tab-content">
              <h3 style="color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.3rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 1rem; border-bottom: 2px solid rgba(244,179,29,0.3); padding-bottom: 0.5rem;">
                <i class="fas fa-users me-2"></i>Técnicos Vinculados
              </h3>
              ${techLinks.length > 0 ? `
                <div style="overflow-x: auto; border-radius: 8px; border: 1px solid rgba(244,179,29,0.2);">
                  <table style="width: 100%; background: rgba(255,255,255,0.02); border-collapse: collapse;">
                    <thead>
                      <tr style="background: rgba(244,179,29,0.1); border-bottom: 2px solid rgba(244,179,29,0.3);">
                        <th style="padding: 1rem; text-align: left; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-user me-2"></i>Técnico
                        </th>
                        <th style="padding: 1rem; text-align: left; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-tools me-2"></i>Especialidade
                        </th>
                        <th style="padding: 1rem; text-align: right; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-cog me-2"></i>Ações
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      ${techLinks.map(t => {
                        const techName = this.getTechnicianName(t);
                        const techId = this.getTechnicianId(t);
                        const linkId = t.id || t.link_id || 'unknown';
                        const equipmentType = 'Geral';
                        return `
                          <tr style="border-bottom: 1px solid rgba(244,179,29,0.1); transition: all 0.3s ease;" onmouseover="this.style.background='rgba(244,179,29,0.05)'" onmouseout="this.style.background='transparent'" data-id="${linkId}">
                            <td style="padding: 1rem; color: #fff; font-weight: 500;">
                              ${techName}
                              ${techId ? `<span style="color: rgba(255,255,255,0.5); font-size: 0.85rem;">(ID: ${techId})</span>` : ''}
                            </td>
                            <td style="padding: 1rem; color: rgba(255,255,255,0.7);">${equipmentType}</td>
                            <td style="padding: 1rem; text-align: right;">
                              <button class="delete-link-btn" data-link-type="tecnicos" data-item-id="${techId || linkId}" title="Remover vínculo"
                                style="background: none; border: none; color: #ED1C24; font-size: 1.1rem; cursor: pointer; padding: 0.5rem; border-radius: 50%; transition: all 0.3s ease;"
                                onmouseover="this.style.background='rgba(237,28,36,0.1)'; this.style.transform='scale(1.1)'"
                                onmouseout="this.style.background='none'; this.style.transform='scale(1)'">
                                <i class="fas fa-trash-alt"></i>
                              </button>
                            </td>
                          </tr>
                        `;
                      }).join('')}
                    </tbody>
                  </table>
                </div>
              ` : `
                <div style="text-align: center; padding: 3rem; color: rgba(255,255,255,0.5);">
                  <i class="fas fa-user-slash" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(244,179,29,0.3);"></i>
                  <p style="font-size: 1.1rem; margin: 0;">Nenhum técnico vinculado a esta filial.</p>
                  <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0; opacity: 0.7;">Clique em "Novo Vínculo" para adicionar técnicos.</p>
                </div>
              `}
            </div>
            <div id="tab-content-prestadoras" class="tab-content hidden">
              <h3 style="color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.3rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 1rem; border-bottom: 2px solid rgba(244,179,29,0.3); padding-bottom: 0.5rem;">
                <i class="fas fa-building me-2"></i>Prestadoras Vinculadas
              </h3>
              ${providerLinks.length > 0 ? `
                <div style="overflow-x: auto; border-radius: 8px; border: 1px solid rgba(244,179,29,0.2);">
                  <table style="width: 100%; background: rgba(255,255,255,0.02); border-collapse: collapse;">
                    <thead>
                      <tr style="background: rgba(244,179,29,0.1); border-bottom: 2px solid rgba(244,179,29,0.3);">
                        <th style="padding: 1rem; text-align: left; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-building me-2"></i>Prestadora
                        </th>
                        <th style="padding: 1rem; text-align: left; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-envelope me-2"></i>Contato
                        </th>
                        <th style="padding: 1rem; text-align: right; color: #f4b31d; font-family: 'Rajdhani', sans-serif; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                          <i class="fas fa-cog me-2"></i>Ações
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      ${providerLinks.map(p => {
                        const providerName = this.getProviderName(p);
                        const providerId = this.getProviderId(p);
                        const linkId = p.id || p.link_id || 'unknown';
                        const providerEmail = (p.Provider && p.Provider.email) || p.provider_email || 'N/A';
                        return `
                          <tr style="border-bottom: 1px solid rgba(244,179,29,0.1); transition: all 0.3s ease;" onmouseover="this.style.background='rgba(244,179,29,0.05)'" onmouseout="this.style.background='transparent'" data-id="${linkId}">
                            <td style="padding: 1rem; color: #fff; font-weight: 500;">
                              ${providerName}
                              ${providerId ? `<span style="color: rgba(255,255,255,0.5); font-size: 0.85rem;">(ID: ${providerId})</span>` : ''}
                            </td>
                            <td style="padding: 1rem; color: rgba(255,255,255,0.7);">${providerEmail}</td>
                            <td style="padding: 1rem; text-align: right;">
                              <button class="delete-link-btn" data-link-type="prestadoras" data-item-id="${providerId || linkId}" title="Remover vínculo"
                                style="background: none; border: none; color: #ED1C24; font-size: 1.1rem; cursor: pointer; padding: 0.5rem; border-radius: 50%; transition: all 0.3s ease;"
                                onmouseover="this.style.background='rgba(237,28,36,0.1)'; this.style.transform='scale(1.1)'"
                                onmouseout="this.style.background='none'; this.style.transform='scale(1)'">
                                <i class="fas fa-trash-alt"></i>
                              </button>
                            </td>
                          </tr>
                        `;
                      }).join('')}
                    </tbody>
                  </table>
                </div>
              ` : `
                <div style="text-align: center; padding: 3rem; color: rgba(255,255,255,0.5);">
                  <i class="fas fa-building-slash" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(244,179,29,0.3);"></i>
                  <p style="font-size: 1.1rem; margin: 0;">Nenhuma prestadora vinculada a esta filial.</p>
                  <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0; opacity: 0.7;">Clique em "Novo Vínculo" para adicionar prestadoras.</p>
                </div>
              `}
            </div>
          </div>
        </div>
      </div>
    `;
    modalContent.innerHTML = html;
    document.getElementById('details-modal').classList.add('is-visible');
    // Fechar modal
    modalContent.querySelector('.close-modal-btn').addEventListener('click', () => document.getElementById('details-modal').classList.remove('is-visible'));
    // Tabs
    const tabButtons = modalContent.querySelectorAll('.tab-button');
    const tabContents = modalContent.querySelectorAll('.tab-content');
    let currentTab = 'tecnicos';
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        tabButtons.forEach(btn => {
          btn.classList.remove('active-tab');
          btn.style.color = 'rgba(255,255,255,0.6)';
          btn.style.borderBottom = '3px solid transparent';
        });
        button.classList.add('active-tab');
        button.style.color = '#f4b31d';
        button.style.borderBottom = '3px solid #f4b31d';
        tabContents.forEach(content => content.classList.add('hidden'));
        modalContent.querySelector(`#tab-content-${button.dataset.tab}`).classList.remove('hidden');
        currentTab = button.dataset.tab;
      });
    });
    // Botão Novo Vínculo
    modalContent.querySelector('#btnNovoVinculo').addEventListener('click', async () => {
      // Modal flutuante customizado
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.inset = '0';
      overlay.style.background = 'rgba(0,0,0,0.7)';
      overlay.style.zIndex = '99999';
      overlay.style.display = 'flex';
      overlay.style.alignItems = 'center';
      overlay.style.justifyContent = 'center';
      overlay.style.padding = '1rem';
      // Conteúdo do modal
      const modal = document.createElement('div');
      modal.style.background = '#2d323b';
      modal.style.borderRadius = '18px';
      modal.style.boxShadow = '0 8px 32px 0 rgba(0,0,0,0.37)';
      modal.style.padding = '2rem 1.5rem 1.5rem 1.5rem';
      modal.style.maxWidth = '400px';
      modal.style.width = '100%';
      modal.style.position = 'relative';
      modal.innerHTML = `<h3 style="color: #FDB813; font-size: 1.1rem; font-weight: bold; margin-bottom: 1.2rem; text-align:center;">Vincular ${currentTab === 'tecnicos' ? 'Técnico' : 'Prestadora'}</h3><div id="vinculo-list" style="margin-bottom: 1.5rem;"></div><div class="flex justify-end mt-4" style="display:flex; justify-content: flex-end;"><button id="btnSalvarVinculo" class="btn btn-shell" style="background: linear-gradient(90deg, #FDB813 0%, #ED1C24 100%); color: #232a34; border-radius: 10px; font-weight: bold; padding: 0.5em 1.5em;">Salvar</button></div><button id="btnFecharVinculo" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: #fff; font-size: 1.2rem;"><i class="fas fa-times"></i></button>`;
      overlay.appendChild(modal);
      document.body.appendChild(overlay);
      // Listar técnicos/prestadoras disponíveis
      let items = [];
      if (currentTab === 'tecnicos') {
        items = await this.getTechniciansToLink(techLinks);
      } else {
        items = await this.getProvidersToLink(providerLinks);
      }
      const listDiv = modal.querySelector('#vinculo-list');
      if (items.length === 0) {
        listDiv.innerHTML = `<p style="color: #ccc; text-align: center;">Nenhum ${currentTab === 'tecnicos' ? 'técnico' : 'prestadora'} disponível para vincular.</p>`;
        modal.querySelector('#btnSalvarVinculo').disabled = true;
        modal.querySelector('#btnSalvarVinculo').style.opacity = '0.5';
      } else {
        // Caixa de seleção única estilizada
        listDiv.innerHTML = `<select id="selectVinculo" style="width:100%; padding: 0.7rem 1rem; border-radius: 8px; border: 1.5px solid #FDB813; background: #232a34; color: #fff; font-size: 1rem; margin-bottom: 0.5rem;">${items.map(item => `<option value="${item.id}">${item.name}</option>`).join('')}</select>`;
        modal.querySelector('#btnSalvarVinculo').disabled = false;
        modal.querySelector('#btnSalvarVinculo').style.opacity = '1';
      }
      // Fechar modal
      modal.querySelector('#btnFecharVinculo').onclick = () => document.body.removeChild(overlay);
      overlay.onclick = e => { if (e.target === overlay) document.body.removeChild(overlay); };
      // Salvar vínculo
      modal.querySelector('#btnSalvarVinculo').onclick = async () => {
        const select = modal.querySelector('#selectVinculo');
        if (!select || !select.value) return;
        if (currentTab === 'tecnicos') {
          await this.vincularTecnico(branch.id, select.value);
        } else {
          await this.vincularPrestadora(branch.id, select.value);
        }
        document.body.removeChild(overlay);
        // Aguardar um pouco para garantir que o vínculo foi processado no backend
        setTimeout(() => {
          this.showBranchDetailsModal(branch);
        }, 500);
      };
    });

    // Event listeners para botões de excluir vínculos
    modalContent.querySelectorAll('.delete-link-btn').forEach(button => {
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        const linkType = button.getAttribute('data-link-type');
        const itemId = button.getAttribute('data-item-id');

        if (confirm(`Tem certeza que deseja remover este vínculo?`)) {
          try {
            if (linkType === 'tecnicos') {
              await fetch(`/api/novo-link-management/links/technician-branch/${itemId}/${branchId}`, {
                method: 'DELETE',
                credentials: 'include'
              });
            } else if (linkType === 'prestadoras') {
              await fetch(`/api/novo-link-management/links/provider-branch/${itemId}/${branchId}`, {
                method: 'DELETE',
                credentials: 'include'
              });
            }

            this.showToast('Vínculo removido com sucesso!', 'success');
            // Recarregar modal
            setTimeout(() => {
              this.showBranchDetailsModal(branch);
            }, 500);

          } catch (error) {
            console.error('Erro ao remover vínculo:', error);
            this.showToast('Erro ao remover vínculo', 'error');
          }
        }
      });
    });

    } catch (error) {
      console.error('[MODAL-ERROR] Erro ao carregar vínculos da filial:', error);
      this.showToast('Erro ao carregar vínculos da filial', 'error');
      
      // Renderizar modal básico mesmo com erro
      const branchId = this.getBranchId(branch);
      const branchManager = this.getBranchResponsible(branch);
      
      modalContent.innerHTML = `
        <div class="modal-header" style="background: linear-gradient(135deg, #ED1C24 0%, #da291c 100%); color: #fff; padding: 1.5rem; border-radius: 10px 10px 0 0; display: flex; justify-content: space-between; align-items: center;">
          <div style="flex: 1;">
            <h2 style="margin: 0; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1.75rem; text-transform: uppercase; letter-spacing: 1px;">
              <i class="fas fa-exclamation-triangle me-2" style="color: #f4b31d;"></i>Erro - Filial ${branchId}
            </h2>
            <p style="margin: 0.5rem 0 0 0; font-size: 0.95rem; opacity: 0.8;">Não foi possível carregar os dados da filial</p>
            <p style="margin: 0.25rem 0 0 0; font-size: 0.9rem; opacity: 0.7;">
              <i class="fas fa-user-tie me-1"></i>Responsável: <strong>${branchManager}</strong>
            </p>
          </div>
          <button class="close-modal-btn" style="background: none; border: none; color: #f4b31d; font-size: 1.5rem; cursor: pointer; padding: 0.5rem; border-radius: 50%; transition: all 0.3s ease;">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body" style="padding: 3rem; text-align: center; background: rgba(255,255,255,0.02); border-radius: 0 0 10px 10px;">
          <div style="color: rgba(255,255,255,0.7);">
            <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 1.5rem; color: #ED1C24;"></i>
            <p style="font-size: 1.2rem; margin-bottom: 1rem; color: #fff;">Erro ao carregar vínculos da filial</p>
            <p style="font-size: 1rem; margin-bottom: 2rem; opacity: 0.7;">Ocorreu um problema ao buscar os dados. Tente recarregar a página.</p>
            <button onclick="location.reload()" style="background: linear-gradient(135deg, #f4b31d 0%, #ED1C24 100%); color: #1a1a1a; border: none; border-radius: 10px; padding: 1rem 2rem; font-family: 'Rajdhani', sans-serif; font-weight: 700; font-size: 1rem; text-transform: uppercase; letter-spacing: 1px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(244, 179, 29, 0.3);">
              <i class="fas fa-sync-alt me-2"></i>Recarregar Página
            </button>
          </div>
        </div>
      `;
      
      document.getElementById('details-modal').classList.add('is-visible');
      modalContent.querySelector('.close-modal-btn').addEventListener('click', () => 
        document.getElementById('details-modal').classList.remove('is-visible')
      );
    }
  }

  // Método auxiliar para extrair dados de vínculos com fallbacks robustos
  extractLinksData(responseData, linkType) {
    if (!responseData || typeof responseData !== 'object') {
      console.warn(`[EXTRACT-LINKS-WARN] Dados inválidos para vínculos de ${linkType}:`, responseData);
      return [];
    }

    // Verificar se a resposta indica sucesso
    if (responseData.success === false) {
      console.warn(`[EXTRACT-LINKS-WARN] API retornou erro para vínculos de ${linkType}:`, responseData.message || 'Erro desconhecido');
      return [];
    }

    // Tentar extrair dados usando diferentes estruturas possíveis
    const possibleKeys = ['data', 'links', 'items', 'results'];
    
    for (const key of possibleKeys) {
      if (responseData[key] && Array.isArray(responseData[key])) {
        console.log(`[EXTRACT-LINKS-DEBUG] Vínculos de ${linkType} extraídos usando chave '${key}':`, responseData[key].length, 'itens');
        return responseData[key];
      }
    }

    // Se não encontrou em nenhuma chave, verificar se o próprio objeto é um array
    if (Array.isArray(responseData)) {
      console.log(`[EXTRACT-LINKS-DEBUG] Vínculos de ${linkType} extraídos diretamente como array:`, responseData.length, 'itens');
      return responseData;
    }

    console.warn(`[EXTRACT-LINKS-WARN] Não foi possível extrair vínculos de ${linkType}. Estrutura recebida:`, Object.keys(responseData));
    return [];
  }

  // Métodos auxiliares para extração robusta de dados de técnicos
  getTechnicianName(techLink) {
    return (techLink.Technician && techLink.Technician.name) ||
           techLink.technician_name ||
           techLink.name ||
           techLink.nome ||
           'Nome não disponível';
  }

  getTechnicianId(techLink) {
    return (techLink.Technician && techLink.Technician.id) ||
           techLink.technician_id ||
           techLink.id ||
           null;
  }

  // Métodos auxiliares para extração robusta de dados de prestadoras
  getProviderName(providerLink) {
    return (providerLink.Provider && providerLink.Provider.name) ||
           providerLink.provider_name ||
           providerLink.name ||
           providerLink.nome ||
           'Nome não disponível';
  }

  getProviderId(providerLink) {
    return (providerLink.Provider && providerLink.Provider.id) ||
           providerLink.provider_id ||
           providerLink.id ||
           null;
  }

  async novoVinculoModal(branch, tipo) {
    const modalContent = document.getElementById('details-modal-content');
    let options = '';
    if (tipo === 'tecnico') {
      options = this.technicians.map(t => `<option value="${t.id}">${t.name}</option>`).join('');
    } else {
      options = this.providers.map(p => `<option value="${p.id}">${p.name}</option>`).join('');
    }
    modalContent.innerHTML = `
      <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h2 style="font-size: 1.5rem; font-weight: 700;">Novo Vínculo ${tipo === 'tecnico' ? 'Técnico' : 'Prestadora'} - Filial ${branch.id}</h2>
        <button class="close-modal-btn" style="color: rgba(255,255,255,0.7); background: none; border: none; cursor: pointer; font-size: 1.25rem;"><i class="fas fa-times"></i></button>
      </div>
      <div class="card-body">
        <form id="formNovoVinculo">
          <label class="form-label">${tipo === 'tecnico' ? 'Técnico' : 'Prestadora'}</label>
          <select class="form-select" id="selectNovoVinculo" required>${options}</select>
          <button type="submit" class="btn btn-success mt-3">Salvar Vínculo</button>
        </form>
      </div>
    `;
    modalContent.querySelector('.close-modal-btn').addEventListener('click', () => document.getElementById('details-modal').classList.remove('is-visible'));
    modalContent.querySelector('#formNovoVinculo').addEventListener('submit', async (e) => {
      e.preventDefault();
      const id = modalContent.querySelector('#selectNovoVinculo').value;
      if (!id) return;
      if (tipo === 'tecnico') {
        // Vincular técnico (pega primeiro tipo de equipamento disponível)
        await fetch('/api/novo-link-management/links/technician-branch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ technician_id: Number(id), branch_id: branch.id })
        });
      } else {
        await fetch('/api/novo-link-management/links/provider-branch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ provider_id: Number(id), branch_id: branch.id })
        });
      }
      this.showToast('Vínculo salvo com sucesso!', 'success');
      // Reabrir modal de detalhes atualizado
      await this.showBranchDetailsModal(branch);
    });
  }

  async getTechniciansToLink(existingLinks) {
    try {
      // Verificação robusta de existingLinks
      if (!Array.isArray(existingLinks)) {
        console.warn('[GET-TECHS-WARN] existingLinks não é um array:', existingLinks);
        existingLinks = [];
      }

      // Extrair IDs dos técnicos já vinculados com verificação robusta
      let linkedIds = [];
      existingLinks.forEach((link, index) => {
        try {
          const techId = this.getTechnicianId(link);
          if (techId) {
            linkedIds.push(techId);
          }
        } catch (error) {
          console.warn(`[GET-TECHS-WARN] Erro ao extrair ID do técnico no índice ${index}:`, error, link);
        }
      });

      console.log('[GET-TECHS-DEBUG] IDs de técnicos já vinculados:', linkedIds);

      // Filtrar técnicos disponíveis
      const availableTechnicians = this.technicians.filter(tech => {
        if (!tech) return false;
        const techId = tech.id || tech.technician_id;
        return techId && !linkedIds.includes(techId);
      }).map(tech => ({
        id: tech.id || tech.technician_id,
        name: tech.name || tech.nome || `Técnico ${tech.id || 'N/A'}`
      }));

      console.log(`[GET-TECHS-DEBUG] ${availableTechnicians.length} técnicos disponíveis para vincular`);
      return availableTechnicians;

    } catch (error) {
      console.error('[GET-TECHS-ERROR] Erro ao obter técnicos para vincular:', error);
      return [];
    }
  }

  async getProvidersToLink(existingLinks) {
    try {
      // Verificação robusta de existingLinks
      if (!Array.isArray(existingLinks)) {
        console.warn('[GET-PROVIDERS-WARN] existingLinks não é um array:', existingLinks);
        existingLinks = [];
      }

      // Extrair IDs das prestadoras já vinculadas com verificação robusta
      let linkedIds = [];
      existingLinks.forEach((link, index) => {
        try {
          const providerId = this.getProviderId(link);
          if (providerId) {
            linkedIds.push(providerId);
          }
        } catch (error) {
          console.warn(`[GET-PROVIDERS-WARN] Erro ao extrair ID da prestadora no índice ${index}:`, error, link);
        }
      });

      console.log('[GET-PROVIDERS-DEBUG] IDs de prestadoras já vinculadas:', linkedIds);

      // Filtrar prestadoras disponíveis
      const availableProviders = this.providers.filter(provider => {
        if (!provider) return false;
        const providerId = provider.id || provider.provider_id;
        return providerId && !linkedIds.includes(providerId);
      }).map(provider => ({
        id: provider.id || provider.provider_id,
        name: provider.name || provider.nome || `Prestadora ${provider.id || 'N/A'}`
      }));

      console.log(`[GET-PROVIDERS-DEBUG] ${availableProviders.length} prestadoras disponíveis para vincular`);
      return availableProviders;

    } catch (error) {
      console.error('[GET-PROVIDERS-ERROR] Erro ao obter prestadoras para vincular:', error);
      return [];
    }
  }

  async vincularTecnico(branchId, technicianId) {
    try {
      // Validação robusta dos parâmetros
      const validBranchId = Number(branchId);
      const validTechnicianId = Number(technicianId);

      if (!validBranchId || isNaN(validBranchId)) {
        console.error('[VINCULO-ERROR] ID da filial inválido:', branchId);
        this.showToast('Erro: ID da filial inválido', 'error');
        return;
      }

      if (!validTechnicianId || isNaN(validTechnicianId)) {
        console.error('[VINCULO-ERROR] ID do técnico inválido:', technicianId);
        this.showToast('Erro: ID do técnico inválido', 'error');
        return;
      }

      console.log(`[VINCULO] Vinculando técnico ${validTechnicianId} à filial ${validBranchId}`);

      const response = await fetch('/api/novo-link-management/links/technician-branch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          technician_id: validTechnicianId,
          branch_id: validBranchId
          // Removido equipment_type_id - não é obrigatório para vínculo simples técnico-filial
        })
      });

      if (!response.ok) {
        console.error('[VINCULO-ERROR] Erro na resposta da API:', response.status, response.statusText);
        this.showToast(`Erro na API: ${response.status} ${response.statusText}`, 'error');
        return;
      }

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.error('[VINCULO-ERROR] Erro ao parsear resposta JSON:', parseError);
        this.showToast('Erro ao processar resposta da API', 'error');
        return;
      }

      console.log('[VINCULO-DEBUG] Resposta da API:', result);

      if (result.success) {
        this.showToast('Técnico vinculado com sucesso!', 'success');
      } else {
        const errorMessage = result.message || result.error || 'Erro desconhecido ao vincular técnico';
        console.error('[VINCULO-ERROR] Erro retornado pela API:', errorMessage);
        this.showToast(errorMessage, 'error');
      }
    } catch (e) {
      console.error('[VINCULO-ERROR] Erro ao vincular técnico:', e);
      this.showToast('Erro de conexão ao vincular técnico.', 'error');
    }
  }

  async vincularPrestadora(branchId, providerId) {
    try {
      // Validação robusta dos parâmetros
      const validBranchId = Number(branchId);
      const validProviderId = Number(providerId);

      if (!validBranchId || isNaN(validBranchId)) {
        console.error('[VINCULO-ERROR] ID da filial inválido:', branchId);
        this.showToast('Erro: ID da filial inválido', 'error');
        return;
      }

      if (!validProviderId || isNaN(validProviderId)) {
        console.error('[VINCULO-ERROR] ID da prestadora inválido:', providerId);
        this.showToast('Erro: ID da prestadora inválido', 'error');
        return;
      }

      console.log(`[VINCULO] Vinculando prestadora ${validProviderId} à filial ${validBranchId}`);

      const response = await fetch('/api/novo-link-management/links/provider-branch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ 
          provider_id: validProviderId, 
          branch_id: validBranchId 
        })
      });

      if (!response.ok) {
        console.error('[VINCULO-ERROR] Erro na resposta da API:', response.status, response.statusText);
        this.showToast(`Erro na API: ${response.status} ${response.statusText}`, 'error');
        return;
      }

      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.error('[VINCULO-ERROR] Erro ao parsear resposta JSON:', parseError);
        this.showToast('Erro ao processar resposta da API', 'error');
        return;
      }

      console.log('[VINCULO-DEBUG] Resposta da API:', result);

      if (result.success) {
        this.showToast('Prestadora vinculada com sucesso!', 'success');
      } else {
        const errorMessage = result.message || result.error || 'Erro desconhecido ao vincular prestadora';
        console.error('[VINCULO-ERROR] Erro retornado pela API:', errorMessage);
        this.showToast(errorMessage, 'error');
      }
    } catch (e) {
      console.error('[VINCULO-ERROR] Erro ao vincular prestadora:', e);
      this.showToast('Erro de conexão ao vincular prestadora.', 'error');
    }
  }

  showToast(message, type = "info") {
    try {
      const toast = document.getElementById("toast");
      if (!toast) {
        console.warn('[TOAST-WARN] Elemento toast não encontrado, usando console.log');
        console.log(`[TOAST-${type.toUpperCase()}] ${message}`);
        return;
      }

      // Validação robusta dos parâmetros
      const validMessage = message || 'Mensagem não especificada';
      const validType = ['info', 'success', 'warning', 'error'].includes(type) ? type : 'info';

      console.log(`[TOAST-${validType.toUpperCase()}] ${validMessage}`);

      toast.textContent = validMessage;
      toast.className = `toast ${validType}`;
      toast.classList.add("show");
      
      setTimeout(() => { 
        if (toast.classList.contains("show")) {
          toast.classList.remove("show"); 
        }
      }, 3000);
    } catch (error) {
      console.error('[TOAST-ERROR] Erro ao exibir toast:', error);
      console.log(`[FALLBACK-MESSAGE] ${message}`);
    }
  }
}

document.addEventListener("DOMContentLoaded", () => {
  window.novoLinkManagement = new NovoLinkManagement();

  // Debug: Verificar se os elementos existem
  console.log('[DEBUG] Verificando elementos do modal de convite...');

  // Botão Convidar Técnico
  const btnConvidarTecnico = document.getElementById("btnConvidarTecnico");
  const modalElement = document.getElementById("modalConvidarTecnico");
  const formConvidarTecnico = document.getElementById("formConvidarTecnico");
  const conviteAlertBox = document.getElementById("conviteAlertBox");
  const conviteLinkBox = document.getElementById("conviteLinkBox");
  const conviteEmail = document.getElementById("conviteEmail");

  console.log('[DEBUG] Elementos encontrados:', {
    btnConvidarTecnico: !!btnConvidarTecnico,
    modalElement: !!modalElement,
    formConvidarTecnico: !!formConvidarTecnico,
    conviteAlertBox: !!conviteAlertBox,
    conviteLinkBox: !!conviteLinkBox,
    conviteEmail: !!conviteEmail,
    bootstrap: typeof bootstrap !== 'undefined'
  });

  const modalConvidarTecnico = modalElement ? new bootstrap.Modal(modalElement) : null;
  if (btnConvidarTecnico && modalConvidarTecnico) {
    btnConvidarTecnico.addEventListener("click", () => {
      console.log('[DEBUG] Botão Convidar Técnico clicado');
      if (conviteAlertBox) conviteAlertBox.innerHTML = "";
      if (conviteLinkBox) {
        conviteLinkBox.style.display = "none";
        conviteLinkBox.innerHTML = "";
      }
      if (conviteEmail) conviteEmail.value = "";
      if (formConvidarTecnico) formConvidarTecnico.reset();
      modalConvidarTecnico.show();
    });
  } else {
    console.error('[ERROR] Botão Convidar Técnico ou modal não encontrado');
  }
  if (formConvidarTecnico) {
    formConvidarTecnico.addEventListener("submit", function (e) {
      e.preventDefault();
      console.log('[DEBUG] Formulário de convite submetido');

      if (conviteAlertBox) conviteAlertBox.innerHTML = "";
      if (conviteLinkBox) {
        conviteLinkBox.style.display = "none";
        conviteLinkBox.innerHTML = "";
      }

      const email = conviteEmail ? conviteEmail.value.trim() : "";
      if (!email) {
        showConviteAlert("Informe um e-mail válido.", "danger");
        return;
      }

      showConviteAlert("Enviando convite...", "info");

      fetch("/api/invites/technician", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest"
        },
        credentials: "include",
        body: JSON.stringify({ email }),
      })
        .then((resp) => {
          console.log('[DEBUG] Resposta do servidor:', resp.status, resp.statusText);
          return resp.json();
        })
        .then((data) => {
          console.log('[DEBUG] Dados recebidos:', data);
          if (data.success && data.data && data.data.token) {
            showConviteAlert("Convite enviado com sucesso!", "success");
            if (conviteLinkBox) {
              conviteLinkBox.style.display = "block";
              const link = `${window.location.origin}/register/technician?token=${data.data.token}`;
              conviteLinkBox.innerHTML = `<div class='alert alert-success'><b>Link de cadastro:</b><br><a href='${link}' target='_blank'>${link}</a></div>`;
            }
          } else {
            showConviteAlert(data.message || "Erro ao enviar convite.", "danger");
          }
        })
        .catch((error) => {
          console.error('[ERROR] Erro ao enviar convite:', error);
          showConviteAlert("Erro ao enviar convite. Tente novamente.", "danger");
        });
    });
  } else {
    console.error('[ERROR] Formulário de convite não encontrado');
  }
  function showConviteAlert(msg, type) {
    if (conviteAlertBox) {
      conviteAlertBox.innerHTML = `<div class="alert alert-${type} text-center">${msg}</div>`;
    } else {
      console.error('[ERROR] conviteAlertBox não encontrado');
    }
  }
  // Botão Convidar Prestadora
  const btnConvidarPrestadora = document.getElementById("btnConvidarPrestadora");
  const modalConvidarPrestadora = new bootstrap.Modal(document.getElementById("modalConvidarPrestadora"));
  const formConvidarPrestadora = document.getElementById("formConvidarPrestadora");
  const convitePrestadoraAlertBox = document.getElementById("convitePrestadoraAlertBox");
  const convitePrestadoraLinkBox = document.getElementById("convitePrestadoraLinkBox");
  const convitePrestadoraEmail = document.getElementById("convitePrestadoraEmail");
  if (btnConvidarPrestadora) {
    btnConvidarPrestadora.addEventListener("click", () => {
      convitePrestadoraAlertBox.innerHTML = "";
      convitePrestadoraLinkBox.style.display = "none";
      convitePrestadoraLinkBox.innerHTML = "";
      convitePrestadoraEmail.value = "";
      formConvidarPrestadora.reset();
      modalConvidarPrestadora.show();
    });
  }
  if (formConvidarPrestadora) {
    formConvidarPrestadora.addEventListener("submit", function (e) {
      e.preventDefault();
      convitePrestadoraAlertBox.innerHTML = "";
      convitePrestadoraLinkBox.style.display = "none";
      convitePrestadoraLinkBox.innerHTML = "";
      const email = convitePrestadoraEmail.value.trim();
      if (!email) {
        convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-danger text-center">Informe um e-mail válido.</div>';
        return;
      }
      fetch(`/api/providers/exists?email=${encodeURIComponent(email)}`)
        .then((resp) => resp.json())
        .then((data) => {
          if (data.exists) {
            convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-danger text-center">Já existe uma prestadora cadastrada com este e-mail.</div>';
            return;
          }
          convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-info text-center">Enviando convite...</div>';
          fetch("/api/provider-invites", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email }),
          })
            .then((resp) => resp.json())
            .then((data) => {
              if (data.success && data.data && data.data.token) {
                convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-success text-center">Convite enviado com sucesso!</div>';
                convitePrestadoraLinkBox.style.display = "block";
                const link = `${window.location.origin}/register/provider?token=${data.data.token}`;
                convitePrestadoraLinkBox.innerHTML = `<div class='alert alert-success'><b>Link de cadastro:</b><br><a href='${link}' target='_blank'>${link}</a></div>`;
              } else {
                convitePrestadoraAlertBox.innerHTML = `<div class='alert alert-danger text-center'>${data.message || "Erro ao enviar convite."}</div>`;
              }
            })
            .catch(() => {
              convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-danger text-center">Erro ao enviar convite. Tente novamente.</div>';
            });
        })
        .catch(() => {
          convitePrestadoraAlertBox.innerHTML = '<div class="alert alert-danger text-center">Erro ao verificar e-mail. Tente novamente.</div>';
        });
    });
  }
});

