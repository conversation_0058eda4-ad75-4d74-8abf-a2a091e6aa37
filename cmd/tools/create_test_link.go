package main

import (
	"fmt"
	"log"
	"os"
	"tradicao/internal/models"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🧪 CRIANDO VÍNCULO DE TESTE")
	fmt.Println("===========================")

	// Buscar técnico (ID 5 - ITALO)
	var technician models.User
	result := db.Where("id = ? AND role IN (?, ?)", 5, "technician", "tecnico").First(&technician)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar técnico: %v", result.Error)
	}
	fmt.Printf("✅ Técnico encontrado: %s (ID: %d)\n", technician.Name, technician.ID)

	// Buscar tipo de equipamento (ID 9 - TurboChef)
	var equipmentType models.EquipmentType
	result = db.Where("id = ?", 9).First(&equipmentType)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar tipo de equipamento: %v", result.Error)
	}
	fmt.Printf("✅ Tipo de equipamento encontrado: %s (ID: %d)\n", equipmentType.Name, equipmentType.ID)

	// Verificar se já existe vínculo
	var existingLink models.TechnicianEquipmentType
	result = db.Where("technician_id = ? AND equipment_type_id = ?", technician.ID, equipmentType.ID).First(&existingLink)
	
	if result.Error == gorm.ErrRecordNotFound {
		// Criar novo vínculo
		newLink := models.TechnicianEquipmentType{
			TechnicianID:    int(technician.ID),
			EquipmentTypeID: int(equipmentType.ID),
			IsActive:        true,
		}
		
		result = db.Create(&newLink)
		if result.Error != nil {
			log.Fatalf("Erro ao criar vínculo: %v", result.Error)
		}
		
		fmt.Printf("✅ Vínculo criado com sucesso! ID: %d\n", newLink.ID)
		fmt.Printf("   - Técnico: %s (ID: %d)\n", technician.Name, technician.ID)
		fmt.Printf("   - Tipo: %s (ID: %d)\n", equipmentType.Name, equipmentType.ID)
		fmt.Printf("   - Ativo: %t\n", newLink.IsActive)
		
	} else if result.Error != nil {
		log.Fatalf("Erro ao verificar vínculo existente: %v", result.Error)
	} else {
		fmt.Printf("⚠️  Vínculo já existe! ID: %d\n", existingLink.ID)
	}

	// Buscar todos os vínculos para verificar
	fmt.Println("\n📋 VÍNCULOS EXISTENTES:")
	var allLinks []models.TechnicianEquipmentType
	result = db.Preload("Technician").Preload("EquipmentType").Find(&allLinks)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar vínculos: %v", result.Error)
	}

	fmt.Printf("✅ Total de vínculos: %d\n", len(allLinks))
	for _, link := range allLinks {
		techName := "Técnico não encontrado"
		if link.Technician != nil {
			techName = link.Technician.Name
		}
		typeName := "Tipo não encontrado"
		if link.EquipmentType != nil {
			typeName = link.EquipmentType.Name
		}
		fmt.Printf("   - ID: %d, Técnico: %s (ID: %d), Tipo: %s (ID: %d), Ativo: %t\n", 
			link.ID, techName, link.TechnicianID, typeName, link.EquipmentTypeID, link.IsActive)
	}

	fmt.Println("\n✅ TESTE CONCLUÍDO!")
}
