package main

import (
	"fmt"
	"log"
	"os"
	"tradicao/internal/models"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🧪 TESTANDO API DE VÍNCULOS TÉCNICO-EQUIPAMENTO")
	fmt.Println("===============================================")

	// 1. Testar busca de vínculos (GET)
	fmt.Println("\n📋 1. TESTANDO BUSCA DE VÍNCULOS:")
	var links []models.TechnicianEquipmentType
	result := db.Preload("Technician").Preload("EquipmentType").Find(&links)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar vínculos: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Busca realizada com sucesso. Vínculos encontrados: %d\n", len(links))
		for _, link := range links {
			techName := "Técnico não encontrado"
			if link.Technician != nil {
				techName = link.Technician.Name
			}
			typeName := "Tipo não encontrado"
			if link.EquipmentType != nil {
				typeName = link.EquipmentType.Name
			}
			fmt.Printf("   - ID: %d, Técnico: %s (ID: %d), Tipo: %s (ID: %d), Ativo: %t\n", 
				link.ID, techName, link.TechnicianID, typeName, link.EquipmentTypeID, link.IsActive)
		}
	}

	// 2. Buscar técnicos disponíveis
	fmt.Println("\n📋 2. TÉCNICOS DISPONÍVEIS:")
	var technicians []models.User
	result = db.Where("role IN (?, ?) AND is_active = true", "technician", "tecnico").Find(&technicians)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar técnicos: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Técnicos encontrados: %d\n", len(technicians))
		for _, tech := range technicians {
			fmt.Printf("   - ID: %d, Nome: %s, Role: %s\n", tech.ID, tech.Name, tech.Role)
		}
	}

	// 3. Buscar tipos de equipamento disponíveis
	fmt.Println("\n📋 3. TIPOS DE EQUIPAMENTO DISPONÍVEIS:")
	var equipmentTypes []models.EquipmentType
	result = db.Find(&equipmentTypes)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar tipos: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Tipos encontrados: %d\n", len(equipmentTypes))
		for i, et := range equipmentTypes {
			if i < 5 { // Mostrar apenas os primeiros 5
				fmt.Printf("   - ID: %d, Nome: %s, Descrição: %s\n", et.ID, et.Name, et.Description)
			}
		}
		if len(equipmentTypes) > 5 {
			fmt.Printf("   ... e mais %d tipos\n", len(equipmentTypes)-5)
		}
	}

	// 4. Testar criação de vínculo (se houver técnicos e tipos)
	if len(technicians) > 0 && len(equipmentTypes) > 0 {
		fmt.Println("\n📋 4. TESTANDO CRIAÇÃO DE VÍNCULO:")
		
		// Verificar se já existe um vínculo
		var existingLink models.TechnicianEquipmentType
		result = db.Where("technician_id = ? AND equipment_type_id = ?", 
			technicians[0].ID, equipmentTypes[0].ID).First(&existingLink)
		
		if result.Error == gorm.ErrRecordNotFound {
			// Criar novo vínculo
			newLink := models.TechnicianEquipmentType{
				TechnicianID:    int(technicians[0].ID),
				EquipmentTypeID: int(equipmentTypes[0].ID),
				IsActive:        true,
			}
			
			result = db.Create(&newLink)
			if result.Error != nil {
				fmt.Printf("❌ Erro ao criar vínculo: %v\n", result.Error)
			} else {
				fmt.Printf("✅ Vínculo criado com sucesso! ID: %d\n", newLink.ID)
				fmt.Printf("   - Técnico: %s (ID: %d)\n", technicians[0].Name, technicians[0].ID)
				fmt.Printf("   - Tipo: %s (ID: %d)\n", equipmentTypes[0].Name, equipmentTypes[0].ID)
				
				// Buscar o vínculo criado com relações
				var createdLink models.TechnicianEquipmentType
				result = db.Preload("Technician").Preload("EquipmentType").First(&createdLink, newLink.ID)
				if result.Error != nil {
					fmt.Printf("❌ Erro ao buscar vínculo criado: %v\n", result.Error)
				} else {
					fmt.Printf("✅ Vínculo verificado com relações carregadas\n")
				}
			}
		} else if result.Error != nil {
			fmt.Printf("❌ Erro ao verificar vínculo existente: %v\n", result.Error)
		} else {
			fmt.Printf("⚠️  Vínculo já existe entre técnico %s e tipo %s\n", 
				technicians[0].Name, equipmentTypes[0].Name)
		}
	} else {
		fmt.Println("\n⚠️  Não há técnicos ou tipos suficientes para testar criação")
	}

	// 5. Testar busca final
	fmt.Println("\n📋 5. BUSCA FINAL DE VÍNCULOS:")
	var finalLinks []models.TechnicianEquipmentType
	result = db.Preload("Technician").Preload("EquipmentType").Find(&finalLinks)
	if result.Error != nil {
		fmt.Printf("❌ Erro na busca final: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Total de vínculos após teste: %d\n", len(finalLinks))
	}

	fmt.Println("\n✅ TESTE CONCLUÍDO!")
	fmt.Println("A API de vínculos técnico-equipamento deve estar funcionando corretamente.")
}
