package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔍 ANÁLISE DETALHADA DA TABELA technician_equipment_type")
	fmt.Println("======================================================")

	// Verificar estrutura da tabela
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
		IsNullable string `gorm:"column:is_nullable"`
	}

	result := db.Raw(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_schema = 'public' AND table_name = 'technician_equipment_type' 
		ORDER BY ordinal_position
	`).Scan(&columns)

	if result.Error != nil {
		log.Fatalf("Erro ao obter estrutura: %v", result.Error)
	}

	fmt.Printf("📊 Estrutura atual da tabela:\n")
	hasIsActive := false
	for _, col := range columns {
		nullable := "NOT NULL"
		if col.IsNullable == "YES" {
			nullable = "NULL"
		}
		fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		if col.ColumnName == "is_active" {
			hasIsActive = true
		}
	}

	if !hasIsActive {
		fmt.Println("\n❌ PROBLEMA IDENTIFICADO: Coluna 'is_active' NÃO EXISTE!")
		fmt.Println("🔧 Será necessário adicionar a coluna...")
		
		// Adicionar a coluna is_active
		fmt.Println("\n🔧 Adicionando coluna is_active...")
		result = db.Exec("ALTER TABLE technician_equipment_type ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true")
		if result.Error != nil {
			log.Fatalf("Erro ao adicionar coluna is_active: %v", result.Error)
		}
		fmt.Println("✅ Coluna is_active adicionada com sucesso!")
		
		// Verificar novamente
		result = db.Raw(`
			SELECT column_name, data_type, is_nullable 
			FROM information_schema.columns 
			WHERE table_schema = 'public' AND table_name = 'technician_equipment_type' 
			ORDER BY ordinal_position
		`).Scan(&columns)

		if result.Error != nil {
			log.Fatalf("Erro ao verificar estrutura atualizada: %v", result.Error)
		}

		fmt.Printf("\n📊 Estrutura atualizada da tabela:\n")
		for _, col := range columns {
			nullable := "NOT NULL"
			if col.IsNullable == "YES" {
				nullable = "NULL"
			}
			fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		}
	} else {
		fmt.Println("\n✅ Coluna 'is_active' já existe!")
	}

	// Verificar se há outras colunas necessárias
	fmt.Println("\n🔍 Verificando outras colunas necessárias...")
	
	requiredColumns := []string{"created_at", "updated_at"}

	for _, colName := range requiredColumns {
		found := false
		for _, col := range columns {
			if col.ColumnName == colName {
				found = true
				break
			}
		}
		
		if !found {
			fmt.Printf("⚠️  Coluna '%s' não encontrada. Adicionando...\n", colName)
			var sql string
			if colName == "created_at" {
				sql = "ALTER TABLE technician_equipment_type ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
			} else if colName == "updated_at" {
				sql = "ALTER TABLE technician_equipment_type ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
			}
			
			result = db.Exec(sql)
			if result.Error != nil {
				fmt.Printf("❌ Erro ao adicionar coluna %s: %v\n", colName, result.Error)
			} else {
				fmt.Printf("✅ Coluna %s adicionada com sucesso!\n", colName)
			}
		} else {
			fmt.Printf("✅ Coluna '%s' já existe\n", colName)
		}
	}

	fmt.Println("\n✅ ANÁLISE CONCLUÍDA!")
}
