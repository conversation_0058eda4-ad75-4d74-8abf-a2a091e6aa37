package main

import (
	"fmt"
	"log"
	"os"
	"tradicao/internal/models"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔧 TESTANDO CORREÇÃO DO MODELO TechnicianEquipmentType")
	fmt.Println("===================================================")

	// Testar se o modelo está usando a tabela correta
	model := models.TechnicianEquipmentType{}
	tableName := model.TableName()
	fmt.Printf("✅ Nome da tabela no modelo: %s\n", tableName)

	// Verificar se a tabela existe
	var exists bool
	result := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = ?)", tableName).Scan(&exists)
	if result.Error != nil {
		log.Fatalf("Erro ao verificar tabela: %v", result.Error)
	}

	if exists {
		fmt.Printf("✅ Tabela %s EXISTE no banco de dados\n", tableName)
	} else {
		fmt.Printf("❌ Tabela %s NÃO EXISTE no banco de dados\n", tableName)
		return
	}

	// Testar operações CRUD básicas
	fmt.Println("\n🔍 TESTANDO OPERAÇÕES CRUD...")

	// 1. Buscar todos os registros
	var links []models.TechnicianEquipmentType
	result = db.Find(&links)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar registros: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Busca realizada com sucesso. Registros encontrados: %d\n", len(links))
	}

	// 2. Testar busca com preload (se houver relacionamentos)
	var linksWithRelations []models.TechnicianEquipmentType
	result = db.Preload("Technician").Preload("EquipmentType").Find(&linksWithRelations)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar com preload: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Busca com preload realizada com sucesso. Registros: %d\n", len(linksWithRelations))
	}

	// 3. Verificar estrutura da tabela
	fmt.Println("\n📊 ESTRUTURA DA TABELA:")
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
		IsNullable string `gorm:"column:is_nullable"`
	}

	result = db.Raw(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_schema = 'public' AND table_name = ? 
		ORDER BY ordinal_position
	`, tableName).Scan(&columns)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao obter estrutura: %v\n", result.Error)
	} else {
		for _, col := range columns {
			nullable := "NOT NULL"
			if col.IsNullable == "YES" {
				nullable = "NULL"
			}
			fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		}
	}

	// 4. Verificar se há dados de teste
	fmt.Println("\n📈 DADOS EXISTENTES:")
	for i, link := range links {
		fmt.Printf("   %d. TechnicianID: %d, EquipmentTypeID: %d, Active: %t\n", 
			i+1, link.TechnicianID, link.EquipmentTypeID, link.IsActive)
	}

	fmt.Println("\n✅ TESTE CONCLUÍDO COM SUCESSO!")
	fmt.Println("A correção do modelo TechnicianEquipmentType está funcionando corretamente.")
}
