package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔍 ANÁLISE DA TABELA USERS")
	fmt.Println("=========================")

	// Verificar estrutura da tabela users
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
		IsNullable string `gorm:"column:is_nullable"`
	}

	result := db.Raw(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_schema = 'public' AND table_name = 'users' 
		ORDER BY ordinal_position
	`).Scan(&columns)

	if result.Error != nil {
		log.Fatalf("Erro ao obter estrutura: %v", result.Error)
	}

	fmt.Printf("📊 Estrutura da tabela users:\n")
	hasIsActive := false
	for _, col := range columns {
		nullable := "NOT NULL"
		if col.IsNullable == "YES" {
			nullable = "NULL"
		}
		fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		if col.ColumnName == "is_active" {
			hasIsActive = true
		}
	}

	if !hasIsActive {
		fmt.Println("\n❌ Coluna 'is_active' NÃO EXISTE na tabela users")
	} else {
		fmt.Println("\n✅ Coluna 'is_active' existe na tabela users")
	}

	// Buscar técnicos sem usar is_active
	fmt.Println("\n📋 TÉCNICOS NO SISTEMA:")
	var technicians []struct {
		ID   int    `gorm:"column:id"`
		Name string `gorm:"column:name"`
		Role string `gorm:"column:role"`
	}

	result = db.Raw("SELECT id, name, role FROM users WHERE role IN ('technician', 'tecnico') AND deleted_at IS NULL").Scan(&technicians)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar técnicos: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Técnicos encontrados: %d\n", len(technicians))
		for _, tech := range technicians {
			fmt.Printf("   - ID: %d, Nome: %s, Role: %s\n", tech.ID, tech.Name, tech.Role)
		}
	}

	fmt.Println("\n✅ ANÁLISE CONCLUÍDA!")
}
