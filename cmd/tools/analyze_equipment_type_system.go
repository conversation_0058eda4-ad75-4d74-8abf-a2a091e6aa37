package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔍 ANÁLISE DO SISTEMA DE TIPOS DE EQUIPAMENTO")
	fmt.Println("============================================")

	// 1. Verificar tabela equipment_types
	fmt.Println("\n📋 1. TABELA equipment_types:")
	var equipmentTypes []struct {
		ID          int    `gorm:"column:id"`
		Name        string `gorm:"column:name"`
		Description string `gorm:"column:description"`
	}
	
	result := db.Raw("SELECT id, name, description FROM equipment_types ORDER BY name").Scan(&equipmentTypes)
	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar tipos de equipamento: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Encontrados %d tipos de equipamento:\n", len(equipmentTypes))
		for _, et := range equipmentTypes {
			fmt.Printf("   - ID: %d, Nome: %s, Descrição: %s\n", et.ID, et.Name, et.Description)
		}
	}

	// 2. Verificar como equipamentos se relacionam com tipos
	fmt.Println("\n📋 2. RELAÇÃO EQUIPAMENTOS x TIPOS:")
	var equipmentTypeRelations []struct {
		EquipmentID     int    `gorm:"column:equipment_id"`
		EquipmentName   string `gorm:"column:equipment_name"`
		EquipmentType   string `gorm:"column:equipment_type"`
		EquipmentTypeID *int   `gorm:"column:equipment_type_id"`
		TypeName        *string `gorm:"column:type_name"`
	}

	result = db.Raw(`
		SELECT 
			e.id as equipment_id,
			COALESCE(e.name, e.serial_number, 'Sem nome') as equipment_name,
			e.type as equipment_type,
			e.equipment_type_id,
			et.name as type_name
		FROM equipment e
		LEFT JOIN equipment_types et ON e.equipment_type_id = et.id
		ORDER BY e.id
		LIMIT 10
	`).Scan(&equipmentTypeRelations)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar relações: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Primeiros 10 equipamentos e suas relações:\n")
		for _, rel := range equipmentTypeRelations {
			typeInfo := "Sem tipo definido"
			if rel.EquipmentTypeID != nil && rel.TypeName != nil {
				typeInfo = fmt.Sprintf("ID: %d, Nome: %s", *rel.EquipmentTypeID, *rel.TypeName)
			} else if rel.EquipmentType != "" {
				typeInfo = fmt.Sprintf("Tipo string: %s", rel.EquipmentType)
			}
			fmt.Printf("   - Equipamento %d (%s): %s\n", rel.EquipmentID, rel.EquipmentName, typeInfo)
		}
	}

	// 3. Verificar ordens de manutenção e tipos de equipamento
	fmt.Println("\n📋 3. ORDENS DE MANUTENÇÃO x TIPOS:")
	var orderTypeRelations []struct {
		OrderID         int    `gorm:"column:order_id"`
		OrderNumber     string `gorm:"column:order_number"`
		EquipmentID     int    `gorm:"column:equipment_id"`
		EquipmentTypeID *int   `gorm:"column:equipment_type_id"`
		TypeName        *string `gorm:"column:type_name"`
	}

	result = db.Raw(`
		SELECT 
			mo.id as order_id,
			mo.number as order_number,
			mo.equipment_id,
			mo.equipment_type_id,
			et.name as type_name
		FROM maintenance_orders mo
		LEFT JOIN equipment_types et ON mo.equipment_type_id = et.id
		ORDER BY mo.id DESC
		LIMIT 10
	`).Scan(&orderTypeRelations)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar ordens: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Últimas 10 ordens de manutenção:\n")
		for _, rel := range orderTypeRelations {
			typeInfo := "Sem tipo definido"
			if rel.EquipmentTypeID != nil && rel.TypeName != nil {
				typeInfo = fmt.Sprintf("ID: %d, Nome: %s", *rel.EquipmentTypeID, *rel.TypeName)
			}
			fmt.Printf("   - Ordem %s (ID: %d), Equipamento: %d, Tipo: %s\n", 
				rel.OrderNumber, rel.OrderID, rel.EquipmentID, typeInfo)
		}
	}

	// 4. Verificar vínculos técnico-tipo de equipamento
	fmt.Println("\n📋 4. VÍNCULOS TÉCNICO x TIPO DE EQUIPAMENTO:")
	var techEquipLinks []struct {
		TechnicianID    int    `gorm:"column:technician_id"`
		TechnicianName  string `gorm:"column:technician_name"`
		EquipmentTypeID int    `gorm:"column:equipment_type_id"`
		TypeName        string `gorm:"column:type_name"`
		IsActive        bool   `gorm:"column:is_active"`
	}

	result = db.Raw(`
		SELECT 
			tet.technician_id,
			u.name as technician_name,
			tet.equipment_type_id,
			et.name as type_name,
			tet.is_active
		FROM technician_equipment_type tet
		INNER JOIN users u ON tet.technician_id = u.id
		INNER JOIN equipment_types et ON tet.equipment_type_id = et.id
		ORDER BY tet.technician_id, tet.equipment_type_id
	`).Scan(&techEquipLinks)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar vínculos: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Vínculos técnico-tipo encontrados: %d\n", len(techEquipLinks))
		for _, link := range techEquipLinks {
			status := "Inativo"
			if link.IsActive {
				status = "Ativo"
			}
			fmt.Printf("   - Técnico %s (ID: %d) → Tipo %s (ID: %d) [%s]\n", 
				link.TechnicianName, link.TechnicianID, link.TypeName, link.EquipmentTypeID, status)
		}
	}

	// 5. Verificar estrutura final da tabela technician_equipment_type
	fmt.Println("\n📋 5. ESTRUTURA FINAL DA TABELA technician_equipment_type:")
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
		IsNullable string `gorm:"column:is_nullable"`
	}

	result = db.Raw(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_schema = 'public' AND table_name = 'technician_equipment_type' 
		ORDER BY ordinal_position
	`).Scan(&columns)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao obter estrutura: %v\n", result.Error)
	} else {
		fmt.Printf("✅ Estrutura da tabela:\n")
		for _, col := range columns {
			nullable := "NOT NULL"
			if col.IsNullable == "YES" {
				nullable = "NULL"
			}
			fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		}
	}

	fmt.Println("\n✅ ANÁLISE CONCLUÍDA!")
}
