package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔍 ANÁLISE DETALHADA DAS TABELAS DE VÍNCULOS")
	fmt.Println("===========================================")

	// Verificar todas as tabelas que existem
	var allTables []string
	result := db.Raw("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name").Scan(&allTables)
	if result.Error != nil {
		log.Fatalf("Erro ao buscar tabelas: %v", result.Error)
	}

	fmt.Printf("📋 Total de tabelas no banco: %d\n\n", len(allTables))

	// Filtrar tabelas relacionadas a técnicos e equipamentos
	relevantTables := []string{}
	for _, table := range allTables {
		if contains(table, "technician") || contains(table, "equipment") || contains(table, "user") || contains(table, "branch") {
			relevantTables = append(relevantTables, table)
		}
	}

	fmt.Println("📋 TABELAS RELEVANTES ENCONTRADAS:")
	for _, table := range relevantTables {
		fmt.Printf("   ✅ %s\n", table)
	}

	fmt.Println("\n🔍 ANALISANDO TABELA ESPECÍFICA: technician_equipment_type")
	fmt.Println("=========================================================")

	// Verificar se a tabela technician_equipment_type existe
	var exists bool
	result = db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'technician_equipment_type')").Scan(&exists)
	if result.Error != nil {
		log.Fatalf("Erro ao verificar tabela: %v", result.Error)
	}

	if !exists {
		fmt.Println("❌ Tabela technician_equipment_type NÃO EXISTE")
		
		// Verificar se existe a versão plural
		result = db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'technician_equipment_types')").Scan(&exists)
		if result.Error != nil {
			log.Fatalf("Erro ao verificar tabela plural: %v", result.Error)
		}
		
		if exists {
			fmt.Println("✅ Tabela technician_equipment_types (plural) EXISTE")
			analyzeTable(db, "technician_equipment_types")
		} else {
			fmt.Println("❌ Nenhuma das duas versões existe")
		}
	} else {
		fmt.Println("✅ Tabela technician_equipment_type (singular) EXISTE")
		analyzeTable(db, "technician_equipment_type")
	}

	fmt.Println("\n🔍 VERIFICANDO OUTRAS TABELAS IMPORTANTES")
	fmt.Println("========================================")

	importantTables := []string{"equipment_types", "users", "branches"}
	for _, table := range importantTables {
		var tableExists bool
		result = db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = ?)", table).Scan(&tableExists)
		if result.Error != nil {
			fmt.Printf("❌ Erro ao verificar %s: %v\n", table, result.Error)
			continue
		}
		
		if tableExists {
			fmt.Printf("✅ %s existe\n", table)
			analyzeTable(db, table)
		} else {
			fmt.Printf("❌ %s NÃO existe\n", table)
		}
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		 findInString(s, substr))))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func analyzeTable(db *gorm.DB, tableName string) {
	// Obter estrutura da tabela
	var columns []struct {
		ColumnName string `gorm:"column:column_name"`
		DataType   string `gorm:"column:data_type"`
		IsNullable string `gorm:"column:is_nullable"`
	}

	result := db.Raw(`
		SELECT column_name, data_type, is_nullable 
		FROM information_schema.columns 
		WHERE table_schema = 'public' AND table_name = ? 
		ORDER BY ordinal_position
	`, tableName).Scan(&columns)

	if result.Error != nil {
		fmt.Printf("   ❌ Erro ao obter estrutura: %v\n", result.Error)
		return
	}

	fmt.Printf("   📊 Estrutura:\n")
	for _, col := range columns {
		nullable := "NOT NULL"
		if col.IsNullable == "YES" {
			nullable = "NULL"
		}
		fmt.Printf("      - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
	}

	// Contar registros
	var count int64
	result = db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&count)
	if result.Error != nil {
		fmt.Printf("   ❌ Erro ao contar registros: %v\n", result.Error)
	} else {
		fmt.Printf("   📈 Registros: %d\n", count)
	}
	fmt.Println()
}
