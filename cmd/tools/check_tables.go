package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// Carregar variáveis de ambiente
	err := godotenv.Load()
	if err != nil {
		log.Println("Arquivo .env não encontrado, usando variáveis de ambiente do sistema")
	}

	// Configurar conexão com o banco de dados
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASS")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" || dbPort == "" || dbUser == "" || dbPassword == "" || dbName == "" {
		log.Fatalf("Variáveis de ambiente obrigatórias não encontradas. Configure: DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME")
	}

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Configurar logger do GORM
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	// Conectar ao banco de dados
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	fmt.Println("🔍 VERIFICANDO TABELAS RELACIONADAS A TÉCNICOS E EQUIPAMENTOS")
	fmt.Println("============================================================")

	// Lista de tabelas para verificar
	tables := []string{
		"technician_equipment_type",
		"technician_equipment_types", 
		"equipment_types",
		"technician_branches",
		"technician_orders",
		"users",
	}

	for _, tableName := range tables {
		fmt.Printf("\n📋 Verificando tabela: %s\n", tableName)
		
		// Verificar se a tabela existe
		var exists bool
		err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = ?)", tableName).Scan(&exists)
		if err != nil {
			fmt.Printf("❌ Erro ao verificar tabela %s: %v\n", tableName, err)
			continue
		}

		if !exists {
			fmt.Printf("❌ Tabela %s NÃO EXISTE\n", tableName)
			continue
		}

		fmt.Printf("✅ Tabela %s EXISTE\n", tableName)

		// Obter estrutura da tabela
		var columns []struct {
			ColumnName string `gorm:"column:column_name"`
			DataType   string `gorm:"column:data_type"`
			IsNullable string `gorm:"column:is_nullable"`
		}

		err = db.Raw(`
			SELECT column_name, data_type, is_nullable 
			FROM information_schema.columns 
			WHERE table_schema = 'public' AND table_name = ? 
			ORDER BY ordinal_position
		`, tableName).Scan(&columns)

		if err != nil {
			fmt.Printf("❌ Erro ao obter estrutura da tabela %s: %v\n", tableName, err)
			continue
		}

		fmt.Printf("   📊 Estrutura da tabela:\n")
		for _, col := range columns {
			nullable := "NOT NULL"
			if col.IsNullable == "YES" {
				nullable = "NULL"
			}
			fmt.Printf("   - %s: %s (%s)\n", col.ColumnName, col.DataType, nullable)
		}

		// Contar registros
		var count int64
		err = db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&count)
		if err != nil {
			fmt.Printf("❌ Erro ao contar registros da tabela %s: %v\n", tableName, err)
		} else {
			fmt.Printf("   📈 Total de registros: %d\n", count)
		}
	}

	fmt.Println("\n🔍 VERIFICANDO TABELAS RELACIONADAS ESPECIFICAMENTE")
	fmt.Println("==================================================")

	// Verificar se existe alguma tabela com padrão similar
	var similarTables []string
	result := db.Raw(`
		SELECT table_name
		FROM information_schema.tables
		WHERE table_schema = 'public'
		AND (table_name LIKE '%technician%equipment%' OR table_name LIKE '%equipment%technician%')
		ORDER BY table_name
	`).Scan(&similarTables)

	if result.Error != nil {
		fmt.Printf("❌ Erro ao buscar tabelas similares: %v\n", result.Error)
	} else {
		fmt.Printf("📋 Tabelas encontradas com padrão técnico-equipamento:\n")
		for _, table := range similarTables {
			fmt.Printf("   - %s\n", table)
		}
	}

	fmt.Println("\n✅ Verificação concluída!")
}
