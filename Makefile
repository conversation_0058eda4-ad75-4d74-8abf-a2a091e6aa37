# 🛠️ Makefile — Sistema Tradição
# Comandos para desenvolvimento, qualidade e deploy do sistema unificado

.PHONY: help dev run build test test-integration test-coverage fmt lint clean clean-legacy clean-cache
.PHONY: check-quality check-duplications check-architecture deploy-check
.PHONY: migrate-up migrate-down seed-dev backup deploy
.PHONY: flow painel zip

# 📚 HELP E DOCUMENTAÇÃO
help: ## 📖 Exibir todos os comandos disponíveis
	@echo "🛠️  Sistema Tradição - Comandos Disponíveis"
	@echo ""
	@echo "🚀 DESENVOLVIMENTO:"
	@echo "  dev              - Executar em modo desenvolvimento com hot reload"
	@echo "  run              - Executar backend normalmente"
	@echo "  build            - Compilar aplicação para produção"
	@echo ""
	@echo "🧪 TESTES E QUALIDADE:"
	@echo "  test             - Executar testes unitários"
	@echo "  test-integration - Executar testes de integração"
	@echo "  test-coverage    - Gerar relatório de cobertura de testes"
	@echo "  fmt              - Formatar código Go"
	@echo "  lint             - Executar linter (staticcheck)"
	@echo ""
	@echo "✅ VERIFICAÇÕES OBRIGATÓRIAS:"
	@echo "  check-quality      - Verificação completa de qualidade"
	@echo "  check-duplications - Verificar duplicações de código"
	@echo "  check-architecture - Verificar conformidade arquitetural"
	@echo "  deploy-check       - Verificação completa antes de deploy"
	@echo ""
	@echo "🗄️  BANCO DE DADOS:"
	@echo "  migrate-up       - Executar migrações"
	@echo "  migrate-down     - Reverter migrações"
	@echo "  seed-dev         - Popular banco com dados de desenvolvimento"
	@echo "  backup           - Backup do banco antes de deploy"
	@echo ""
	@echo "🚀 PRODUÇÃO:"
	@echo "  deploy           - Deploy com verificações obrigatórias"
	@echo ""
	@echo "🧹 LIMPEZA:"
	@echo "  clean            - Limpar binários e arquivos temporários"
	@echo "  clean-legacy     - Remover arquivos legados"
	@echo "  clean-cache      - Limpar cache de desenvolvimento"
	@echo ""
	@echo "🔧 FERRAMENTAS:"
	@echo "  flow             - Executar fluxo de manutenção"
	@echo "  painel           - Abrir painel MCP"
	@echo "  zip              - Gerar pacote MCP"

# 🚀 DESENVOLVIMENTO
dev: ## 🔄 Executar em modo desenvolvimento com hot reload
	@echo "🔄 Iniciando modo desenvolvimento..."
	@echo "📁 Monitorando mudanças em: cmd/, internal/, web/"
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "⚠️  Air não encontrado. Instalando..."; \
		go install github.com/cosmtrek/air@latest; \
		air; \
	fi

run: ## ▶️ Executar backend normalmente
	@echo "▶️ Executando backend..."
	go run cmd/main.go

build: check-architecture ## 🏗️ Compilar aplicação para produção
	@echo "🏗️ Compilando aplicação..."
	@mkdir -p bin
	go build -ldflags="-w -s" -o bin/tradicao cmd/main.go
	@echo "✅ Compilação concluída: bin/tradicao"

# 🧪 TESTES E QUALIDADE
test: ## 🧪 Executar testes unitários
	@echo "🧪 Rodando testes unitários..."
	go test -v ./...

test-integration: ## 🔗 Executar testes de integração
	@echo "🔗 Rodando testes de integração..."
	@echo "🔍 Verificando arquivo de testes de integração..."
	@if [ -f "tests/integration/unified_order_flow_test.go" ]; then \
		go test -v ./tests/integration/...; \
	else \
		echo "⚠️  Testes de integração não encontrados em tests/integration/"; \
		echo "📝 Execute: make help para ver comandos disponíveis"; \
	fi

test-coverage: ## 📊 Gerar relatório de cobertura de testes
	@echo "📊 Gerando relatório de cobertura..."
	@mkdir -p coverage
	go test -coverprofile=coverage/coverage.out ./...
	go tool cover -html=coverage/coverage.out -o coverage/coverage.html
	@echo "📈 Relatório gerado: coverage/coverage.html"
	@go tool cover -func=coverage/coverage.out | grep total | awk '{print "🎯 Cobertura total: " $$3}'

fmt: ## 🎨 Formatar código Go
	@echo "🎨 Formatando código..."
	go fmt ./...
	@echo "✅ Código formatado"

lint: ## 🔍 Executar linter (staticcheck)
	@echo "🔍 Rodando linter..."
	@if command -v staticcheck > /dev/null; then \
		staticcheck ./...; \
	else \
		echo "⚠️  Staticcheck não encontrado. Instalando..."; \
		go install honnef.co/go/tools/cmd/staticcheck@latest; \
		staticcheck ./...; \
	fi

# ✅ VERIFICAÇÕES OBRIGATÓRIAS
check-quality: fmt lint test test-coverage check-architecture ## 🎯 Verificação completa de qualidade
	@echo "🎯 Executando verificação completa de qualidade..."
	@echo "✅ Todas as verificações de qualidade concluídas"

check-duplications: ## 🔍 Verificar duplicações de código
	@echo "🔍 Verificando duplicações de código..."
	@echo "📋 Procurando handlers duplicados..."
	@find internal/handlers -name "*ordem*" -type f | wc -l | awk '{if($$1>1) print "❌ Encontrados " $$1 " handlers de ordem (máximo: 1)"; else print "✅ Apenas 1 handler de ordem encontrado"}'
	@echo "📋 Procurando rotas duplicadas..."
	@grep -r "/api/ordens" internal/routes/ 2>/dev/null | wc -l | awk '{if($$1>0) print "❌ Encontradas " $$1 " rotas legadas /api/ordens"; else print "✅ Nenhuma rota legada encontrada"}'

check-architecture: ## 🏛️ Verificar conformidade com ARQUITETURA_OBRIGATORIA.md
	@echo "🏛️ Verificando conformidade arquitetural..."
	@if [ -f "scripts/check_architecture_compliance.sh" ]; then \
		chmod +x scripts/check_architecture_compliance.sh; \
		./scripts/check_architecture_compliance.sh; \
	else \
		echo "⚠️  Script de verificação não encontrado: scripts/check_architecture_compliance.sh"; \
		echo "📝 Verificação manual de arquitetura:"; \
		echo "   - Handler unificado: internal/handlers/unified_order_handler.go"; \
		echo "   - Rotas unificadas: internal/routes/unified_order_routes_new.go"; \
		echo "   - JavaScript unificado: web/static/js/unified_orders.js"; \
	fi

deploy-check: check-quality check-duplications backup ## 🚀 Verificação completa antes de deploy
	@echo "🚀 Executando verificação completa antes de deploy..."
	@echo "✅ Sistema pronto para deploy"

# 🗄️ BANCO DE DADOS
migrate-up: ## ⬆️ Executar migrações
	@echo "⬆️ Executando migrações..."
	@if [ -f "cmd/migrate/main.go" ]; then \
		go run cmd/migrate/main.go up; \
	else \
		echo "⚠️  Arquivo de migração não encontrado: cmd/migrate/main.go"; \
		echo "🔧 Usando migrate CLI se disponível..."; \
		if command -v migrate > /dev/null; then \
			migrate -path migrations -database "$$DATABASE_URL" up; \
		else \
			echo "❌ Migrate CLI não encontrado. Instale: go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest"; \
		fi \
	fi

migrate-down: ## ⬇️ Reverter migrações
	@echo "⬇️ Revertendo migrações..."
	@echo "⚠️  ATENÇÃO: Esta operação pode causar perda de dados!"
	@read -p "Confirma reversão de migrações? [y/N]: " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		if [ -f "cmd/migrate/main.go" ]; then \
			go run cmd/migrate/main.go down; \
		elif command -v migrate > /dev/null; then \
			migrate -path migrations -database "$$DATABASE_URL" down; \
		else \
			echo "❌ Sistema de migração não encontrado"; \
		fi \
	else \
		echo "❌ Operação cancelada"; \
	fi

seed-dev: ## 🌱 Popular banco com dados de desenvolvimento
	@echo "🌱 Populando banco com dados de desenvolvimento..."
	@if [ -f "cmd/seed/main.go" ]; then \
		go run cmd/seed/main.go; \
	else \
		echo "⚠️  Arquivo de seed não encontrado: cmd/seed/main.go"; \
		echo "📝 Crie o arquivo para popular dados de desenvolvimento"; \
	fi

backup: ## 💾 Backup do banco antes de deploy
	@echo "💾 Realizando backup do banco..."
	@if [ -n "$$DATABASE_URL" ]; then \
		timestamp=$$(date +%Y%m%d_%H%M%S); \
		mkdir -p backups; \
		echo "📦 Criando backup: backups/backup_$$timestamp.sql"; \
		pg_dump "$$DATABASE_URL" > "backups/backup_$$timestamp.sql" 2>/dev/null || \
		echo "⚠️  Erro no backup. Verifique DATABASE_URL e pg_dump"; \
	else \
		echo "⚠️  DATABASE_URL não configurada. Backup não realizado."; \
	fi

# 🚀 PRODUÇÃO
deploy: deploy-check ## 🚀 Deploy com verificações obrigatórias
	@echo "🚀 Iniciando deploy com verificações obrigatórias..."
	@echo "✅ Todas as verificações passaram. Prosseguindo com deploy..."
	./.trae/agents/agente_deploy.sh

# 🧹 LIMPEZA
clean: ## 🧹 Limpar binários e arquivos temporários
	@echo "🧹 Limpando binários e arquivos temporários..."
	rm -rf bin/ MCP_ProjetoTradicao.zip coverage/ tmp/

clean-legacy: ## 🗑️ Remover arquivos legados
	@echo "🗑️ Removendo arquivos legados..."
	@echo "⚠️  Esta operação remove handlers e rotas legadas!"
	@read -p "Confirma remoção de arquivos legados? [y/N]: " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		echo "🗑️ Removendo handlers legados..."; \
		rm -f internal/handlers/api_ordens.go; \
		rm -f internal/handlers/ordens.go; \
		rm -f internal/handlers/verordens_handler.go; \
		rm -f internal/handlers/ordem_v2_handler.go; \
		echo "🗑️ Removendo rotas legadas..."; \
		rm -f internal/routes/ordens.go; \
		rm -f internal/routes/ordens_v2_routes.go; \
		rm -f internal/routes/verordens_routes.go; \
		echo "✅ Arquivos legados removidos"; \
	else \
		echo "❌ Operação cancelada"; \
	fi

clean-cache: ## 🧽 Limpar cache de desenvolvimento
	@echo "🧽 Limpando cache de desenvolvimento..."
	go clean -cache -modcache -testcache
	rm -rf .air_tmp/
	@echo "✅ Cache limpo"

# 🔧 FERRAMENTAS (mantidas do Makefile original)
flow: ## 🔄 Executar fluxo padrão de manutenção
	@echo "🔄 Executando fluxo padrão de manutenção..."
	@echo "[fluxo] Verificando backend → Atualizando frontend → Auditoria"
	@bash .trae/fluxos/manutencao.yaml

painel: ## 🧠 Abrir painel MCP
	@echo "🧠 Abrindo painel MCP"
	./.trae/painel_mcp.sh

zip: ## 📦 Gerar pacote .zip do MCP
	@echo "📦 Gerando pacote .zip do MCP"
	zip -r MCP_ProjetoTradicao.zip .trae/
