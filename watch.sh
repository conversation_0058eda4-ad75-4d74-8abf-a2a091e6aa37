#!/bin/bash

# Script simples de hot reload usando inotify
# Uso: ./watch.sh

echo "🔧 HOT RELOAD SIMPLES - REDE TRADIÇÃO"
echo "====================================="

# Configurar PATH do Go
export PATH="/usr/local/go/bin:$PATH"
export GOPATH="$HOME/go"
export PATH="$GOPATH/bin:$PATH"

# Verificar se .env existe
if [ ! -f .env ]; then
    echo "❌ Arquivo .env não encontrado!"
    exit 1
fi

# Função para compilar e executar
build_and_run() {
    echo "🔄 Recompilando..."
    
    # Parar processo anterior se existir
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null
        wait $SERVER_PID 2>/dev/null
    fi
    
    # Compilar
    go build -o ./tmp/main . 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Compilação bem-sucedida"
        # Executar em background
        ./tmp/main &
        SERVER_PID=$!
        echo "🚀 Servidor iniciado (PID: $SERVER_PID)"
    else
        echo "❌ Erro na compilação"
    fi
}

# Criar diretório tmp
mkdir -p tmp

# Compilação inicial
echo "🔨 Compilação inicial..."
build_and_run

echo ""
echo "👀 Monitorando mudanças em arquivos .go..."
echo "   Ctrl+C para parar"
echo ""

# Monitorar mudanças
while true; do
    # Aguardar mudanças em arquivos .go
    inotifywait -r -e modify,create,delete --include='\.go$' . 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "📝 Mudança detectada!"
        sleep 1  # Aguardar um pouco para evitar múltiplas recompilações
        build_and_run
    fi
done
