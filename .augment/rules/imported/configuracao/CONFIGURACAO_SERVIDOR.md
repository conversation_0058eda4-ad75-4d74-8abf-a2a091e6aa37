---
type: "manual"
---

# 📋 Configuração do Servidor - Projeto Tradição

## 🎯 O que Aprendemos

Durante a resolução dos problemas de login, identificamos pontos críticos na configuração do servidor que são essenciais para o funcionamento correto da aplicação.

## 🔧 Configuração do Servidor

### 1. Estrutura de Inicialização

#### Script de Inicialização Rápida (`iniciar_rapido.sh`)
```bash
# Configurações principais
export DB_PERSISTENT_CONNECTION="true"
export GIN_MODE="release"

# Servidor configurado para escutar em:
HOST="0.0.0.0"  # Todas as interfaces
PORT="8080"      # Porta padrão
```

#### Configuração do Servidor HTTP (`cmd/main.go`)
```go
// Endereço do servidor
addr := fmt.Sprintf("0.0.0.0:%d", *port)

// Configuração do servidor HTTP
srv := &http.Server{
    Addr:    addr,
    Handler: router,
}

// Inicialização em goroutine
go func() {
    if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
        log.Fatalf("Erro ao iniciar servidor: %v", err)
    }
}()
```

### 2. Variáveis de Ambiente Obrigatórias

#### Arquivo `.env` - Configurações Essenciais
```env
# Banco de Dados
DB_HOST=*************
DB_PORT=5432
DB_USER=postgres
DB_PASS=i1t2a3l4o5
DB_NAME=tradicao

# Aplicação
PORT=8080
ENV=development

# Segurança JWT
JWT_SECRET=chave-secreta-shell-tradicao
JWT_EXPIRATION=1h

# Logs
LOG_LEVEL=debug
LOG_FILE=logs/app.log
```

### 3. Validações de Inicialização

#### Verificações Automáticas do Script
1. **Diretório de Trabalho**: Verifica se está no diretório raiz do projeto
2. **Arquivo .env**: Confirma existência e carrega variáveis
3. **Variáveis Obrigatórias**: Valida DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME
4. **Portas Livres**: Mata processos nas portas 8080, 8081, 8082
5. **Compilação**: Compila a aplicação antes de executar

### 4. Configuração de Rede

#### Endereçamento
- **Interface**: `0.0.0.0` (todas as interfaces de rede)
- **Porta**: `8080` (configurável via variável PORT)
- **Protocolo**: HTTP/1.1
- **IPv4/IPv6**: Suporte completo

#### Acessibilidade
```bash
# URLs de acesso válidas:
http://localhost:8080
http://127.0.0.1:8080
http://0.0.0.0:8080
http://[IP_DA_MÁQUINA]:8080
```

### 5. Configuração do Gin Framework

#### Modo de Execução
```go
// Configurado via variável de ambiente
export GIN_MODE="release"  // Produção
// ou
export GIN_MODE="debug"    // Desenvolvimento
```

#### Router Principal (`cmd/api/rest.go`)
```go
// Inicialização do router
router := gin.Default()

// Configuração de componentes unificados
setup.SetupUnifiedComponents(router, db)

// Configuração de rotas de ordens
routes.SetupOrderRoutes(router, orderHandler)
```

### 6. Configuração de Banco de Dados

#### Conexão PostgreSQL
```go
// String de conexão completa
DATABASE_URL=***************************************************/tradicao?sslmode=disable

// Configuração de conexão persistente
DB_PERSISTENT_CONNECTION="true"
```

#### Inicialização GORM
```go
db, err := database.InitGorm()
if err != nil {
    log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
}
```

### 7. Configuração de Segurança

#### JWT (JSON Web Tokens)
```go
// Configuração no config.go
type Config struct {
    JWTSecret string  // Chave secreta para assinatura
    // ... outras configurações
}

// Carregamento da configuração
cfg := &Config{
    JWTSecret: getEnv("JWT_SECRET", "your-secret-key"),
    // ...
}
```

### 8. Shutdown Gracioso

#### Encerramento Controlado
```go
// Aguardar sinal de interrupção
<-quit
log.Println("Encerrando servidor...")

// Contexto com timeout para shutdown
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

// Encerramento gracioso
if err := srv.Shutdown(ctx); err != nil {
    log.Fatalf("Erro ao encerrar servidor: %v", err)
}
```

## 🚨 Problemas Identificados e Soluções

### 1. Servidor Encerrando Automaticamente
**Problema**: O servidor iniciava mas encerrava imediatamente
**Causa**: Falta de canal de bloqueio para manter o processo ativo
**Solução**: Implementado canal `quit` para aguardar sinais de interrupção

### 2. Conectividade de Rede
**Problema**: Conexões falhando mesmo com servidor rodando
**Causa**: Configuração de rede e firewall
**Solução**: Verificação de portas com `ss -tlnp` e `netstat`

### 3. Validação de Ambiente
**Problema**: Falhas silenciosas por variáveis não configuradas
**Causa**: Falta de validação das variáveis obrigatórias
**Solução**: Script de validação completa antes da inicialização

## 📝 Checklist de Configuração

### Antes de Iniciar o Servidor
- [ ] Arquivo `.env` existe e está configurado
- [ ] Variáveis de banco de dados estão corretas
- [ ] Porta 8080 está livre
- [ ] Diretório de trabalho é o raiz do projeto
- [ ] Aplicação compila sem erros

### Durante a Execução
- [ ] Servidor responde em `http://localhost:8080`
- [ ] Logs mostram "Servidor iniciado em 0.0.0.0:8080"
- [ ] Conexão com banco de dados estabelecida
- [ ] Rotas de API respondem corretamente

### Monitoramento
- [ ] Verificar logs em `logs/app.log`
- [ ] Monitorar uso de porta com `ss -tlnp | grep 8080`
- [ ] Verificar processos com `ps aux | grep app`

## 🔍 Comandos de Diagnóstico

```bash
# Verificar se o servidor está rodando
ss -tlnp | grep 8080

# Verificar processos da aplicação
ps aux | grep app

# Testar conectividade
curl -I http://localhost:8080

# Verificar logs
tail -f logs/app.log

# Verificar variáveis de ambiente
env | grep DB_
```

## 📚 Arquivos de Configuração Principais

1. **`iniciar_rapido.sh`** - Script de inicialização com validações
2. **`.env`** - Variáveis de ambiente
3. **`cmd/main.go`** - Configuração principal do servidor
4. **`cmd/api/rest.go`** - Configuração do router Gin
5. **`internal/config/config.go`** - Estrutura de configuração

## 🎯 Próximos Passos

1. **Monitoramento**: Implementar health checks
2. **Logs**: Estruturar logs com níveis apropriados
3. **Segurança**: Revisar configurações de CORS e headers
4. **Performance**: Configurar timeouts e limites de conexão
5. **Deploy**: Configurar para ambiente de produção

---

*Documentação gerada baseada na análise da configuração atual do servidor - Projeto Tradição*