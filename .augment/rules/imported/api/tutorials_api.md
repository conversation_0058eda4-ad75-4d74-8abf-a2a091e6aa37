---
type: "manual"
---

# API de Tutoriais

Esta API permite criar, listar, visualizar, editar e excluir tutoriais, com orquestração de agentes especialistas (Supervisor, TutorialAgent, NotificationsAgent, SecurityAgent).

## Endpoints

### Listar tutoriais
- **GET /api/tutorials**
- Parâmetros: `offset`, `limit`
- Resposta:
```json
{
  "total": 2,
  "items": [
    { "id": 1, "title": "Como acessar o sistema", "content": "..." },
    ...
  ]
}
```

### Visualizar tutorial
- **GET /api/tutorials/:id**
- Resposta:
```json
{
  "id": 1,
  "title": "Como acessar o sistema",
  "content": "..."
}
```

### Criar tutorial
- **POST /api/tutorials**
- Body:
```json
{
  "title": "Novo tutorial",
  "content": "Conteúdo do tutorial"
}
```
- Resposta: 201 Created

### Editar tutorial
- **PUT /api/tutorials/:id**
- Body igual ao POST
- Resposta: 200 OK

### Excluir tutorial
- **DELETE /api/tutorials/:id**
- Resposta: 204 No Content

## Fluxo Orquestrado
- O SupervisorAgent valida permissões (SecurityAgent), aciona o TutorialAgent para CRUD e o NotificationsAgent para avisar usuários.
- Apenas usuários com permissão `tutorial:criar` podem criar tutoriais.
- Notificações são enviadas automaticamente após criação/edição. 