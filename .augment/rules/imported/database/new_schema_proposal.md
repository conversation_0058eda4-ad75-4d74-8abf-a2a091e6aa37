---
type: "manual"
---

# Proposta de Novo Esquema de Banco de Dados

## Visão Geral

Este documento apresenta a proposta de um novo esquema de banco de dados normalizado e consistente, que resolverá os problemas identificados na análise do esquema atual.

## Princípios de Design

1. **Nomenclatura Consistente**: Todos os nomes de tabelas e colunas serão em inglês
2. **Normalização**: Esquema normalizado para evitar redundância
3. **Integridade Referencial**: Todas as relações terão constraints apropriadas
4. **Auditoria**: Campos de auditoria (created_at, updated_at) em todas as tabelas
5. **Soft Delete**: Uso de deleted_at para exclusão lógica onde apropriado

## Esquema Proposto

### Tabelas Principais

1. **users**
   ```sql
   CREATE TABLE users (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       email VARCHAR(100) NOT NULL UNIQUE,
       password VARCHAR(255) NOT NULL,
       role VARCHAR(50) NOT NULL,
       branch_id INTEGER REFERENCES branches(id),
       failed_attempts INTEGER DEFAULT 0,
       blocked BOOLEAN DEFAULT FALSE,
       totp_secret VARCHAR(255) DEFAULT '',
       totp_enabled BOOLEAN DEFAULT FALSE,
       last_password_change TIMESTAMP,
       force_password_change BOOLEAN DEFAULT FALSE,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_at TIMESTAMP
   );
   ```

2. **branches**
   ```sql
   CREATE TABLE branches (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       code VARCHAR(20) NOT NULL UNIQUE,
       address VARCHAR(255),
       city VARCHAR(100),
       state VARCHAR(2),
       zip_code VARCHAR(20),
       phone VARCHAR(20),
       email VARCHAR(100),
       type VARCHAR(20) DEFAULT 'urban',
       is_active BOOLEAN DEFAULT TRUE,
       manager_id INTEGER REFERENCES users(id),
       latitude DECIMAL(10, 8),
       longitude DECIMAL(11, 8),
       opening_hours VARCHAR(255),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_at TIMESTAMP
   );
   ```

3. **equipment**
   ```sql
   CREATE TABLE equipment (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       serial_number VARCHAR(100),
       model VARCHAR(100),
       brand VARCHAR(100),
       type VARCHAR(50) NOT NULL,
       installation_date TIMESTAMP,
       last_maintenance TIMESTAMP,
       last_preventive TIMESTAMP,
       next_preventive TIMESTAMP,
       status VARCHAR(50) DEFAULT 'active',
       location VARCHAR(100),
       branch_id INTEGER NOT NULL REFERENCES branches(id),
       notes TEXT,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_at TIMESTAMP
   );
   ```

4. **maintenance_orders**
   ```sql
   CREATE TABLE maintenance_orders (
       id SERIAL PRIMARY KEY,
       title VARCHAR(100) NOT NULL,
       description TEXT,
       status VARCHAR(50) DEFAULT 'pending',
       priority VARCHAR(20) DEFAULT 'medium',
       branch_id INTEGER NOT NULL REFERENCES branches(id),
       equipment_id INTEGER NOT NULL REFERENCES equipment(id),
       requested_by INTEGER NOT NULL REFERENCES users(id),
       created_by_user_id INTEGER NOT NULL REFERENCES users(id),
       assigned_provider_id INTEGER REFERENCES service_providers(id),
       approved_by_user_id INTEGER REFERENCES users(id),
       scheduled_date TIMESTAMP,
       completion_date TIMESTAMP,
       estimated_cost DECIMAL(10, 2),
       actual_cost DECIMAL(10, 2),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

5. **service_providers**
   ```sql
   CREATE TABLE service_providers (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       cnpj VARCHAR(20) NOT NULL,
       specialties VARCHAR(255),
       area_of_expertise VARCHAR(100),
       contact VARCHAR(100),
       average_rating DECIMAL(3, 2),
       status VARCHAR(20) DEFAULT 'active',
       user_id INTEGER REFERENCES users(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

### Tabelas de Suporte

1. **tags**
   ```sql
   CREATE TABLE tags (
       id SERIAL PRIMARY KEY,
       name VARCHAR(50) NOT NULL,
       category_id INTEGER REFERENCES tag_categories(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

2. **tag_categories**
   ```sql
   CREATE TABLE tag_categories (
       id SERIAL PRIMARY KEY,
       name VARCHAR(50) NOT NULL,
       description VARCHAR(255),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

3. **equipment_tags**
   ```sql
   CREATE TABLE equipment_tags (
       equipment_id INTEGER NOT NULL REFERENCES equipment(id),
       tag_id INTEGER NOT NULL REFERENCES tags(id),
       PRIMARY KEY (equipment_id, tag_id)
   );
   ```

4. **materials**
   ```sql
   CREATE TABLE materials (
       id SERIAL PRIMARY KEY,
       maintenance_order_id INTEGER NOT NULL REFERENCES maintenance_orders(id),
       name VARCHAR(100) NOT NULL,
       quantity INTEGER NOT NULL,
       unit_cost DECIMAL(10, 2) NOT NULL,
       cost DECIMAL(10, 2) NOT NULL,
       unit VARCHAR(20),
       added_by_user_id INTEGER NOT NULL REFERENCES users(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

5. **maintenance_order_photos**
   ```sql
   CREATE TABLE maintenance_order_photos (
       id SERIAL PRIMARY KEY,
       order_id INTEGER NOT NULL REFERENCES maintenance_orders(id),
       file_path VARCHAR(255) NOT NULL,
       file_name VARCHAR(100) NOT NULL,
       file_size INTEGER NOT NULL,
       content_type VARCHAR(50) NOT NULL,
       caption VARCHAR(255),
       uploaded_by INTEGER NOT NULL REFERENCES users(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

6. **security_policies**
   ```sql
   CREATE TABLE security_policies (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       password_min_length INTEGER DEFAULT 8,
       password_require_uppercase BOOLEAN DEFAULT TRUE,
       password_require_number BOOLEAN DEFAULT TRUE,
       password_require_special_char BOOLEAN DEFAULT TRUE,
       password_expiry_days INTEGER DEFAULT 90,
       max_login_attempts INTEGER DEFAULT 5,
       lockout_duration_minutes INTEGER DEFAULT 30,
       enable_2fa BOOLEAN DEFAULT FALSE,
       session_timeout_minutes INTEGER DEFAULT 60,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

7. **audit_logs**
   ```sql
   CREATE TABLE audit_logs (
       id SERIAL PRIMARY KEY,
       user_id INTEGER REFERENCES users(id),
       action VARCHAR(100) NOT NULL,
       resource VARCHAR(100),
       details TEXT,
       ip VARCHAR(50),
       user_agent VARCHAR(255),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

8. **calendar_events**
   ```sql
   CREATE TABLE calendar_events (
       id SERIAL PRIMARY KEY,
       title VARCHAR(100) NOT NULL,
       description TEXT,
       start_date TIMESTAMP NOT NULL,
       end_date TIMESTAMP,
       user_id INTEGER REFERENCES users(id),
       maintenance_order_id INTEGER REFERENCES maintenance_orders(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

## Índices

Para otimizar a performance, serão criados os seguintes índices:

```sql
-- Índices para users
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_branch_id ON users(branch_id);

-- Índices para branches
CREATE INDEX idx_branches_manager_id ON branches(manager_id);
CREATE INDEX idx_branches_is_active ON branches(is_active);
CREATE INDEX idx_branches_city_state ON branches(city, state);

-- Índices para equipment
CREATE INDEX idx_equipment_branch_id ON equipment(branch_id);
CREATE INDEX idx_equipment_type ON equipment(type);
CREATE INDEX idx_equipment_status ON equipment(status);
CREATE INDEX idx_equipment_next_preventive ON equipment(next_preventive);

-- Índices para maintenance_orders
CREATE INDEX idx_maintenance_orders_branch_id ON maintenance_orders(branch_id);
CREATE INDEX idx_maintenance_orders_equipment_id ON maintenance_orders(equipment_id);
CREATE INDEX idx_maintenance_orders_status ON maintenance_orders(status);
CREATE INDEX idx_maintenance_orders_priority ON maintenance_orders(priority);
CREATE INDEX idx_maintenance_orders_scheduled_date ON maintenance_orders(scheduled_date);

-- Outros índices
CREATE INDEX idx_service_providers_status ON service_providers(status);
CREATE INDEX idx_tags_category_id ON tags(category_id);
CREATE INDEX idx_materials_maintenance_order_id ON materials(maintenance_order_id);
CREATE INDEX idx_maintenance_order_photos_order_id ON maintenance_order_photos(order_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX idx_calendar_events_maintenance_order_id ON calendar_events(maintenance_order_id);
CREATE INDEX idx_calendar_events_start_date ON calendar_events(start_date);
```

## Constraints e Validações

Além das constraints de chave primária e estrangeira, serão adicionadas as seguintes validações:

```sql
-- Validação de role em users
ALTER TABLE users ADD CONSTRAINT check_user_role CHECK (role IN ('admin', 'manager', 'financial', 'branch_user', 'technician', 'provider'));

-- Validação de status em equipment
ALTER TABLE equipment ADD CONSTRAINT check_equipment_status CHECK (status IN ('active', 'inactive', 'maintenance', 'discontinued'));

-- Validação de status em maintenance_orders
ALTER TABLE maintenance_orders ADD CONSTRAINT check_order_status CHECK (status IN ('pending', 'approved', 'in_progress', 'completed', 'cancelled'));

-- Validação de prioridade em maintenance_orders
ALTER TABLE maintenance_orders ADD CONSTRAINT check_order_priority CHECK (priority IN ('low', 'medium', 'high', 'critical'));
```

## Próximos Passos

Com este esquema definido, os próximos passos são:

1. Criar scripts de migração Atlas para implementar o novo esquema
2. Desenvolver scripts para migrar dados das tabelas antigas para as novas
3. Atualizar os modelos Go para refletir o novo esquema
4. Adaptar os repositórios para trabalhar com as novas tabelas
