---
type: "manual"
---

# IMPORTANTE: Uso de Banco de Dados no Sistema Tradição

## Regra Fundamental: PROIBIDO Uso de Banco de Dados Local

**O uso de banco de dados local está EXPRESSAMENTE PROIBIDO neste projeto.**

Todas as conexões devem ser feitas exclusivamente com o banco de dados remoto especificado no arquivo `.env`.

## Configuração Correta

O sistema foi modificado para garantir que apenas o banco de dados remoto seja utilizado. As seguintes alterações foram implementadas:

1. Verificação em todos os pontos de conexão para bloquear tentativas de uso de banco de dados local
2. Priorização do uso da variável `DATABASE_URL` para conexão
3. Uso de valores padrão que apontam para o banco remoto quando as variáveis de ambiente não estão definidas

## Configuração Obrigatória do Banco de Dados

**IMPORTANTE**: O sistema NÃO possui valores padrão hardcoded. <PERSON><PERSON> as configurações devem ser fornecidas através de variáveis de ambiente no arquivo .env.

Se as variáveis de ambiente não estiverem definidas, o sistema irá falhar com erro e não iniciará.

## Arquivo .env

O arquivo `.env` deve conter as seguintes variáveis para conexão com o banco de dados:

```
DB_HOST=seu_host_do_banco
DB_PORT=sua_porta_do_banco
DB_USER=seu_usuario_do_banco
DB_PASS=sua_senha_do_banco
DB_NAME=nome_do_banco

# String de conexão completa para PostgreSQL
DATABASE_URL=postgresql://usuario:senha@host:porta/banco?sslmode=disable
```

**ATENÇÃO**: Substitua os valores acima pelas credenciais reais do seu banco de dados. Nunca commite credenciais reais no controle de versão.

## Motivos para esta Restrição

1. **Consistência de Dados**: Garantir que todos os desenvolvedores trabalhem com os mesmos dados
2. **Evitar Problemas de Migração**: Prevenir problemas de esquema de banco de dados inconsistente
3. **Segurança**: Manter os dados em um ambiente controlado e seguro
4. **Simplificação**: Eliminar a necessidade de configurar e manter bancos de dados locais

## O que Acontece se Tentar Usar Banco de Dados Local?

Se o sistema detectar uma tentativa de conexão com um banco de dados local (host = "localhost" ou "127.0.0.1"), ele irá:

1. Exibir uma mensagem de erro clara
2. Encerrar a aplicação imediatamente
3. Registrar o erro nos logs

## Arquivos Modificados

Os seguintes arquivos foram modificados para implementar esta restrição:

1. `internal/database/database.go`
2. `internal/database/connection.go`
3. `internal/database/connection_pool.go`
4. `internal/config/database.go`
5. `internal/config/config.go`
6. `internal/database/postgresql.go`

## Como Testar Conexão com o Banco de Dados

Para verificar se a conexão com o banco de dados remoto está funcionando corretamente, execute:

```bash
go run scripts/verify_user.go <EMAIL>
```

Este comando deve retornar informações sobre o usuário técnico, confirmando que a conexão com o banco de dados remoto está funcionando.

## Suporte e Dúvidas

Em caso de dúvidas ou problemas com a conexão ao banco de dados, consulte a documentação ou entre em contato com a equipe de suporte.

**LEMBRE-SE: Nunca tente modificar o código para usar um banco de dados local. Esta restrição é fundamental para o funcionamento correto do sistema.**
