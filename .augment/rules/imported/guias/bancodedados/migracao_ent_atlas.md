---
type: "manual"
---

# Migração para ENT e Atlas

Este documento descreve o processo de migração do banco de dados para o ENT e Atlas.

## Visão Geral

O projeto passou por uma migração de banco de dados, substituindo a implementação baseada em GORM por uma implementação baseada em ENT e Atlas. Para isso, foi necessário limpar o banco de dados, removendo tabelas e estruturas não utilizadas que causavam erros durante a migração.

## Mudanças Principais

### 1. Nomenclatura

A principal mudança foi a padronização da nomenclatura das tabelas e colunas em inglês:

| Antigo (Português) | Novo (Inglês) |
|-------------------|---------------|
| filiais | branches |
| usuarios | users |
| equipamentos | equipment |
| ordens_servico | maintenance_orders |

### 2. Estrutura das Tabelas

As tabelas foram redesenhadas para seguir boas práticas de modelagem de dados:

- Adição de colunas para soft delete (`deleted_at`)
- Padronização das colunas de timestamp (`created_at`, `updated_at`)
- Adição de índices para otimizar consultas comuns
- Definição explícita de chaves estrangeiras

### 3. Relacionamentos

Os relacionamentos entre as tabelas foram explicitamente definidos:

- Filiais (branches) têm relacionamentos com equipamentos, usuários e ordens de manutenção
- Usuários (users) podem ser técnicos/prestadores associados a filiais
- Equipamentos (equipment) pertencem a filiais
- Ordens de manutenção (maintenance_orders) estão associadas a filiais, equipamentos e usuários

## Processo de Migração

O processo de migração foi realizado em várias etapas:

### 1. Backup dos Bancos de Dados

Antes de qualquer alteração, foram feitos backups completos dos bancos de dados existentes:

```bash
sudo ./scripts/backup_databases.sh
```

### 2. Limpeza do Banco de Dados

Foi criado um novo banco de dados limpo com apenas as tabelas necessárias para o ENT:

```bash
sudo ./scripts/clean_database.sh
```

Este script:
- Criou um novo banco de dados `tradicao_ent`
- Criou as tabelas necessárias com a estrutura correta
- Migrou os dados relevantes dos bancos de dados existentes

### 3. Configuração do ENT e Atlas

Foram criados os arquivos de configuração necessários para o ENT e Atlas:

```bash
./scripts/setup_ent_atlas.sh
```

Este script:
- Criou os arquivos de esquema do Atlas
- Configurou o Atlas para gerenciar as migrações
- Criou os arquivos de migração inicial

## Estrutura do Novo Banco de Dados

O novo banco de dados `tradicao_ent` contém as seguintes tabelas:

1. **branches**: Filiais/postos
2. **users**: Usuários do sistema (incluindo técnicos/prestadores)
3. **equipment**: Equipamentos
4. **maintenance_orders**: Ordens de manutenção
5. **schema_versions**: Tabela de controle de versões do Atlas

Para mais detalhes sobre a estrutura de cada tabela, consulte o documento [Estrutura Detalhada das Tabelas](estrutura_tabelas.md).

Para mais detalhes sobre os relacionamentos entre as tabelas, consulte o documento [Relacionamentos do Banco de Dados](relacionamentos.md).

## Uso do ENT

O ENT é um ORM (Object-Relational Mapping) para Go que gera código fortemente tipado para interagir com o banco de dados. Para usar o ENT com o novo banco de dados:

1. Os esquemas das entidades estão definidos em `ent/schema/`
2. O código gerado pelo ENT está em `ent/`
3. Para gerar o código do ENT após alterações nos esquemas:

```bash
go generate ./ent
```

## Uso do Atlas

O Atlas é uma ferramenta de migração de banco de dados que gerencia as alterações no esquema. Para usar o Atlas:

1. Os arquivos de migração estão em `migrations/atlas/`
2. A configuração do Atlas está em `atlas.hcl`
3. Para aplicar migrações:

```bash
./scripts/run_atlas_migrations.sh
```

## Próximos Passos

1. Atualizar o código da aplicação para usar o ENT em vez do GORM
2. Implementar novas funcionalidades usando o ENT
3. Criar novas migrações com o Atlas conforme necessário

## Solução de Problemas

Se você encontrar problemas com o ENT ou Atlas, consulte:

1. [Documentação oficial do ENT](https://entgo.io/docs/getting-started)
2. [Documentação oficial do Atlas](https://atlasgo.io/getting-started)
3. Logs em `logs/`
