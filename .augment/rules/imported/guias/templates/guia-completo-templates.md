---
type: "manual"
---

# <PERSON><PERSON><PERSON><PERSON> de Templates e Criação de Páginas no Gin

## 1. Fundamentos do Design System

### 1.1 Cores
```css
:root {
    /* Cores Principais */
    --shell-red: #ED1C24;
    --shell-red-hover: #CE181F;
    --shell-red-light: #ff6b6e;
    --shell-yellow: #FDB813;
    --shell-yellow-hover: #e0a100;
    --shell-yellow-light: #ffe07a;
    --shell-dark: #333333;
    --shell-dark-hover: #222222;
    --shell-light: #f8f9fa;
    --shell-light-hover: #e9ecef;
    --shell-gray: #808080;
    --shell-gray-light: #d9d9d9;

    /* Cores de Status */
    --shell-success: #28a745;
    --shell-info: #17a2b8;
    --shell-warning: #ffc107;
    --shell-danger: #dc3545;
}
```

### 1.2 Tipografia
```css
/* <PERSON><PERSON><PERSON> */
font-family: 'Raj<PERSON><PERSON>', sans-serif; /* T<PERSON><PERSON>los e destaques */
font-family: 'Share Tech Mono', monospace; /* Elementos técnicos */

/* Tamanhos */
--font-size-xs: 0.75rem;   /* 12px */
--font-size-sm: 0.875rem;  /* 14px */
--font-size-md: 1rem;      /* 16px */
--font-size-lg: 1.25rem;   /* 20px */
--font-size-xl: 1.5rem;    /* 24px */
--font-size-xxl: 2rem;     /* 32px */

/* Pesos */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

## 2. Integração com Gin

### 2.1 Configuração do Gin
```go
func (t *TemplateLoader) LoadTemplates() error {
    t.engine.SetFuncMap(template.FuncMap{
        "formatDate": helpers.FormatDate,
    })
    pattern := filepath.Join(t.config.TemplatesDir, "**/*.html")
    t.engine.LoadHTMLGlob(pattern)
    return nil
}
```

### 2.2 Estrutura de Templates

#### Template Completo (base_sidemenu.html)
```html
{{ define "secao/pagina.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Título da Página - Rede Tradição Shell</title>
    
    <!-- Recursos CSS -->
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/pagina.css">
</head>
<body>
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <!-- Conteúdo aqui -->
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/static/js/common.js"></script>
    <script src="/static/js/theme.js"></script>
    <script src="/static/js/pagina.js"></script>
</body>
</html>
{{ end }}
```

#### Sidebar Independente (sidebar.html)
```html
<!-- Para usar em qualquer página, basta incluir este arquivo com: -->
{{ template "sidebar" . }}
```

## 3. Componentes do Design System

### 3.1 Cards

#### Card Padrão
```html
<div class="card-shell">
    <div class="card-header">Título</div>
    <div class="card-body">
        Conteúdo
    </div>
</div>
```

#### Card de Métrica
```html
<div class="stats-card">
    <div class="stats-card-title">Título da Métrica</div>
    <div class="stats-card-value">123</div>
    <div class="stats-card-trend">
        <i class="fas fa-arrow-up trend-up"></i> 5% desde ontem
    </div>
</div>
```

### 3.2 Tabelas
```html
<table class="table-shell">
    <thead>
        <tr>
            <th>Coluna 1</th>
            <th>Coluna 2</th>
        </tr>
    </thead>
    <tbody>
        <!-- Dados -->
    </tbody>
</table>
```

### 3.3 Formulários
```html
<form class="form-shell">
    <div class="form-group">
        <label>Campo</label>
        <input type="text" class="form-control">
    </div>
</form>
```

## 4. Processo de Criação

### 4.1 Estrutura de Arquivos
```
web/
├── static/
│   ├── css/
│   │   ├── common.css      # Estilos comuns
│   │   ├── styles.css      # Estilos globais
│   │   └── [pagina].css    # Estilos específicos
│   ├── js/
│   │   ├── common.js       # Scripts comuns
│   │   ├── theme.js        # Scripts do tema
│   │   └── [pagina].js     # Scripts específicos
│   └── images/             # Imagens e ícones
└── templates/
    ├── layouts/
    │   ├── base_sidemenu.html  # Template completo com sidebar
    │   └── sidebar.html        # Componente sidebar independente
    └── [secao]/
        └── [pagina].html   # Templates das páginas
```

### 4.2 Passos para Criação

1. **Definir a Estrutura**
   - Criar diretórios necessários
   - Definir nome do template
   - Configurar meta tags

2. **Implementar Layout**
   - Incluir sidebar (usando `{{ template "sidebar" . }}`)
   - Definir estrutura de conteúdo
   - Adicionar componentes

3. **Adicionar Estilos**
   - Usar variáveis do design system
   - Implementar responsividade
   - Adicionar animações

4. **Adicionar JavaScript**
   - Inicializar componentes
   - Implementar interatividade
   - Tratar eventos

## 5. Exemplos Práticos

### 5.1 Página de Dashboard
```html
{{ define "dashboard/index.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- Meta tags e recursos -->
</head>
<body>
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1>Dashboard</h1>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="stats-card">
                        <!-- Métrica -->
                    </div>
                </div>
                <!-- Mais cards -->
            </div>
            
            <div class="chart-container">
                <!-- Gráfico -->
            </div>
        </div>
    </div>
</body>
</html>
{{ end }}
```

### 5.2 Página de Listagem
```html
{{ define "ordens/list.html" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- Meta tags e recursos -->
</head>
<body>
    {{ template "sidebar" . }}
    <div class="content-with-sidebar">
        <div class="main-content">
            <div class="page-header">
                <h1>Ordens de Serviço</h1>
                <button class="btn-shell-red">
                    <i class="fas fa-plus"></i> Nova Ordem
                </button>
            </div>
            
            <div class="filter-section">
                <!-- Filtros -->
            </div>
            
            <table class="table-shell">
                <!-- Tabela -->
            </table>
        </div>
    </div>
</body>
</html>
{{ end }}
```

## 6. Boas Práticas

### 6.1 Templates
- Usar blocos para conteúdo dinâmico
- Manter estrutura consistente
- Incluir sidebar em todas as páginas
- Seguir padrão de nomenclatura

### 6.2 CSS
- Usar variáveis do design system
- Manter especificidade baixa
- Seguir a hierarquia de cores
- Implementar estados hover/active

### 6.3 JavaScript
- Usar classes para organização
- Implementar tratamento de erros
- Seguir padrão de eventos
- Manter código modular

## 7. Troubleshooting

### 7.1 Problemas Comuns
1. **Sidebar não aparece**
   - Verificar inclusão do template
   - Confirmar estrutura do HTML

2. **Estilos não aplicados**
   - Verificar caminhos dos arquivos CSS
   - Confirmar ordem de carregamento

3. **JavaScript não funciona**
   - Verificar console por erros
   - Confirmar ordem de carregamento

### 7.2 Soluções
- Usar ferramentas de desenvolvimento
- Verificar logs do servidor
- Consultar documentação do design system 