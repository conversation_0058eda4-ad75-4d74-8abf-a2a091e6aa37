---
type: "manual"
---

# Guia de Segurança e Boas Práticas Frontend

## 1. Segurança Frontend

### 1.1 Proteção contra XSS
```javascript
// Função para sanitizar input
function sanitizeInput(input) {
    return input
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
}

// Exemplo de uso
const userInput = document.getElementById('userInput').value;
const safeInput = sanitizeInput(userInput);
document.getElementById('output').innerHTML = safeInput;
```

### 1.2 Proteção CSRF
```html
<!-- Token CSRF no formulário -->
<form action="/api/data" method="POST">
    <input type="hidden" name="_csrf" value="{{ .CSRFToken }}">
    <!-- Campos do formulário -->
</form>
```

```javascript
// Configuração do Axios com token CSRF
axios.defaults.headers.common['X-CSRF-Token'] = document.querySelector('meta[name="csrf-token"]').content;
```

### 1.3 Content Security Policy
```html
<!-- Meta tag para CSP -->
<meta http-equiv="Content-Security-Policy" 
    content="default-src 'self';
             script-src 'self' 'unsafe-inline' 'unsafe-eval';
             style-src 'self' 'unsafe-inline';
             img-src 'self' data: https:;
             font-src 'self' data:;
             connect-src 'self' https://api.exemplo.com;">
```

### 1.4 Sanitização de Input
```javascript
// Classe para validação de input
class InputValidator {
    static validateEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }

    static validatePassword(password) {
        // Mínimo 8 caracteres, pelo menos uma letra e um número
        const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/;
        return regex.test(password);
    }

    static sanitizeHTML(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }
}
```

## 2. Boas Práticas de Performance

### 2.1 Lazy Loading
```javascript
// Lazy loading de imagens
document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
});
```

### 2.2 Code Splitting
```javascript
// Exemplo de code splitting com import dinâmico
const loadModule = async () => {
    try {
        const module = await import('./heavy-module.js');
        module.doSomething();
    } catch (error) {
        console.error('Erro ao carregar módulo:', error);
    }
};
```

### 2.3 Caching
```javascript
// Exemplo de cache com localStorage
class CacheManager {
    static set(key, value, ttl = 3600000) { // TTL em milissegundos
        const item = {
            value,
            expiry: Date.now() + ttl
        };
        localStorage.setItem(key, JSON.stringify(item));
    }

    static get(key) {
        const item = localStorage.getItem(key);
        if (!item) return null;

        const { value, expiry } = JSON.parse(item);
        if (Date.now() > expiry) {
            localStorage.removeItem(key);
            return null;
        }

        return value;
    }
}
```

## 3. Gerenciamento de Estado

### 3.1 Estado Local
```javascript
// Classe para gerenciamento de estado local
class LocalState {
    constructor(initialState = {}) {
        this.state = initialState;
        this.listeners = new Set();
    }

    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.notifyListeners();
    }

    getState() {
        return this.state;
    }

    subscribe(listener) {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }

    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
}
```

### 3.2 Estado Global
```javascript
// Exemplo de store simples
class Store {
    constructor() {
        this.state = {};
        this.listeners = new Set();
    }

    dispatch(action) {
        this.state = this.reducer(this.state, action);
        this.notifyListeners();
    }

    subscribe(listener) {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }

    getState() {
        return this.state;
    }
}
```

## 4. Tratamento de Erros

### 4.1 Error Boundary
```javascript
// Componente de error boundary
class ErrorBoundary {
    constructor(element) {
        this.element = element;
        this.hasError = false;
    }

    static catchError(error) {
        console.error('Erro capturado:', error);
        // Implementar lógica de fallback
    }

    render() {
        if (this.hasError) {
            return `
                <div class="error-container">
                    <h2>Algo deu errado</h2>
                    <button onclick="window.location.reload()">
                        Tentar novamente
                    </button>
                </div>
            `;
        }
        return this.element.innerHTML;
    }
}
```

### 4.2 Tratamento de Erros em Requisições
```javascript
// Classe para tratamento de erros em requisições
class RequestErrorHandler {
    static async handleRequest(promise) {
        try {
            const response = await promise;
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }

    static handleError(error) {
        // Log do erro
        console.error('Erro na requisição:', error);

        // Notificar usuário
        this.showErrorMessage(error);

        // Reportar para serviço de monitoramento
        this.reportError(error);
    }

    static showErrorMessage(error) {
        // Implementar lógica de exibição de erro
    }

    static reportError(error) {
        // Implementar lógica de report
    }
}
```

## 5. Testes Frontend

### 5.1 Testes Unitários
```javascript
// Exemplo de teste unitário com Jest
describe('InputValidator', () => {
    test('validateEmail deve retornar true para email válido', () => {
        expect(InputValidator.validateEmail('<EMAIL>')).toBe(true);
    });

    test('validatePassword deve retornar true para senha válida', () => {
        expect(InputValidator.validatePassword('Test1234')).toBe(true);
    });
});
```

### 5.2 Testes de Integração
```javascript
// Exemplo de teste de integração com Cypress
describe('Formulário de Login', () => {
    it('deve mostrar erro com credenciais inválidas', () => {
        cy.visit('/login');
        cy.get('#email').type('<EMAIL>');
        cy.get('#password').type('wrongpass');
        cy.get('form').submit();
        cy.get('.error-message').should('be.visible');
    });
});
```

## 6. Checklist de Segurança

### 6.1 Implementação
- [ ] CSP configurado
- [ ] Proteção contra XSS
- [ ] Proteção CSRF
- [ ] Sanitização de input
- [ ] Validação de dados
- [ ] Headers de segurança
- [ ] HTTPS forçado
- [ ] Cookies seguros

### 6.2 Manutenção
- [ ] Atualização regular de dependências
- [ ] Monitoramento de vulnerabilidades
- [ ] Logs de segurança
- [ ] Backup de dados
- [ ] Plano de resposta a incidentes
- [ ] Testes de segurança regulares
- [ ] Auditorias de código
- [ ] Documentação atualizada 