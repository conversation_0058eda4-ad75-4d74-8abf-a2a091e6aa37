---
type: "manual"
---

# Upload de Imagens

## 1. Visão Geral

Esta etapa envolve a implementação do sistema de upload de imagens para logomarcas de prestadoras e avatares de técnicos. Isso inclui:

1. Configuração do diretório de uploads
2. Implementação do serviço de upload
3. Implementação dos handlers para upload
4. Configuração para servir arquivos estáticos

## 2. Configuração do Diretório de Uploads

### 2.1. Estrutura de Diretórios

O sistema de upload utiliza a seguinte estrutura de diretórios:

```
uploads/
├── avatars/    # Fotos de perfil de técnicos
└── logos/      # Logomarcas de prestadoras
```

### 2.2. Permissões de Diretórios

Os diretórios devem ter as seguintes permissões:
- Permissão de leitura para o servidor web
- Permissão de escrita para o aplicativo

### 2.3. Configuração no Servidor

O diretório de uploads deve ser configurado como um diretório estático no servidor web.

## 3. Implementação do Serviço de Upload

### 3.1. Funcionalidades do Serviço

O serviço de upload (`FileUploadService`) implementa as seguintes funcionalidades:

- **Inicialização**:
  - Criar diretórios de uploads se não existirem
  - Configurar caminhos base

- **Upload de Arquivo**:
  - Validar tipo de arquivo
  - Gerar nome único para o arquivo
  - Salvar arquivo no diretório apropriado
  - Retornar URL relativa

- **Exclusão de Arquivo**:
  - Remover arquivo existente
  - Lidar com casos onde o arquivo não existe

### 3.2. Tipos de Upload

O serviço suporta dois tipos de upload:

- **Avatares**: Para fotos de perfil de técnicos
  - Tamanho máximo: 1MB
  - Tipos permitidos: jpg, jpeg, png, gif

- **Logos**: Para logomarcas de prestadoras
  - Tamanho máximo: 2MB
  - Tipos permitidos: jpg, jpeg, png, gif

## 4. Implementação dos Handlers para Upload

### 4.1. Handler para Upload de Logomarca

O handler para upload de logomarca (`UploadProviderLogo`) implementa:

- **Validação**:
  - Verificar se o usuário tem permissão
  - Verificar se o arquivo é válido
  - Verificar tamanho máximo

- **Processamento**:
  - Fazer upload do arquivo
  - Remover logomarca anterior se existir
  - Atualizar URL no banco de dados

- **Resposta**:
  - Retornar URL da nova logomarca
  - Retornar mensagem de sucesso

### 4.2. Handler para Upload de Avatar

O handler para upload de avatar (`UploadUserAvatar`) implementa:

- **Validação**:
  - Verificar se o usuário tem permissão
  - Verificar se o arquivo é válido
  - Verificar tamanho máximo

- **Processamento**:
  - Fazer upload do arquivo
  - Remover avatar anterior se existir
  - Atualizar URL no banco de dados

- **Resposta**:
  - Retornar URL do novo avatar
  - Retornar mensagem de sucesso

## 5. Configuração para Servir Arquivos Estáticos

### 5.1. Configuração no Gin

Configurar o Gin para servir o diretório de uploads como arquivos estáticos:

```go
router.Static("/uploads", "./uploads")
```

### 5.2. URLs de Acesso

As imagens são acessíveis através das seguintes URLs:

- Avatares: `/uploads/avatars/{filename}`
- Logomarcas: `/uploads/logos/{filename}`

## 6. Integração com a Interface de Usuário

### 6.1. Componente de Upload de Logomarca

O componente de upload de logomarca inclui:

- Exibição da logomarca atual
- Botão para selecionar nova imagem
- Prévia da imagem selecionada
- Botão para confirmar upload
- Indicador de progresso
- Mensagens de erro/sucesso

### 6.2. Componente de Upload de Avatar

O componente de upload de avatar inclui:

- Exibição do avatar atual
- Botão para selecionar nova imagem
- Prévia da imagem selecionada
- Botão para confirmar upload
- Indicador de progresso
- Mensagens de erro/sucesso

### 6.3. JavaScript para Upload

O JavaScript para upload implementa:

- Seleção de arquivo
- Validação de tipo e tamanho
- Exibição de prévia
- Envio via AJAX
- Atualização da imagem após upload
- Tratamento de erros

## 7. Verificação da Implementação

Após implementar o sistema de upload de imagens, verifique se:

1. Os diretórios de uploads são criados corretamente
2. Os arquivos são salvos com nomes únicos
3. Os tipos de arquivo são validados corretamente
4. Os tamanhos máximos são respeitados
5. As imagens são acessíveis através das URLs corretas
6. As imagens anteriores são removidas ao fazer upload de novas

## 8. Solução de Problemas Comuns

### 8.1. Erro de Permissão

Se ocorrer um erro de permissão ao fazer upload, verifique:

- Permissões dos diretórios de uploads
- Usuário do processo do servidor
- SELinux ou AppArmor (em sistemas Linux)

### 8.2. Erro de Tipo de Arquivo

Se ocorrer um erro de tipo de arquivo, verifique:

- Extensão do arquivo
- MIME type do arquivo
- Validação no cliente e no servidor

### 8.3. Erro de Tamanho de Arquivo

Se ocorrer um erro de tamanho de arquivo, verifique:

- Configuração de `client_max_body_size` no servidor web
- Configuração de `upload_max_filesize` no PHP (se aplicável)
- Validação no cliente e no servidor

## 9. Próximos Passos

Após concluir a implementação do sistema de upload de imagens, os próximos passos são:

1. Realizar testes e verificação
2. Documentar para usuários finais
