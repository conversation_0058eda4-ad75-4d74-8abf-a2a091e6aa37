---
type: "manual"
---

# Migração para ENT e Atlas

Este documento descreve o processo de migração do banco de dados atual para o ENT e Atlas.

## Visão Geral

O projeto está migrando de uma implementação baseada em GORM para uma implementação baseada em ENT e Atlas. Para isso, é necessário limpar o banco de dados atual, removendo tabelas e estruturas não utilizadas que causam erros durante a migração.

## Pré-requisitos

- PostgreSQL 12+
- Go 1.16+
- Atlas CLI
- Acesso de administrador ao banco de dados

## Processo de Migração

O processo de migração é dividido em várias etapas:

1. **Backup dos Bancos de Dados**
   - Faz backup dos bancos de dados existentes para garantir que os dados possam ser recuperados em caso de problemas.

2. **Análise da Estrutura do Banco de Dados**
   - Analisa a estrutura atual do banco de dados para identificar tabelas, colunas e relacionamentos.
   - Gera relatórios detalhados para referência futura.

3. **Limpeza do Banco de Dados**
   - Cria um novo banco de dados limpo para o ENT.
   - Cria as tabelas necessárias para o ENT com base nos esquemas definidos.
   - Migra os dados relevantes dos bancos de dados existentes para o novo banco.

4. **Verificação da Integridade do Banco de Dados**
   - Verifica se todas as tabelas necessárias foram criadas.
   - Verifica a integridade referencial entre as tabelas.
   - Verifica a contagem de registros para garantir que todos os dados foram migrados corretamente.

5. **Configuração do ENT e Atlas**
   - Configura o ENT para usar o novo banco de dados.
   - Configura o Atlas para gerenciar as migrações do banco de dados.
   - Cria os arquivos de esquema e migração necessários.

6. **Execução de Migrações Atlas**
   - Executa as migrações Atlas para aplicar o esquema ao banco de dados.

## Scripts de Migração

Os seguintes scripts foram criados para automatizar o processo de migração:

- `scripts/backup_databases.sh`: Faz backup dos bancos de dados existentes.
- `scripts/analyze_database_structure.sh`: Analisa a estrutura atual do banco de dados.
- `scripts/clean_database.sh`: Limpa o banco de dados e cria um novo banco para o ENT.
- `scripts/verify_database.sh`: Verifica a integridade do banco de dados após a limpeza.
- `scripts/setup_ent_atlas.sh`: Configura o ENT e Atlas para usar o novo banco de dados.
- `scripts/run_atlas_migrations.sh`: Executa as migrações Atlas.
- `scripts/run_all.sh`: Script principal que executa todo o processo de migração.

## Como Executar a Migração

Para executar a migração, siga os seguintes passos:

1. **Modo Dry Run (Simulação)**
   ```bash
   sudo ./scripts/run_all.sh --dry-run
   ```
   Este comando executará todo o processo em modo de simulação, sem fazer alterações reais no banco de dados.

2. **Migração Real**
   ```bash
   sudo ./scripts/run_all.sh
   ```
   Este comando executará todo o processo de migração, fazendo alterações reais no banco de dados.

## Estrutura do Banco de Dados Limpo

O banco de dados limpo contém as seguintes tabelas:

1. **branches**: Tabela de filiais/postos.
2. **users**: Tabela de usuários do sistema.
3. **equipment**: Tabela de equipamentos.
4. **maintenance_orders**: Tabela de ordens de manutenção.
5. **schema_versions**: Tabela de controle de versões do Atlas.

## Resolução de Problemas

Se você encontrar problemas durante a migração, consulte os logs gerados em `logs/` para obter mais informações sobre o erro.

### Problemas Comuns

1. **Erro de Conexão com o Banco de Dados**
   - Verifique se o PostgreSQL está em execução.
   - Verifique se as credenciais do banco de dados estão corretas no arquivo `.env`.

2. **Erro de Permissão**
   - Verifique se você está executando os scripts com privilégios de administrador (sudo).
   - Verifique se o usuário do PostgreSQL tem permissões suficientes para criar e modificar bancos de dados.

3. **Erro de Migração Atlas**
   - Verifique se o Atlas CLI está instalado corretamente.
   - Verifique se os arquivos de esquema e migração estão corretos.
   - Verifique os logs do Atlas para obter mais informações sobre o erro.

## Próximos Passos

Após a migração, você deve:

1. Atualizar o arquivo `.env` com as credenciais corretas do banco de dados.
2. Executar a aplicação para testar a conexão com o novo banco de dados.
3. Verificar se todas as funcionalidades da aplicação estão funcionando corretamente.

## Rollback

Se você precisar reverter a migração, pode restaurar os backups dos bancos de dados originais:

```bash
sudo -u postgres psql -c "DROP DATABASE IF EXISTS tradicao_ent;"
sudo -u postgres psql -c "CREATE DATABASE tradicao_ent WITH TEMPLATE template0 ENCODING 'UTF8' LC_COLLATE 'C.UTF-8' LC_CTYPE 'C.UTF-8';"
sudo -u postgres psql -d tradicao_ent -f backups/YYYYMMDD/fcobdj_tradicao_backup.sql
```

Substitua `YYYYMMDD` pela data do backup que você deseja restaurar.
