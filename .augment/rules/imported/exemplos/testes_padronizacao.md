---
type: "manual"
---

# Testes Automatizados para Padronização de Nomenclatura

Este documento demonstra como implementar testes automatizados para verificar a conformidade com as convenções de nomenclatura.

## 1. Testes Unitários para Modelos

### Teste de Estrutura do Modelo

```go
package models_test

import (
    "reflect"
    "testing"
    "time"

    "tradicao/internal/models"
)

func TestMaintenanceOrderStructure(t *testing.T) {
    // Obter o tipo do modelo
    orderType := reflect.TypeOf(models.MaintenanceOrder{})
    
    // Mapa de campos esperados com seus tipos
    expectedFields := map[string]string{
        "ID":                "uint",
        "Number":            "string",
        "Title":             "string",
        "Description":       "string",
        "Problem":           "string",
        "Status":            "models.OrderStatus",
        "Priority":          "models.PriorityLevel",
        "BranchID":          "uint",
        "EquipmentID":       "uint",
        "ServiceProviderID": "*uint",
        "TechnicianID":      "*uint",
        "CreatedByUserID":   "uint",
        "DueDate":           "time.Time",
        "OpenDate":          "time.Time",
        "CompletionDate":    "*time.Time",
        "EstimatedCost":     "float64",
        "ActualCost":        "float64",
        "CreatedAt":         "time.Time",
        "UpdatedAt":         "time.Time",
        "DeletedAt":         "*time.Time",
    }
    
    // Verificar se todos os campos esperados existem com os tipos corretos
    for fieldName, expectedType := range expectedFields {
        field, found := orderType.FieldByName(fieldName)
        if !found {
            t.Errorf("Campo esperado %s não encontrado no modelo MaintenanceOrder", fieldName)
            continue
        }
        
        actualType := field.Type.String()
        if !strings.HasSuffix(actualType, expectedType) {
            t.Errorf("Campo %s tem tipo %s, esperado %s", fieldName, actualType, expectedType)
        }
    }
    
    // Verificar se não há campos extras não esperados
    for i := 0; i < orderType.NumField(); i++ {
        field := orderType.Field(i)
        if field.PkgPath != "" {
            // Campo não exportado, ignorar
            continue
        }
        
        if _, exists := expectedFields[field.Name]; !exists {
            // Verificar se é um campo virtual (marcado com gorm:"-")
            if isVirtualField(field) {
                continue
            }
            
            t.Errorf("Campo não esperado %s encontrado no modelo MaintenanceOrder", field.Name)
        }
    }
}

// Verifica se um campo é virtual (marcado com gorm:"-")
func isVirtualField(field reflect.StructField) bool {
    gormTag := field.Tag.Get("gorm")
    return strings.Contains(gormTag, "-")
}

func TestOrderStatusValues(t *testing.T) {
    // Verificar se todos os valores de status estão definidos corretamente
    expectedValues := map[models.OrderStatus]string{
        models.StatusPending:    "pending",
        models.StatusScheduled:  "scheduled",
        models.StatusInProgress: "in_progress",
        models.StatusCompleted:  "completed",
        models.StatusCancelled:  "cancelled",
        models.StatusVerified:   "verified",
        models.StatusRejected:   "rejected",
        models.StatusApproved:   "approved",
    }
    
    for status, expectedValue := range expectedValues {
        if string(status) != expectedValue {
            t.Errorf("Status %s tem valor %s, esperado %s", status, string(status), expectedValue)
        }
    }
}

func TestPriorityLevelValues(t *testing.T) {
    // Verificar se todos os valores de prioridade estão definidos corretamente
    expectedValues := map[models.PriorityLevel]string{
        models.PriorityLow:      "low",
        models.PriorityMedium:   "medium",
        models.PriorityHigh:     "high",
        models.PriorityCritical: "critical",
    }
    
    for priority, expectedValue := range expectedValues {
        if string(priority) != expectedValue {
            t.Errorf("Prioridade %s tem valor %s, esperado %s", priority, string(priority), expectedValue)
        }
    }
}
```

## 2. Testes de Conversão entre Modelos

```go
func TestMaintenanceOrderConversion(t *testing.T) {
    // Criar uma ordem no formato legado
    legacyOrder := &models.OrdemManutencaoExpandida{
        ID:              1,
        Titulo:          "Manutenção Preventiva",
        Descricao:       "Verificação periódica do equipamento",
        FilialID:        2,
        FilialNome:      "Posto Shell Centro",
        EquipamentoID:   3,
        EquipamentoNome: "Bomba de Combustível #1",
        Status:          "pendente",
        Prioridade:      "media",
        SolicitanteID:   4,
        SolicitanteNome: "João Silva",
    }
    
    // Converter para o modelo padronizado
    order := models.FromLegacyOrdem(legacyOrder)
    
    // Verificar se a conversão foi feita corretamente
    if order.ID != legacyOrder.ID {
        t.Errorf("ID incorreto: %d, esperado %d", order.ID, legacyOrder.ID)
    }
    
    if order.Title != legacyOrder.Titulo {
        t.Errorf("Title incorreto: %s, esperado %s", order.Title, legacyOrder.Titulo)
    }
    
    if order.Description != legacyOrder.Descricao {
        t.Errorf("Description incorreto: %s, esperado %s", order.Description, legacyOrder.Descricao)
    }
    
    if order.BranchID != legacyOrder.FilialID {
        t.Errorf("BranchID incorreto: %d, esperado %d", order.BranchID, legacyOrder.FilialID)
    }
    
    if order.BranchName != legacyOrder.FilialNome {
        t.Errorf("BranchName incorreto: %s, esperado %s", order.BranchName, legacyOrder.FilialNome)
    }
    
    if order.EquipmentID != legacyOrder.EquipamentoID {
        t.Errorf("EquipmentID incorreto: %d, esperado %d", order.EquipmentID, legacyOrder.EquipamentoID)
    }
    
    if order.EquipmentName != legacyOrder.EquipamentoNome {
        t.Errorf("EquipmentName incorreto: %s, esperado %s", order.EquipmentName, legacyOrder.EquipamentoNome)
    }
    
    if order.Status != models.StatusPending {
        t.Errorf("Status incorreto: %s, esperado %s", order.Status, models.StatusPending)
    }
    
    if order.Priority != models.PriorityMedium {
        t.Errorf("Priority incorreto: %s, esperado %s", order.Priority, models.PriorityMedium)
    }
    
    if order.CreatedByUserID != legacyOrder.SolicitanteID {
        t.Errorf("CreatedByUserID incorreto: %d, esperado %d", order.CreatedByUserID, legacyOrder.SolicitanteID)
    }
    
    if order.CreatedByName != legacyOrder.SolicitanteNome {
        t.Errorf("CreatedByName incorreto: %s, esperado %s", order.CreatedByName, legacyOrder.SolicitanteNome)
    }
}
```

## 3. Testes de API

```go
func TestMaintenanceOrderAPI(t *testing.T) {
    // Configurar servidor de teste
    router := gin.Default()
    handlers.SetupMaintenanceOrderRoutes(router)
    
    // Criar uma ordem para teste
    order := &models.MaintenanceOrder{
        ID:          1,
        Number:      "ORD-001",
        Title:       "Manutenção Preventiva",
        Description: "Verificação periódica do equipamento",
        Problem:     "Verificação de rotina",
        Status:      models.StatusPending,
        Priority:    models.PriorityMedium,
        BranchID:    2,
        EquipmentID: 3,
        DueDate:     time.Now().AddDate(0, 0, 7),
        OpenDate:    time.Now(),
    }
    
    // Salvar a ordem no banco de dados de teste
    db := database.GetTestDB()
    db.Create(order)
    
    // Fazer uma requisição GET para a API
    req, _ := http.NewRequest("GET", "/api/maintenance-orders/1", nil)
    resp := httptest.NewRecorder()
    router.ServeHTTP(resp, req)
    
    // Verificar se a resposta está no formato correto
    if resp.Code != http.StatusOK {
        t.Errorf("Código de status incorreto: %d, esperado %d", resp.Code, http.StatusOK)
    }
    
    var response map[string]interface{}
    json.Unmarshal(resp.Body.Bytes(), &response)
    
    // Verificar se a resposta tem a estrutura correta
    if success, ok := response["success"].(bool); !ok || !success {
        t.Errorf("Campo 'success' incorreto: %v", response["success"])
    }
    
    data, ok := response["data"].(map[string]interface{})
    if !ok {
        t.Errorf("Campo 'data' não encontrado ou com formato incorreto")
        return
    }
    
    // Verificar se os campos estão no formato correto
    expectedFields := []string{
        "id", "number", "title", "description", "problem", "status", "priority",
        "branch_id", "equipment_id", "due_date", "open_date", "created_at", "updated_at",
    }
    
    for _, field := range expectedFields {
        if _, exists := data[field]; !exists {
            t.Errorf("Campo '%s' não encontrado na resposta", field)
        }
    }
    
    // Verificar valores específicos
    if id, ok := data["id"].(float64); !ok || int(id) != 1 {
        t.Errorf("Campo 'id' incorreto: %v, esperado 1", data["id"])
    }
    
    if title, ok := data["title"].(string); !ok || title != "Manutenção Preventiva" {
        t.Errorf("Campo 'title' incorreto: %v, esperado 'Manutenção Preventiva'", data["title"])
    }
    
    if status, ok := data["status"].(string); !ok || status != "pending" {
        t.Errorf("Campo 'status' incorreto: %v, esperado 'pending'", data["status"])
    }
    
    if priority, ok := data["priority"].(string); !ok || priority != "medium" {
        t.Errorf("Campo 'priority' incorreto: %v, esperado 'medium'", data["priority"])
    }
}
```

## 4. Testes de Frontend

```javascript
describe('API Data Mapping', () => {
    test('mapOrderFromAPI should correctly map API data', () => {
        // Dados simulados da API
        const apiData = {
            id: 1,
            number: 'ORD-001',
            title: 'Manutenção Preventiva',
            description: 'Verificação periódica do equipamento',
            problem: 'Verificação de rotina',
            status: 'pending',
            priority: 'medium',
            branch_id: 2,
            branch_name: 'Posto Shell Centro',
            equipment_id: 3,
            equipment_name: 'Bomba de Combustível #1',
            technician_id: 4,
            technician_name: 'João Silva',
            due_date: '2023-12-31',
            open_date: '2023-12-24',
            completion_date: null,
            estimated_cost: '150.00',
            actual_cost: '0.00',
            created_at: '2023-12-24T10:30:00Z',
            updated_at: '2023-12-24T10:30:00Z'
        };
        
        // Mapear dados da API para o formato do frontend
        const order = mapOrderFromAPI(apiData);
        
        // Verificar se o mapeamento foi feito corretamente
        expect(order.id).toBe(1);
        expect(order.number).toBe('ORD-001');
        expect(order.title).toBe('Manutenção Preventiva');
        expect(order.description).toBe('Verificação periódica do equipamento');
        expect(order.problem).toBe('Verificação de rotina');
        expect(order.status).toBe('pending');
        expect(order.priority).toBe('medium');
        expect(order.branchId).toBe(2);
        expect(order.branchName).toBe('Posto Shell Centro');
        expect(order.equipmentId).toBe(3);
        expect(order.equipmentName).toBe('Bomba de Combustível #1');
        expect(order.technicianId).toBe(4);
        expect(order.technicianName).toBe('João Silva');
        expect(order.dueDate).toEqual(new Date('2023-12-31'));
        expect(order.openDate).toEqual(new Date('2023-12-24'));
        expect(order.completionDate).toBeNull();
        expect(order.estimatedCost).toBe(150.00);
        expect(order.actualCost).toBe(0.00);
        expect(order.createdAt).toEqual(new Date('2023-12-24T10:30:00Z'));
        expect(order.updatedAt).toEqual(new Date('2023-12-24T10:30:00Z'));
    });
    
    test('formatStatus should correctly format status for display', () => {
        expect(formatStatus('pending')).toBe('Pendente');
        expect(formatStatus('scheduled')).toBe('Agendada');
        expect(formatStatus('in_progress')).toBe('Em Andamento');
        expect(formatStatus('completed')).toBe('Concluída');
        expect(formatStatus('cancelled')).toBe('Cancelada');
        expect(formatStatus('verified')).toBe('Verificada');
        expect(formatStatus('rejected')).toBe('Rejeitada');
        expect(formatStatus('approved')).toBe('Aprovada');
        expect(formatStatus('unknown')).toBe('unknown');
    });
    
    test('formatPriority should correctly format priority for display', () => {
        expect(formatPriority('low')).toBe('Baixa');
        expect(formatPriority('medium')).toBe('Média');
        expect(formatPriority('high')).toBe('Alta');
        expect(formatPriority('critical')).toBe('Crítica');
        expect(formatPriority('unknown')).toBe('unknown');
    });
});
```
