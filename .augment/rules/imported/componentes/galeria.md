---
type: "manual"
---

# Galeria de Equipamentos

## Visão Geral
A Galeria de Equipamentos é um componente que permite visualizar, filtrar e obter detalhes sobre os equipamentos cadastrados no sistema. O componente segue os padrões visuais do projeto, utilizando o tema escuro (dark-theme) e os componentes visuais padronizados como cards, spinners e estados vazios.

## Estrutura de Arquivos
- `web/templates/galeria/galeria.html` - Template HTML principal da galeria
- `web/static/css/galeria.css` - Estilos específicos da galeria
- `web/static/js/galeria.js` - Funcionalidades JavaScript da galeria

## Componentes Principais

### 1. Filtros
Permite filtrar os equipamentos por:
- Filial
- Tipo de Equipamento
- Status
- Busca por texto (nome, modelo, número de série)

### 2. Informações da Filial
Exibe detalhes sobre a filial selecionada:
- Nome, endereço, telefone e email
- Estatísticas de equipamentos (total, ativos, em manutenção)

### 3. Visualização de Equipamentos
Oferece duas formas de visualização:
- **Grade (Grid)**: Exibe cards com imagem e informações básicas
- **Lista (Table)**: Exibe uma tabela com informações detalhadas

### 4. Modal de Detalhes
Exibe informações detalhadas sobre o equipamento selecionado:
- Dados técnicos (tipo, modelo, marca, número de série)
- Status e datas importantes
- Observações
- Galeria de mídias relacionadas ao equipamento

## Estilos e Padrões Visuais

### Cards
Os cards seguem o padrão visual do projeto:
- Classe `card-shell` para o container
- Gradiente de fundo escuro
- Bordas arredondadas
- Efeitos de hover com elevação e borda amarela

### Estados de Carregamento e Vazios
- `shell-spinner` para indicadores de carregamento
- `empty-state` para mensagens de "nenhum item encontrado"

### Cores e Temas
- Tema escuro (`dark-theme`) aplicado ao body
- Cores padrão do projeto (shell-yellow, shell-red)
- Gradientes e efeitos de sombra para profundidade

## Responsividade
O layout é totalmente responsivo:
- Grid adaptável com `minmax` para diferentes tamanhos de tela
- Ajustes específicos para dispositivos móveis
- Reorganização de elementos em telas menores

## Como Utilizar

### Inicialização
A galeria é inicializada automaticamente quando a página é carregada. O JavaScript busca os equipamentos da API e preenche a visualização.

### Filtros
1. Selecione uma filial para ver apenas equipamentos daquela unidade
2. Escolha um tipo de equipamento para filtrar por categoria
3. Selecione um status para ver equipamentos ativos, em manutenção ou inativos
4. Use a busca para encontrar equipamentos por nome, modelo ou número de série

### Visualização
- Clique nos ícones de grade/lista para alternar entre os modos de visualização
- Clique em um equipamento para ver detalhes completos no modal

## Integração com a API
A galeria consome os seguintes endpoints:
- `GET /api/equipments` - Lista todos os equipamentos
- `GET /api/equipments/:id` - Obtém detalhes de um equipamento específico
- `GET /api/branches/:id` - Obtém detalhes de uma filial específica

## Problemas Conhecidos e Correções

### Problema: Duplicação na Exibição de Filiais

**Descrição do Problema:**
- A página Galeria exibia listagens duplicadas de filiais para alguns usuários, especialmente técnicos
- Isso ocorria principalmente para o usuário `<EMAIL>` (ID 94)
- As filiais 102 e 105 apareciam duplicadas na lista de seleção

**Causa Raiz:**
- Havia duplicação na tabela `technician_branches` para o técnico com ID 94
- Existiam entradas duplicadas associando este técnico às filiais 102 e 105
- A consulta SQL não usava `DISTINCT` ao buscar as filiais associadas a um técnico

**Solução Implementada:**
1. **Remoção de Duplicações:**
   - Criado script SQL `scripts/remove_duplicate_technician_branches.sql` para remover as duplicações
   - Implementada verificação no script de associação para evitar duplicações futuras
   - Atualizado script para primeiro remover associações existentes antes de inserir novas

2. **Correção da Consulta SQL:**
   - Modificada a consulta no arquivo `filial_filter_service.go`, linha 158, para usar `DISTINCT`:

   ```sql
   -- Antes
   SELECT b.* FROM branches b
   JOIN technician_branches tb ON b.id = tb.branch_id
   WHERE tb.technician_id = $1

   -- Depois
   SELECT DISTINCT b.* FROM branches b
   JOIN technician_branches tb ON b.id = tb.branch_id
   WHERE tb.technician_id = $1
   ```

3. **Correção da Referência à Tabela:**
   - Corrigida a referência à tabela 'user' que não existe no banco de dados:

   ```sql
   -- Antes
   SELECT b.* FROM branches b
   JOIN technician_branches tb ON b.id = tb.branch_id
   JOIN user u ON tb.technician_id = u.id
   WHERE tb.technician_id = $1

   -- Depois
   SELECT b.* FROM branches b
   JOIN technician_branches tb ON b.id = tb.branch_id
   JOIN users u ON tb.technician_id = u.id
   WHERE tb.technician_id = $1
   ```

### Caso Especial: Usuário `<EMAIL>`

**Descrição:**
- Existe um tratamento especial para o usuário com email `<EMAIL>` (ID 94)
- Este usuário deve ter acesso apenas às filiais 102 e 105
- Isso é implementado através de inserções diretas na tabela `technician_branches`

**Implementação:**
- Script `scripts/associate_technician_manual.sql` para associar o técnico às filiais corretas
- O script primeiro remove associações existentes antes de inserir novas para evitar duplicações

```sql
-- Remover associações existentes
DELETE FROM technician_branches WHERE technician_id = 94;

-- Inserir novas associações
INSERT INTO technician_branches (technician_id, branch_id) VALUES (94, 102);
INSERT INTO technician_branches (technician_id, branch_id) VALUES (94, 105);
```

## Como Executar as Correções

1. **Remover Duplicações na Tabela `technician_branches`**
   ```bash
   psql -d  -f scripts/remove_duplicate_technician_branches.sql
   ```

2. **Associar o Técnico às Filiais Corretas**
   ```bash
   psql -d  -f scripts/associate_technician_manual.sql
   ```

3. **Reiniciar o Servidor**
   ```bash
   ./script.sh
   ```

## Recomendações para Manutenção

1. **Verificação Periódica de Duplicações**
   - Implementar uma verificação periódica para detectar e corrigir duplicações na tabela `technician_branches`
   - Isso evitará problemas futuros com a exibição de filiais na página `galeria`

2. **Uso de Constraints no Banco de Dados**
   - Adicionar uma constraint `UNIQUE` na tabela `technician_branches` para os campos `technician_id` e `branch_id`
   - Isso impedirá a inserção de duplicações no nível do banco de dados

3. **Documentação do Caso Especial**
   - Manter documentação clara sobre o caso especial do usuário `<EMAIL>`
   - Explicar por que este usuário tem tratamento especial e como funciona

## Personalização

Para personalizar a galeria:

1. **Adicionar novos filtros**:
   - Adicione novos campos de filtro no HTML
   - Atualize a função de filtro no JavaScript

2. **Modificar o layout dos cards**:
   - Edite a estrutura HTML gerada no JavaScript
   - Ajuste os estilos em `galeria.css`

3. **Adicionar novas funcionalidades**:
   - Implemente novas funções no arquivo `galeria.js`
   - Adicione os elementos de interface correspondentes no HTML
