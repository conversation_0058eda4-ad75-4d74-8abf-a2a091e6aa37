sequenceDiagram
    participant Admin as Administrador
    participant API as API Links
    participant Service as LinkManagementService
    participant DB as Banco de Dados
    
    Note over Admin,DB: Sistema EXISTENTE - Vínculo Técnico-Equipamento
    
    Admin->>API: POST /api/links/technician-equipment
    Note right of Admin: {technician_id, equipment_type_id}
    
    API->>Service: LinkTechnicianToEquipment()
    Service->>DB: Verificar se técnico existe
    Service->>DB: Verificar se tipo equipamento existe
    Service->>DB: Verificar se vínculo já existe
    Service->>DB: Criar vínculo na tabela technician_equipment_types
    
    DB-->>Service: Vínculo criado
    Service-->>API: Sucesso
    API-->>Admin: {"success": true, "message": "Técnico vinculado"}
    
    Note over Admin,DB: APIs Disponíveis:
    Note over Admin,DB: GET /api/links/technician-equipment (listar)
    Note over Admin,DB: POST /api/links/technician-equipment (criar)
    Note over Admin,DB: PUT /api/links/technician-equipment/:id (atualizar)
    Note over Admin,DB: DELETE /api/links/technician-equipment/:id (remover)