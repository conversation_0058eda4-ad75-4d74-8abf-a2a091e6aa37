sequenceDiagram
    participant User as Usuário Filial
    participant UI as Página Criação Ordem
    participant API as API Orders
    participant Service as MaintenanceOrderService
    participant DB as Database
    
    Note over User,DB: Fluxo Atual Mantido (com vínculos simplificados)
    
    User->>UI: Seleciona Equipamento com Defeito
    UI->>UI: Extrai equipment_type_id do equipamento
    
    Note over UI: Carrega prestadoras vinculadas à filial
    UI->>API: GET /api/orders/available-providers?branch_id=X
    API->>DB: SELECT providers WHERE branch_id = X
    DB-->>UI: Lista de prestadoras
    
    Note over UI: Carrega técnicos aptos (filial + tipo equipamento)
    UI->>API: GET /api/orders/available-technicians?branch_id=X&equipment_type_id=Y
    API->>Service: GetAvailableTechnicians(branchID, equipmentTypeID)
    
    Note over Service: Nova consulta com vínculos separados
    Service->>DB: SELECT users u<br/>INNER JOIN technician_branches tb<br/>  ON u.id = tb.technician_id<br/>  AND tb.branch_id = X AND tb.is_active = true<br/>INNER JOIN technician_equipment_types tet<br/>  ON u.id = tet.technician_id<br/>  AND tet.equipment_type_id = Y AND tet.is_active = true
    
    DB-->>Service: Técnicos vinculados à filial E aptos ao equipamento
    Service-->>API: Lista filtrada de técnicos
    API-->>UI: JSON com técnicos qualificados
    
    UI-->>User: Mostra apenas técnicos aptos para o equipamento
    
    Note over User,DB: Resultado: Mesmo comportamento, estrutura mais simples